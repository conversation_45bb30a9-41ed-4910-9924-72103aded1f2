package com.logmanagement.backend.entity;

/**
 * 用户角色枚举
 * 
 * <AUTHOR> Management System
 * @version 1.0.0
 */
public enum UserRole {
    /**
     * 超级管理员 - 拥有所有权限
     */
    SUPER_ADMIN("SUPER_ADMIN", "超级管理员", "拥有系统所有权限"),
    
    /**
     * 管理员 - 可以管理用户和应用
     */
    ADMIN("ADMIN", "管理员", "可以管理用户和应用"),
    
    /**
     * 开发者 - 可以管理自己的应用和查看日志
     */
    DEVELOPER("DEVELOPER", "开发者", "可以管理自己的应用和查看日志"),
    
    /**
     * 观察者 - 只能查看被授权的应用日志
     */
    VIEWER("VIEWER", "观察者", "只能查看被授权的应用日志");

    private final String code;
    private final String name;
    private final String description;

    UserRole(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取用户角色
     * 
     * @param code 角色代码
     * @return 用户角色
     */
    public static UserRole fromCode(String code) {
        for (UserRole role : values()) {
            if (role.code.equalsIgnoreCase(code)) {
                return role;
            }
        }
        throw new IllegalArgumentException("未知的用户角色: " + code);
    }

    /**
     * 判断是否为管理员角色
     * 
     * @return 是否为管理员
     */
    public boolean isAdmin() {
        return this == SUPER_ADMIN || this == ADMIN;
    }

    /**
     * 判断是否可以管理应用
     * 
     * @return 是否可以管理应用
     */
    public boolean canManageApps() {
        return this == SUPER_ADMIN || this == ADMIN || this == DEVELOPER;
    }

    /**
     * 判断是否可以管理用户
     * 
     * @return 是否可以管理用户
     */
    public boolean canManageUsers() {
        return this == SUPER_ADMIN || this == ADMIN;
    }

    @Override
    public String toString() {
        return name;
    }
}
