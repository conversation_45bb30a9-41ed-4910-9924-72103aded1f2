<template>
  <div class="login-container">
    <div class="login-wrapper">
      <!-- 左侧装饰区域 -->
      <div class="login-decoration">
      <div class="decoration-content">
        <div class="brand-section">
          <div class="brand-logo">
            <el-icon size="64" color="white"><Monitor /></el-icon>
          </div>
          <h1 class="brand-title">通用日志管理系统</h1>
          <p class="brand-subtitle">Logger Management System</p>
          <p class="brand-description">
            专业的日志收集、分析与管理平台<br>
            为您的应用提供全方位的日志监控解决方案
          </p>
        </div>

        <!-- 特性展示 -->
        <div class="features">
          <div class="feature-item">
            <el-icon size="20"><Document /></el-icon>
            <span>实时日志收集</span>
          </div>
          <div class="feature-item">
            <el-icon size="20"><Odometer /></el-icon>
            <span>智能数据分析</span>
          </div>
          <div class="feature-item">
            <el-icon size="20"><Grid /></el-icon>
            <span>多应用管理</span>
          </div>
        </div>
      </div>

      <!-- 动态背景元素 -->
      <div class="floating-elements">
        <div class="floating-circle circle-1"></div>
        <div class="floating-circle circle-2"></div>
        <div class="floating-circle circle-3"></div>
        <div class="floating-circle circle-4"></div>
      </div>
    </div>

    <!-- 右侧登录表单 -->
    <div class="login-panel">
      <div class="login-box">
        <div class="login-header">
          <h2>欢迎回来</h2>
          <p>请登录您的账户以继续使用</p>
        </div>

        <el-form
          :model="loginForm"
          :rules="loginRules"
          ref="loginFormRef"
          class="login-form"
          @keyup.enter="handleLogin"
        >
          <el-form-item prop="username">
            <div class="input-wrapper">
              <label class="input-label">用户名</label>
              <el-input
                v-model="loginForm.username"
                placeholder="请输入用户名或邮箱"
                size="large"
                prefix-icon="User"
                class="custom-input"
              />
            </div>
          </el-form-item>

          <el-form-item prop="password">
            <div class="input-wrapper">
              <label class="input-label">密码</label>
              <el-input
                v-model="loginForm.password"
                type="password"
                placeholder="请输入密码"
                size="large"
                prefix-icon="Lock"
                show-password
                class="custom-input"
              />
            </div>
          </el-form-item>

          <el-form-item>
            <div class="login-options">
              <el-checkbox v-model="loginForm.rememberMe" class="remember-checkbox">
                记住我
              </el-checkbox>
              <el-link type="primary" :underline="false" class="forgot-link">
                忘记密码？
              </el-link>
            </div>
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              size="large"
              class="login-button"
              :loading="loginLoading"
              @click="handleLogin"
            >
              <span v-if="!loginLoading">
                <el-icon><Right /></el-icon>
                立即登录
              </span>
              <span v-else>登录中...</span>
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Monitor, Document, Odometer, Grid, Right } from '@element-plus/icons-vue'
import { useAuthStore } from '../stores/auth'
import { Message } from '../utils/message'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

// 响应式数据
const loginLoading = ref(false)
const loginFormRef = ref()

// 登录表单
const loginForm = reactive({
  username: '',
  password: '',
  rememberMe: false
})

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名或邮箱', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ]
}

// 处理登录
const handleLogin = async () => {
  try {
    await loginFormRef.value.validate()
    loginLoading.value = true

    console.log('开始登录流程...')

    // 使用认证store进行登录
    const response = await authStore.login({
      username: loginForm.username,
      password: loginForm.password,
      rememberMe: loginForm.rememberMe
    })

    Message.success('登录成功！欢迎回来')

    // 获取重定向路径
    const redirectPath = route.query.redirect || '/dashboard'

    // 使用 nextTick 确保状态更新完成后再跳转
    await nextTick()
    router.push(redirectPath)

  } catch (error) {
    console.error('登录失败:', error)
    Message.error(error.message || '登录失败，请检查用户名和密码')
  } finally {
    loginLoading.value = false
  }
}

// 组件挂载时检查是否已登录
onMounted(() => {
  console.log('Login组件挂载，检查认证状态...')
  console.log('isAuthenticated:', authStore.isAuthenticated)
  console.log('token:', authStore.token)
  console.log('user:', authStore.user)

  if (authStore.isAuthenticated) {
    const redirectPath = route.query.redirect || '/dashboard'
    console.log('已登录，跳转到:', redirectPath)
    router.push(redirectPath)
  }
})
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
  padding: 20px;
}

.login-wrapper {
  display: flex;
  background: white;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  max-width: 1000px;
  width: 100%;
  min-height: 600px;
}

/* 左侧装饰区域 */
.login-decoration {
  flex: 1;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.decoration-content {
  text-align: center;
  color: white;
  z-index: 10;
  position: relative;
  max-width: 450px;
  padding: 30px;
}

.brand-section {
  margin-bottom: 40px;
}

.brand-logo {
  margin-bottom: 24px;
  animation: logoFloat 3s ease-in-out infinite;
}

.brand-title {
  font-size: 36px;
  font-weight: 700;
  margin: 0 0 10px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  letter-spacing: -0.5px;
}

.brand-subtitle {
  font-size: 16px;
  margin: 0 0 20px 0;
  opacity: 0.9;
  font-weight: 300;
  letter-spacing: 1px;
}

.brand-description {
  font-size: 14px;
  line-height: 1.5;
  opacity: 0.8;
  margin: 0;
}

.features {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.feature-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateX(6px);
}

.feature-item span {
  font-size: 14px;
  font-weight: 500;
}

/* 浮动元素动画 */
.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.floating-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.05);
  animation: float 8s ease-in-out infinite;
}

.circle-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  left: -50px;
  animation-delay: 0s;
}

.circle-2 {
  width: 150px;
  height: 150px;
  top: 50%;
  right: -30px;
  animation-delay: 2s;
}

.circle-3 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

.circle-4 {
  width: 80px;
  height: 80px;
  top: 30%;
  right: 20%;
  animation-delay: 6s;
}

/* 右侧登录面板 */
.login-panel {
  width: 450px;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.login-box {
  width: 100%;
  max-width: 380px;
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.login-header h2 {
  font-size: 28px;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 6px 0;
  letter-spacing: -0.5px;
}

.login-header p {
  font-size: 15px;
  color: #718096;
  margin: 0;
  line-height: 1.4;
}

.login-form {
  margin-bottom: 28px;
}

.input-wrapper {
  margin-bottom: 20px;
}

.input-label {
  display: block;
  font-size: 13px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 6px;
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 28px;
}

.remember-checkbox {
  font-size: 14px;
  color: #6b7280;
}

.forgot-link {
  font-size: 14px;
  font-weight: 500;
}

.login-button {
  width: 100%;
  height: 48px;
  font-size: 15px;
  font-weight: 600;
  border-radius: 10px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.login-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}



/* 动画效果 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-30px) rotate(180deg);
  }
}

@keyframes logoFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* 输入框样式优化 */
:deep(.custom-input .el-input__wrapper) {
  border-radius: 10px;
  border: 2px solid #e5e7eb;
  box-shadow: none;
  transition: all 0.3s ease;
  padding: 10px 14px;
  height: 46px;
}

:deep(.custom-input .el-input__wrapper:hover) {
  border-color: #d1d5db;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

:deep(.custom-input .el-input__wrapper.is-focus) {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

:deep(.custom-input .el-input__inner) {
  font-size: 16px;
  color: #374151;
}

:deep(.custom-input .el-input__prefix) {
  color: #9ca3af;
}

/* 复选框样式 */
:deep(.remember-checkbox .el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: #667eea;
  border-color: #667eea;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .login-wrapper {
    max-width: 500px;
  }

  .login-decoration {
    display: none;
  }

  .login-panel {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .login-container {
    padding: 15px;
  }

  .login-wrapper {
    min-height: auto;
    border-radius: 15px;
  }

  .login-panel {
    padding: 30px 20px;
  }

  .login-box {
    max-width: 100%;
  }

  .login-header h2 {
    font-size: 24px;
  }

  .login-header p {
    font-size: 14px;
  }

  .login-button {
    height: 46px;
    font-size: 15px;
  }
}

@media (max-width: 480px) {
  .login-container {
    padding: 10px;
  }

  .login-wrapper {
    border-radius: 12px;
  }

  .login-panel {
    padding: 25px 15px;
  }

  .login-header {
    margin-bottom: 28px;
  }

  .login-header h2 {
    font-size: 22px;
  }

  .input-wrapper {
    margin-bottom: 18px;
  }

  .login-options {
    margin-bottom: 22px;
  }


}
</style>
