{"LogManagement": {"ApiBaseUrl": "http://localhost:8080/api", "AppId": "app-7ddacc3f3cef4af894ac1dbf4e8dcadf", "ApiKey": "a8bd9f1b91b14928a42e4186a6fe23df"}, "Simulation": {"LogCount": 100, "IntervalSeconds": 1, "BatchSize": 10, "EnableRandomDelay": true, "MaxRandomDelayMs": 500, "LogLevels": ["INFO", "WARN", "ERROR", "DEBUG"], "LogLevelWeights": {"INFO": 50, "WARN": 25, "ERROR": 15, "DEBUG": 10}}, "Logging": {"LogLevel": {"Default": "Debug", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}}