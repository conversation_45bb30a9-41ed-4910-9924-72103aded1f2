<!DOCTYPE html>
<html>
<head>
    <title>时间格式化测试</title>
</head>
<body>
    <h1>时间格式化测试</h1>
    <div id="results"></div>

    <script>
        // 导入时间格式化函数
        const formatTime = (timestamp) => {
            if (!timestamp) return '-'
            
            let date
            if (timestamp instanceof Date) {
                date = timestamp
            } else if (typeof timestamp === 'string') {
                date = new Date(timestamp)
            } else if (typeof timestamp === 'number') {
                date = new Date(timestamp < 10000000000 ? timestamp * 1000 : timestamp)
            } else {
                return '-'
            }
            
            if (isNaN(date.getTime())) {
                return '-'
            }
            
            const year = date.getFullYear()
            const month = String(date.getMonth() + 1).padStart(2, '0')
            const day = String(date.getDate()).padStart(2, '0')
            const hours = String(date.getHours()).padStart(2, '0')
            const minutes = String(date.getMinutes()).padStart(2, '0')
            const seconds = String(date.getSeconds()).padStart(2, '0')
            const milliseconds = String(date.getMilliseconds()).padStart(3, '0')
            
            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}`
        }

        // 测试不同的时间格式
        const testCases = [
            new Date(),
            new Date().getTime(),
            '2024-06-18T13:45:30.123Z',
            '2024-06-18 13:45:30.123',
            '2024-06-18T13:45:30.888Z',
            '2024-06-18 13:45:30.888'
        ]

        const results = document.getElementById('results')
        
        testCases.forEach((testCase, index) => {
            const result = formatTime(testCase)
            const div = document.createElement('div')
            div.innerHTML = `<strong>测试 ${index + 1}:</strong> ${JSON.stringify(testCase)} → ${result}`
            results.appendChild(div)
        })

        // 测试从后端可能返回的格式
        const backendFormat = '2024-06-18T13:45:30.888'
        const backendResult = formatTime(backendFormat)
        const div = document.createElement('div')
        div.innerHTML = `<strong>后端格式测试:</strong> ${backendFormat} → ${backendResult}`
        div.style.color = 'red'
        results.appendChild(div)
    </script>
</body>
</html>
