# 日志管理系统模拟器

这是一个基于 .NET 9 的控制台应用程序，用于模拟应用程序调用日志管理系统API推送日志数据。

## 功能特性

- 🚀 **多种运行模式**: 单次发送、批量发送、连续发送
- 📊 **智能日志生成**: 根据权重随机生成不同级别的日志
- 🔄 **批量处理**: 支持批量发送以提高性能
- 🎯 **真实模拟**: 模拟真实应用场景的日志数据
- 🛠️ **灵活配置**: 通过配置文件自定义各种参数
- 📈 **统计报告**: 提供详细的发送统计信息
- 🔧 **交互模式**: 友好的交互式操作界面
- 🌐 **健康检查**: 自动检测API服务状态

## 快速开始

### 1. 环境要求

- .NET 9.0 SDK
- 日志管理系统API服务运行中

### 2. 配置设置

编辑 `appsettings.json` 文件：

```json
{
  "LogManagement": {
    "ApiBaseUrl": "http://localhost:8080/api",
    "AppId": "your-app-id-here",
    "ApiKey": "your-api-key-here"
  },
  "Simulation": {
    "LogCount": 100,
    "IntervalSeconds": 1,
    "BatchSize": 10
  }
}
```

### 3. 构建和运行

```bash
# 构建项目
dotnet build

# 运行程序（进入交互模式）
dotnet run

# 或者直接执行特定命令
dotnet run -- single
dotnet run -- batch --count 50 --batch-size 5
dotnet run -- continuous --interval 2
```

## 使用方法

### 命令行模式

#### 发送单条日志
```bash
dotnet run -- single
dotnet run -- single --level ERROR --message "自定义错误消息"
```

#### 批量发送日志
```bash
dotnet run -- batch
dotnet run -- batch --count 200 --batch-size 20 --interval 2
```

#### 连续发送日志
```bash
dotnet run -- continuous
dotnet run -- continuous --batch-size 5 --interval 3
```

#### 测试API连接
```bash
dotnet run -- test
```

#### 显示配置信息
```bash
dotnet run -- config
```

### 交互模式

直接运行程序不带参数，将进入交互模式：

```bash
dotnet run
```

然后按照菜单提示选择操作：

```
=== 日志模拟器交互模式 ===

请选择操作:
1. 发送单条日志
2. 批量发送日志
3. 连续发送日志
4. 测试API连接
5. 显示配置信息
6. 退出
请输入选项 (1-6):
```

## 配置说明

### LogManagement 配置

| 参数 | 说明 | 示例值 |
|------|------|--------|
| ApiBaseUrl | API服务地址 | http://localhost:8080/api |
| AppId | 应用程序ID（请求头AppID） | app-7ddacc3f3cef4af894ac1dbf4e8dcadf |
| ApiKey | API密钥（请求头ApiKey） | a8bd9f1b91b14928a42e4186a6fe23df |

### 请求头认证

模拟器会自动在每个API请求中添加以下请求头进行身份认证：

- **AppID**: 应用程序标识符
- **ApiKey**: API访问密钥

这些请求头是调用日志管理API的必要认证信息。

### Simulation 配置

| 参数 | 说明 | 默认值 |
|------|------|--------|
| LogCount | 批量发送的日志总数 | 100 |
| IntervalSeconds | 批次间隔时间(秒) | 1 |
| BatchSize | 每批次发送的日志数量 | 10 |
| EnableRandomDelay | 是否启用随机延迟 | true |
| MaxRandomDelayMs | 最大随机延迟(毫秒) | 500 |
| LogLevels | 支持的日志级别 | ["INFO", "WARN", "ERROR", "DEBUG"] |

### LogLevelWeights 配置

控制不同日志级别的生成权重：

```json
{
  "LogLevelWeights": {
    "INFO": 50,   // 50% 概率生成INFO日志
    "WARN": 25,   // 25% 概率生成WARN日志
    "ERROR": 15,  // 15% 概率生成ERROR日志
    "DEBUG": 10   // 10% 概率生成DEBUG日志
  }
}
```

## 生成的日志数据

模拟器会生成包含以下信息的真实日志数据：

### 基本信息
- **应用程序ID**: 标识日志来源应用
- **日志级别**: INFO、WARN、ERROR、DEBUG
- **时间戳**: UTC时间
- **日志消息**: 根据级别生成的真实业务消息

### 上下文信息
- **用户ID**: 模拟用户标识
- **会话ID**: 模拟用户会话
- **请求ID**: 模拟请求跟踪
- **IP地址**: 模拟客户端IP
- **用户代理**: 模拟浏览器信息
- **日志来源**: 模拟控制器或服务名称

### 扩展数据
- **环境信息**: 运行环境、版本等
- **系统信息**: 进程ID、机器名、线程ID
- **业务数据**: 根据日志级别的特定数据

### 异常信息（ERROR级别）
- **异常类型**: 常见的.NET异常类型
- **异常消息**: 中文异常描述
- **堆栈跟踪**: 模拟的调用堆栈
- **内部异常**: 可能包含的内部异常

## 示例日志消息

### INFO级别
- "用户 user001 成功登录系统"
- "订单 ORD12345 创建成功"
- "用户 user002 查看了产品列表"

### WARN级别
- "用户 user003 登录失败，密码错误"
- "API调用频率过高，来源IP: *************"
- "数据库连接池使用率达到80%"

### ERROR级别
- "数据库连接失败: Connection timeout"
- "支付接口调用异常: Payment gateway error"
- "文件处理失败: file_1234.txt"

### DEBUG级别
- "执行SQL查询: SELECT * FROM users"
- "缓存键值: cache_5678"
- "方法调用: GetUserProfile"

## 性能特性

- **批量发送**: 减少网络请求次数，提高发送效率
- **异步处理**: 使用异步编程模型，提高并发性能
- **错误重试**: 自动处理网络异常和临时错误
- **内存优化**: 及时释放资源，避免内存泄漏
- **可配置延迟**: 模拟真实应用的发送频率

## 故障排除

### 常见问题

1. **API连接失败**
   - 检查API服务是否运行
   - 确认API地址配置正确
   - 检查网络连接

2. **日志发送失败**
   - 查看控制台错误信息
   - 检查API服务日志
   - 确认数据格式正确

3. **性能问题**
   - 调整批次大小
   - 增加发送间隔
   - 检查系统资源使用

### 调试模式

设置日志级别为Debug以获取详细信息：

```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Debug"
    }
  }
}
```

## 扩展开发

### 添加新的日志级别

1. 在配置文件中添加新级别
2. 在 `LogGeneratorService` 中添加消息模板
3. 更新权重配置

### 自定义日志格式

修改 `LogEntry` 模型类，添加新的字段或修改现有字段。

### 集成其他API

修改 `LogApiService` 类，添加对其他API端点的支持。

## 许可证

本项目采用 MIT 许可证。
