package com.logmanagement.backend.controller;

import com.logmanagement.backend.config.PaginationConfig;
import com.logmanagement.backend.dto.ApiResponse;
import com.logmanagement.backend.dto.CreateUserRequest;
import com.logmanagement.backend.dto.UpdateUserRequest;
import com.logmanagement.backend.entity.User;
import com.logmanagement.backend.entity.UserRole;
import com.logmanagement.backend.entity.UserStatus;
import com.logmanagement.backend.repository.UserRepository;
import com.logmanagement.backend.util.JwtUtil;
import io.swagger.annotations.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 用户管理控制器
 *
 * 提供用户管理的完整功能，包括：
 * - 用户账户的创建、查询、更新、删除
 * - 用户密码管理
 * - 用户状态管理（启用/禁用）
 * - 用户权限分配（应用访问权限）
 * - 用户角色管理
 *
 * 权限控制：
 * - 管理员可以管理所有用户
 * - 普通用户只能查看和修改自己的信息
 *
 * 安全特性：
 * - 密码使用BCrypt加密存储
 * - 敏感信息（如密码）在返回时会被清除
 * - 支持密码强度验证
 *
 * <AUTHOR> Management System
 * @version 1.0.0
 */
@Api(tags = "用户管理", description = "用户账户的增删改查、权限管理、密码管理等功能")
@RestController
@RequestMapping("/users")
public class UserController {

    private static final Logger logger = LoggerFactory.getLogger(UserController.class);

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private PaginationConfig paginationConfig;

    /**
     * 获取用户列表（分页）
     *
     * 支持多种过滤条件的分页查询，包括：
     * - 用户状态过滤（ACTIVE、DISABLED）
     * - 用户角色过滤（SUPER_ADMIN、ADMIN、USER）
     * - 关键词搜索（用户名、真实姓名、邮箱）
     *
     * 权限控制：
     * - 管理员可以查看所有用户
     * - 普通用户只能查看自己的信息
     *
     * 安全特性：
     * - 返回的用户信息中密码字段会被清除
     *
     * @param page 页码，从1开始
     * @param size 每页大小
     * @param sort 排序字段
     * @param order 排序方向
     * @param status 用户状态过滤
     * @param role 用户角色过滤
     * @param keyword 关键词搜索
     * @param authHeader JWT认证令牌
     * @return 用户分页结果
     */
    @ApiOperation(
        value = "获取用户列表",
        notes = "分页查询用户列表，支持按状态、角色、关键词过滤。" +
                "管理员可以查看所有用户，普通用户只能查看自己的信息。" +
                "返回的用户信息中密码字段会被清除。"
    )
    @GetMapping
    public ResponseEntity<ApiResponse<Page<User>>> getUsers(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(required = false) Integer size,
            @RequestParam(defaultValue = "createdAt") String sort,
            @RequestParam(defaultValue = "desc") String order,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String role,
            @RequestParam(required = false) String keyword,
            @RequestHeader(value = "Authorization", required = false) String authHeader) {

        // 使用配置的默认页面大小，并验证大小限制
        int validatedSize = size != null ? paginationConfig.validatePageSize(size) : paginationConfig.getDefaultPageSize();

        logger.info("获取用户列表: page={}, size={}, sort={}, order={}, status={}, role={}, keyword={}",
                   page, validatedSize, sort, order, status, role, keyword);

        try {
            String currentUserId = getCurrentUserId(authHeader);
            if (currentUserId == null) {
                return ResponseEntity.ok(ApiResponse.error("未授权访问"));
            }

            // 创建分页参数
            Sort.Direction direction = "desc".equalsIgnoreCase(order) ? Sort.Direction.DESC : Sort.Direction.ASC;
            Pageable pageable = PageRequest.of(page - 1, validatedSize, Sort.by(direction, sort));

            Page<User> users;

            // 检查是否是管理员
            boolean isAdminUser = isAdmin(authHeader);

            if (isAdminUser) {
                // 管理员可以查看所有用户
                if (hasSearchConditions(keyword, role, status)) {
                    users = searchUsers(keyword, role, status, pageable);
                } else {
                    users = userRepository.findAll(pageable);
                }
            } else {
                // 非管理员只能查看自己的信息
                Optional<User> currentUser = userRepository.findById(currentUserId);
                if (currentUser.isPresent()) {
                    List<User> userList = Arrays.asList(currentUser.get());
                    users = new PageImpl<>(userList, pageable, 1);
                } else {
                    users = new PageImpl<>(Collections.emptyList(), pageable, 0);
                }
            }

            // 清除密码字段
            users.getContent().forEach(user -> user.setPassword(null));

            return ResponseEntity.ok(ApiResponse.success("获取用户列表成功", users));

        } catch (Exception e) {
            logger.error("获取用户列表失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取用户列表失败"));
        }
    }

    /**
     * 获取用户详情
     *
     * 根据用户ID获取用户的详细信息
     *
     * 权限控制：
     * - 管理员可以查看所有用户详情
     * - 普通用户只能查看自己的详情
     *
     * @param id 用户ID
     * @param authHeader JWT认证令牌
     * @return 用户详细信息（密码字段已清除）
     */
    @ApiOperation(
        value = "获取用户详情",
        notes = "根据用户ID获取用户的详细信息，包括基本信息、角色、权限等。" +
                "管理员可以查看所有用户，普通用户只能查看自己的信息。" +
                "返回的用户信息中密码字段会被清除。"
    )
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<User>> getUser(
            @ApiParam(value = "用户ID", required = true, example = "user123456")
            @PathVariable String id,
            @ApiParam(value = "JWT认证令牌，格式：Bearer <token>", required = true)
            @RequestHeader(value = "Authorization", required = false) String authHeader) {

        logger.info("获取用户详情: {}", id);

        try {
            String currentUserId = getCurrentUserId(authHeader);
            if (currentUserId == null) {
                return ResponseEntity.ok(ApiResponse.error("未授权访问"));
            }

            // 只有管理员或用户本人可以查看详情
            if (!isAdmin(authHeader) && !currentUserId.equals(id)) {
                return ResponseEntity.ok(ApiResponse.error("无权限访问"));
            }

            Optional<User> userOpt = userRepository.findById(id);
            if (!userOpt.isPresent()) {
                return ResponseEntity.ok(ApiResponse.error("用户不存在"));
            }

            User user = userOpt.get();
            user.setPassword(null); // 清除密码字段

            return ResponseEntity.ok(ApiResponse.success("获取用户详情成功", user));

        } catch (Exception e) {
            logger.error("获取用户详情失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取用户详情失败"));
        }
    }

    /**
     * 创建用户
     *
     * 创建新的用户账户，只有管理员可以执行此操作
     *
     * 权限控制：
     * - 只有管理员可以创建用户
     *
     * 验证规则：
     * - 用户名必须唯一
     * - 邮箱必须唯一
     * - 密码会自动加密存储
     *
     * @param request 创建用户请求信息
     * @param authHeader JWT认证令牌
     * @return 创建成功的用户信息（密码字段已清除）
     */
    @ApiOperation(
        value = "创建用户",
        notes = "创建新的用户账户，只有管理员可以执行此操作。" +
                "用户名和邮箱必须唯一，密码会自动加密存储。" +
                "返回的用户信息中密码字段会被清除。"
    )
    @PostMapping
    public ResponseEntity<ApiResponse<User>> createUser(
            @ApiParam(value = "创建用户请求信息", required = true)
            @Valid @RequestBody CreateUserRequest request,
            @ApiParam(value = "JWT认证令牌，格式：Bearer <token>", required = true)
            @RequestHeader(value = "Authorization", required = false) String authHeader) {

        logger.info("创建用户: {}", request.getUsername());

        try {
            // 验证管理员权限
            if (!isAdmin(authHeader)) {
                return ResponseEntity.ok(ApiResponse.error("无权限操作"));
            }

            // 检查用户名是否已存在
            if (userRepository.findByUsername(request.getUsername()).isPresent()) {
                return ResponseEntity.ok(ApiResponse.error("用户名已存在"));
            }

            // 检查邮箱是否已存在
            if (userRepository.findByEmail(request.getEmail()).isPresent()) {
                return ResponseEntity.ok(ApiResponse.error("邮箱已存在"));
            }

            // 创建用户
            User user = new User();
            user.setId(UUID.randomUUID().toString().replace("-", ""));
            user.setUsername(request.getUsername());
            user.setPassword(passwordEncoder.encode(request.getPassword()));
            user.setEmail(request.getEmail());
            user.setRealName(request.getRealName());
            user.setRole(UserRole.valueOf(request.getRole().toUpperCase()));
            user.setStatus(UserStatus.ACTIVE);
            user.setAuthorizedAppIds(request.getAuthorizedAppIds());
            user.setCreatedAt(LocalDateTime.now());

            User savedUser = userRepository.save(user);
            savedUser.setPassword(null); // 清除密码字段

            logger.info("用户创建成功: {}", savedUser.getUsername());
            return ResponseEntity.ok(ApiResponse.success("用户创建成功", savedUser));

        } catch (Exception e) {
            logger.error("创建用户失败", e);
            return ResponseEntity.ok(ApiResponse.error("创建用户失败"));
        }
    }

    /**
     * 更新用户
     *
     * 更新用户的基本信息，包括邮箱、真实姓名等
     *
     * 权限控制：
     * - 管理员可以更新所有用户信息（包括角色、状态、权限）
     * - 普通用户只能更新自己的基本信息（邮箱、真实姓名、密码）
     *
     * @param id 用户ID
     * @param request 更新用户请求信息
     * @param authHeader JWT认证令牌
     * @return 更新后的用户信息（密码字段已清除）
     */
    @ApiOperation(
        value = "更新用户",
        notes = "更新用户的基本信息。管理员可以更新所有用户的所有信息，" +
                "普通用户只能更新自己的基本信息。邮箱必须唯一。" +
                "返回的用户信息中密码字段会被清除。"
    )
    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<User>> updateUser(
            @ApiParam(value = "用户ID", required = true, example = "user123456")
            @PathVariable String id,
            @ApiParam(value = "更新用户请求信息", required = true)
            @Valid @RequestBody UpdateUserRequest request,
            @ApiParam(value = "JWT认证令牌，格式：Bearer <token>", required = true)
            @RequestHeader(value = "Authorization", required = false) String authHeader) {

        logger.info("更新用户: {}", id);

        try {
            String currentUserId = getCurrentUserId(authHeader);
            if (currentUserId == null) {
                return ResponseEntity.ok(ApiResponse.error("未授权访问"));
            }

            // 只有管理员或用户本人可以更新
            if (!isAdmin(authHeader) && !currentUserId.equals(id)) {
                return ResponseEntity.ok(ApiResponse.error("无权限操作"));
            }

            Optional<User> userOpt = userRepository.findById(id);
            if (!userOpt.isPresent()) {
                return ResponseEntity.ok(ApiResponse.error("用户不存在"));
            }

            User user = userOpt.get();

            // 检查邮箱是否已被其他用户使用
            if (!user.getEmail().equals(request.getEmail())) {
                Optional<User> existingUser = userRepository.findByEmail(request.getEmail());
                if (existingUser.isPresent() && !existingUser.get().getId().equals(id)) {
                    return ResponseEntity.ok(ApiResponse.error("邮箱已存在"));
                }
            }

            // 更新用户信息
            user.setEmail(request.getEmail());
            user.setRealName(request.getRealName());
            
            // 只有管理员可以修改角色和状态
            if (isAdmin(authHeader)) {
                if (request.getRole() != null) {
                    user.setRole(UserRole.valueOf(request.getRole().toUpperCase()));
                }
                if (request.getStatus() != null) {
                    user.setStatus(UserStatus.valueOf(request.getStatus().toUpperCase()));
                }
                user.setAuthorizedAppIds(request.getAuthorizedAppIds());
            }

            // 如果提供了新密码，则更新密码
            if (request.getPassword() != null && !request.getPassword().isEmpty()) {
                user.setPassword(passwordEncoder.encode(request.getPassword()));
            }

            user.setUpdatedAt(LocalDateTime.now());

            User savedUser = userRepository.save(user);
            savedUser.setPassword(null); // 清除密码字段

            logger.info("用户更新成功: {}", savedUser.getUsername());
            return ResponseEntity.ok(ApiResponse.success("用户更新成功", savedUser));

        } catch (Exception e) {
            logger.error("更新用户失败", e);
            return ResponseEntity.ok(ApiResponse.error("更新用户失败"));
        }
    }

    /**
     * 修改密码
     *
     * 用户修改自己的登录密码，需要提供当前密码进行验证
     *
     * 安全特性：
     * - 必须提供当前密码进行验证
     * - 新密码长度不能少于6位
     * - 新密码会自动加密存储
     *
     * @param request 修改密码请求，包含currentPassword和newPassword
     * @param authHeader JWT认证令牌
     * @return 修改结果
     */
    @ApiOperation(
        value = "修改密码",
        notes = "用户修改自己的登录密码，需要提供当前密码进行验证。" +
                "新密码长度不能少于6位，会自动加密存储。" +
                "请求格式：{\"currentPassword\": \"旧密码\", \"newPassword\": \"新密码\"}"
    )
    @PostMapping("/change-password")
    public ResponseEntity<ApiResponse<String>> changePassword(
            @ApiParam(value = "修改密码请求", required = true,
                     example = "{\"currentPassword\": \"oldpass\", \"newPassword\": \"newpass\"}")
            @RequestBody Map<String, String> request,
            @ApiParam(value = "JWT认证令牌，格式：Bearer <token>", required = true)
            @RequestHeader(value = "Authorization", required = false) String authHeader) {

        try {
            // 验证token并获取当前用户
            String token = authHeader != null ? authHeader.replace("Bearer ", "") : null;
            if (token == null || token.isEmpty()) {
                return ResponseEntity.ok(ApiResponse.error("未提供认证token"));
            }

            String currentUserId = jwtUtil.getUserIdFromToken(token);
            if (currentUserId == null) {
                return ResponseEntity.ok(ApiResponse.error("无效的token"));
            }

            String currentPassword = request.get("currentPassword");
            String newPassword = request.get("newPassword");

            if (currentPassword == null || currentPassword.isEmpty()) {
                return ResponseEntity.ok(ApiResponse.error("请输入当前密码"));
            }

            if (newPassword == null || newPassword.isEmpty()) {
                return ResponseEntity.ok(ApiResponse.error("请输入新密码"));
            }

            if (newPassword.length() < 6) {
                return ResponseEntity.ok(ApiResponse.error("新密码长度不能少于6位"));
            }

            // 获取当前用户
            Optional<User> userOpt = userRepository.findById(currentUserId);
            if (!userOpt.isPresent()) {
                return ResponseEntity.ok(ApiResponse.error("用户不存在"));
            }

            User user = userOpt.get();

            // 验证当前密码
            if (!passwordEncoder.matches(currentPassword, user.getPassword())) {
                return ResponseEntity.ok(ApiResponse.error("当前密码不正确"));
            }

            // 更新密码
            user.setPassword(passwordEncoder.encode(newPassword));
            user.setUpdatedAt(LocalDateTime.now());
            userRepository.save(user);

            logger.info("用户 {} 修改密码成功", user.getUsername());
            return ResponseEntity.ok(ApiResponse.success("密码修改成功"));

        } catch (Exception e) {
            logger.error("修改密码失败", e);
            return ResponseEntity.ok(ApiResponse.error("修改密码失败"));
        }
    }

    /**
     * 删除用户
     *
     * 删除指定的用户账户，只有管理员可以执行此操作
     *
     * 权限控制：
     * - 只有管理员可以删除用户
     * - 管理员不能删除自己
     *
     * 警告：删除操作不可逆，请谨慎操作
     *
     * @param id 用户ID
     * @param authHeader JWT认证令牌
     * @return 删除结果
     */
    @ApiOperation(
        value = "删除用户",
        notes = "删除指定的用户账户，只有管理员可以执行此操作。" +
                "管理员不能删除自己。删除操作不可逆，请谨慎操作。"
    )
    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<String>> deleteUser(
            @ApiParam(value = "用户ID", required = true, example = "user123456")
            @PathVariable String id,
            @ApiParam(value = "JWT认证令牌，格式：Bearer <token>", required = true)
            @RequestHeader(value = "Authorization", required = false) String authHeader) {

        logger.info("删除用户: {}", id);

        try {
            // 验证管理员权限
            if (!isAdmin(authHeader)) {
                return ResponseEntity.ok(ApiResponse.error("无权限操作"));
            }

            String currentUserId = getCurrentUserId(authHeader);
            if (currentUserId != null && currentUserId.equals(id)) {
                return ResponseEntity.ok(ApiResponse.error("不能删除自己"));
            }

            Optional<User> userOpt = userRepository.findById(id);
            if (!userOpt.isPresent()) {
                return ResponseEntity.ok(ApiResponse.error("用户不存在"));
            }

            User user = userOpt.get();
            userRepository.delete(user);

            logger.info("用户删除成功: {}", user.getUsername());
            return ResponseEntity.ok(ApiResponse.success("用户删除成功", null));

        } catch (Exception e) {
            logger.error("删除用户失败", e);
            return ResponseEntity.ok(ApiResponse.error("删除用户失败"));
        }
    }

    /**
     * 启用/禁用用户
     *
     * 切换用户的状态（启用/禁用），只有管理员可以执行此操作
     *
     * 权限控制：
     * - 只有管理员可以切换用户状态
     * - 管理员不能修改自己的状态
     *
     * 状态说明：
     * - ACTIVE：用户可以正常登录和使用系统
     * - DISABLED：用户被禁用，无法登录系统
     *
     * @param id 用户ID
     * @param authHeader JWT认证令牌
     * @return 更新后的用户信息（密码字段已清除）
     */
    @ApiOperation(
        value = "启用/禁用用户",
        notes = "切换用户的状态（启用/禁用），只有管理员可以执行此操作。" +
                "管理员不能修改自己的状态。禁用的用户无法登录系统。" +
                "返回的用户信息中密码字段会被清除。"
    )
    @PostMapping("/{id}/toggle-status")
    public ResponseEntity<ApiResponse<User>> toggleUserStatus(
            @ApiParam(value = "用户ID", required = true, example = "user123456")
            @PathVariable String id,
            @ApiParam(value = "JWT认证令牌，格式：Bearer <token>", required = true)
            @RequestHeader(value = "Authorization", required = false) String authHeader) {

        logger.info("切换用户状态: {}", id);

        try {
            // 验证管理员权限
            if (!isAdmin(authHeader)) {
                return ResponseEntity.ok(ApiResponse.error("无权限操作"));
            }

            String currentUserId = getCurrentUserId(authHeader);
            if (currentUserId != null && currentUserId.equals(id)) {
                return ResponseEntity.ok(ApiResponse.error("不能修改自己的状态"));
            }

            Optional<User> userOpt = userRepository.findById(id);
            if (!userOpt.isPresent()) {
                return ResponseEntity.ok(ApiResponse.error("用户不存在"));
            }

            User user = userOpt.get();
            
            // 切换状态
            if (user.getStatus() == UserStatus.ACTIVE) {
                user.setStatus(UserStatus.DISABLED);
            } else {
                user.setStatus(UserStatus.ACTIVE);
            }
            
            user.setUpdatedAt(LocalDateTime.now());

            User savedUser = userRepository.save(user);
            savedUser.setPassword(null); // 清除密码字段

            logger.info("用户状态切换成功: {} -> {}", user.getUsername(), user.getStatus());
            return ResponseEntity.ok(ApiResponse.success("用户状态更新成功", savedUser));

        } catch (Exception e) {
            logger.error("切换用户状态失败", e);
            return ResponseEntity.ok(ApiResponse.error("切换用户状态失败"));
        }
    }

    /**
     * 获取当前用户ID
     */
    private String getCurrentUserId(String authHeader) {
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            return null;
        }

        try {
            String token = authHeader.substring(7);
            return jwtUtil.getUserIdFromToken(token);
        } catch (Exception e) {
            logger.error("获取用户ID失败", e);
            return null;
        }
    }

    /**
     * 检查是否是管理员
     */
    private boolean isAdmin(String authHeader) {
        String currentUserId = getCurrentUserId(authHeader);
        if (currentUserId == null) {
            return false;
        }

        try {
            User user = userRepository.findById(currentUserId).orElse(null);
            return user != null && "SUPER_ADMIN".equals(user.getRole().name());
        } catch (Exception e) {
            logger.error("检查管理员权限失败", e);
            return false;
        }
    }

    /**
     * 检查是否有搜索条件
     */
    private boolean hasSearchConditions(String keyword, String role, String status) {
        return (keyword != null && !keyword.trim().isEmpty()) ||
               (role != null && !role.trim().isEmpty()) ||
               (status != null && !status.trim().isEmpty());
    }

    /**
     * 根据搜索条件查询用户
     */
    private Page<User> searchUsers(String keyword, String role, String status, Pageable pageable) {
        try {
            // 如果只有角色条件
            if ((keyword == null || keyword.trim().isEmpty()) &&
                (status == null || status.trim().isEmpty()) &&
                role != null && !role.trim().isEmpty()) {
                UserRole userRole = UserRole.valueOf(role.toUpperCase());
                return userRepository.findByRole(userRole, pageable);
            }

            // 如果只有状态条件
            if ((keyword == null || keyword.trim().isEmpty()) &&
                (role == null || role.trim().isEmpty()) &&
                status != null && !status.trim().isEmpty()) {
                UserStatus userStatus = UserStatus.valueOf(status.toUpperCase());
                return userRepository.findByStatus(userStatus, pageable);
            }

            // 如果有角色和状态条件
            if ((keyword == null || keyword.trim().isEmpty()) &&
                role != null && !role.trim().isEmpty() &&
                status != null && !status.trim().isEmpty()) {
                UserRole userRole = UserRole.valueOf(role.toUpperCase());
                UserStatus userStatus = UserStatus.valueOf(status.toUpperCase());
                return userRepository.findByRoleAndStatus(userRole, userStatus, pageable);
            }

            // 如果有关键词搜索，需要使用程序化查询
            return searchUsersByKeyword(keyword, role, status, pageable);

        } catch (Exception e) {
            logger.error("搜索用户失败", e);
            // 如果搜索失败，返回所有用户
            return userRepository.findAll(pageable);
        }
    }

    /**
     * 根据关键词搜索用户（程序化查询）
     */
    private Page<User> searchUsersByKeyword(String keyword, String role, String status, Pageable pageable) {
        // 如果没有关键词，使用角色和状态条件查询
        if (keyword == null || keyword.trim().isEmpty()) {
            if (role != null && !role.trim().isEmpty() && status != null && !status.trim().isEmpty()) {
                UserRole userRole = UserRole.valueOf(role.toUpperCase());
                UserStatus userStatus = UserStatus.valueOf(status.toUpperCase());
                return userRepository.findByRoleAndStatus(userRole, userStatus, pageable);
            } else if (role != null && !role.trim().isEmpty()) {
                UserRole userRole = UserRole.valueOf(role.toUpperCase());
                return userRepository.findByRole(userRole, pageable);
            } else if (status != null && !status.trim().isEmpty()) {
                UserStatus userStatus = UserStatus.valueOf(status.toUpperCase());
                return userRepository.findByStatus(userStatus, pageable);
            }
            return userRepository.findAll(pageable);
        }

        // 使用关键词搜索用户名
        // 注意：这里简化处理，只搜索用户名，实际应用中可以扩展为搜索多个字段
        try {
            return userRepository.findByUsernameContainingIgnoreCase(keyword.trim(), pageable);
        } catch (Exception e) {
            logger.error("关键词搜索用户失败", e);
            return userRepository.findAll(pageable);
        }
    }
}
