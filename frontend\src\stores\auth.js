import { defineStore } from 'pinia'
import { authApi, mockAuth<PERSON>pi } from '../api/auth'

export const useAuthStore = defineStore('auth', {
  state: () => ({
    user: null,
    token: localStorage.getItem('token') || null,
    refreshToken: localStorage.getItem('refreshToken') || null,
    permissions: [],
    loading: false
  }),

  getters: {
    // 检查是否已认证
    isAuthenticated: (state) => !!(state.token && state.user),

    // 获取用户角色
    userRole: (state) => state.user?.role || null,

    // 获取用户名
    username: (state) => state.user?.username || null,

    // 获取用户显示名称
    displayName: (state) => state.user?.name || state.user?.username || '未知用户',

    // 检查是否有特定权限
    hasPermission: (state) => (permission) => {
      if (!state.permissions.length) return false
      if (state.permissions.includes('*')) return true
      return state.permissions.includes(permission)
    },

    // 检查是否是管理员
    isAdmin: (state) => state.user?.role === 'SUPER_ADMIN',

    // 检查是否是开发者
    isDeveloper: (state) => ['SUPER_ADMIN', 'DEVELOPER'].includes(state.user?.role)
  },

  actions: {
    // 设置用户信息
    setUser(user) {
      this.user = user
      // 根据用户角色设置权限
      this.permissions = this.getPermissionsByRole(user?.role)
    },

    // 根据角色获取权限
    getPermissionsByRole(role) {
      const rolePermissions = {
        'SUPER_ADMIN': ['*'], // 超级管理员拥有所有权限
        'ADMIN': ['users:read', 'users:write', 'applications:read', 'applications:write', 'logs:read', 'logs:write'],
        'DEVELOPER': ['applications:read', 'applications:write', 'logs:read', 'logs:write'],
        'VIEWER': ['logs:read']
      }
      return rolePermissions[role] || []
    },

    // 设置token
    setToken(token) {
      this.token = token
      if (token) {
        localStorage.setItem('token', token)
      } else {
        localStorage.removeItem('token')
      }
    },

    // 设置刷新token
    setRefreshToken(refreshToken) {
      this.refreshToken = refreshToken
      if (refreshToken) {
        localStorage.setItem('refreshToken', refreshToken)
      } else {
        localStorage.removeItem('refreshToken')
      }
    },

    // 登录
    async login(credentials) {
      try {
        this.loading = true

        // 先清除可能存在的旧token
        this.clearAuth()

        let response
        try {
          // 尝试真实API
          response = await authApi.login(credentials)

          // 处理后端API响应格式
          if (response.code === 200 && response.data) {
            const loginData = response.data
            // 转换为前端期望的格式
            response = {
              user: loginData.userInfo,
              token: loginData.token,
              refreshToken: null, // 后端暂时没有refreshToken
              expiresIn: loginData.expiresIn
            }
          } else {
            console.error('真实API返回错误:', response)
            throw new Error(response.message || '登录失败')
          }
        } catch (error) {
          console.error('真实API登录失败，详细错误:', error)
          console.error('错误类型:', error.constructor.name)
          console.error('错误消息:', error.message)
          console.error('错误堆栈:', error.stack)

          // 暂时禁用模拟API，强制使用真实API
          throw error

          // 如果真实API失败，使用模拟API
          // console.warn('真实API不可用，使用模拟登录:', error.message)
          // response = await mockAuthApi.login(credentials)
        }

        // 保存用户信息和token
        this.setUser(response.user)
        this.setToken(response.token)
        this.setRefreshToken(response.refreshToken)

        return response
      } catch (error) {
        throw error
      } finally {
        this.loading = false
      }
    },

    // 登出
    async logout() {
      try {
        // 尝试调用登出API
        try {
          await authApi.logout()
        } catch (error) {
          // 如果API失败，使用模拟API
          await mockAuthApi.logout()
        }
      } catch (error) {
        console.warn('登出API调用失败:', error.message)
      } finally {
        // 无论API是否成功，都清除本地状态
        this.clearAuth()
      }
    },

    // 清除认证信息
    clearAuth() {
      this.user = null
      this.token = null
      this.refreshToken = null
      this.permissions = []

      localStorage.removeItem('token')
      localStorage.removeItem('refreshToken')
      localStorage.removeItem('userInfo')
    },

    // 获取当前用户信息
    async getCurrentUser() {
      if (!this.token) {
        throw new Error('未登录')
      }

      try {
        const response = await authApi.getCurrentUser()

        // 处理后端API响应格式
        if (response.code === 200 && response.data) {
          const user = response.data
          this.setUser(user)
          return user
        } else {
          console.error('获取用户信息失败:', response)
          throw new Error(response.message || '获取用户信息失败')
        }
      } catch (error) {
        console.error('获取用户信息异常:', error)
        // 如果获取用户信息失败，清除认证状态
        this.clearAuth()
        throw error
      }
    },

    // 验证token
    async validateToken() {
      const token = this.token || localStorage.getItem('token')
      if (!token) {
        return false
      }

      try {
        let result
        try {
          result = await authApi.validateToken()
        } catch (error) {
          console.warn('真实API验证token失败，使用模拟API:', error.message)
          result = await mockAuthApi.validateToken()
        }

        // 处理不同的响应格式
        if (result.data && typeof result.data.valid === 'boolean') {
          return result.data.valid
        } else if (typeof result.valid === 'boolean') {
          return result.valid
        } else {
          return false
        }
      } catch (error) {
        console.warn('Token验证失败:', error.message)
        this.clearAuth()
        return false
      }
    },

    // 刷新token
    async refreshAuthToken() {
      if (!this.refreshToken) {
        throw new Error('无刷新token')
      }

      try {
        const response = await authApi.refreshToken()
        this.setToken(response.token)
        this.setRefreshToken(response.refreshToken)
        return response
      } catch (error) {
        this.clearAuth()
        throw error
      }
    },

    // 修改密码
    async changePassword(passwordData) {
      try {
        let result
        try {
          result = await authApi.changePassword(passwordData)
        } catch (error) {
          result = await mockAuthApi.changePassword(passwordData)
        }

        return result
      } catch (error) {
        throw error
      }
    },

    // 初始化认证状态
    async initAuth() {
      const token = localStorage.getItem('token')
      const refreshToken = localStorage.getItem('refreshToken')
      const userInfo = localStorage.getItem('userInfo')

      if (token && userInfo) {
        try {
          // 先设置token到store中
          this.setToken(token)
          if (refreshToken) {
            this.setRefreshToken(refreshToken)
          }

          // 验证token是否有效
          const isValid = await this.validateToken()

          if (isValid) {
            // 获取最新的用户信息
            await this.getCurrentUser()
          } else {
            this.clearAuth()
          }
        } catch (error) {
          console.warn('初始化认证状态失败:', error.message)
          this.clearAuth()
        }
      }
    }
  }
})
