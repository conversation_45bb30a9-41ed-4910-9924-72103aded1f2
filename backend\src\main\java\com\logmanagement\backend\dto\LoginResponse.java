package com.logmanagement.backend.dto;

import com.logmanagement.backend.entity.User;
import com.logmanagement.backend.entity.UserRole;
import java.util.List;

/**
 * 登录响应DTO
 * 
 * <AUTHOR> Management System
 * @version 1.0.0
 */
public class LoginResponse {

    /**
     * 访问令牌
     */
    private String token;

    /**
     * 令牌类型
     */
    private String tokenType = "Bearer";

    /**
     * 令牌过期时间（秒）
     */
    private long expiresIn;

    /**
     * 用户信息
     */
    private UserInfo userInfo;

    // 构造函数
    public LoginResponse() {
    }

    public LoginResponse(String token, long expiresIn, User user) {
        this.token = token;
        this.expiresIn = expiresIn;
        this.userInfo = new UserInfo(user);
    }

    // Getter 和 Setter 方法
    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getTokenType() {
        return tokenType;
    }

    public void setTokenType(String tokenType) {
        this.tokenType = tokenType;
    }

    public long getExpiresIn() {
        return expiresIn;
    }

    public void setExpiresIn(long expiresIn) {
        this.expiresIn = expiresIn;
    }

    public UserInfo getUserInfo() {
        return userInfo;
    }

    public void setUserInfo(UserInfo userInfo) {
        this.userInfo = userInfo;
    }

    /**
     * 用户信息内部类
     */
    public static class UserInfo {
        private String id;
        private String username;
        private String email;
        private String realName;
        private UserRole role;
        private List<String> authorizedAppIds;

        public UserInfo() {
        }

        public UserInfo(User user) {
            this.id = user.getId();
            this.username = user.getUsername();
            this.email = user.getEmail();
            this.realName = user.getRealName();
            this.role = user.getRole();
            this.authorizedAppIds = user.getAuthorizedAppIds();
        }

        // Getter 和 Setter 方法
        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }

        public String getRealName() {
            return realName;
        }

        public void setRealName(String realName) {
            this.realName = realName;
        }

        public UserRole getRole() {
            return role;
        }

        public void setRole(UserRole role) {
            this.role = role;
        }

        public List<String> getAuthorizedAppIds() {
            return authorizedAppIds;
        }

        public void setAuthorizedAppIds(List<String> authorizedAppIds) {
            this.authorizedAppIds = authorizedAppIds;
        }
    }

    @Override
    public String toString() {
        return "LoginResponse{" +
                "tokenType='" + tokenType + '\'' +
                ", expiresIn=" + expiresIn +
                ", userInfo=" + userInfo +
                '}';
    }
}
