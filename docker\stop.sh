#!/bin/bash

# 通用日志管理系统 Docker 停止脚本
# 作者: Logger Management System
# 版本: 1.0.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 停止服务
stop_services() {
    log_info "停止所有服务..."
    
    if [ "$1" = "--remove-volumes" ]; then
        log_warning "将删除所有数据卷，这将清除所有数据！"
        read -p "确定要继续吗？(y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            docker-compose down -v
            log_success "服务已停止，数据卷已删除"
        else
            log_info "操作已取消"
            exit 0
        fi
    else
        docker-compose down
        log_success "服务已停止"
    fi
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --remove-volumes    停止服务并删除所有数据卷（谨慎使用）"
    echo "  --help             显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                 # 停止服务但保留数据"
    echo "  $0 --remove-volumes # 停止服务并删除所有数据"
}

# 主函数
main() {
    case "$1" in
        --help)
            show_help
            ;;
        --remove-volumes)
            stop_services --remove-volumes
            ;;
        "")
            stop_services
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
