package com.logmanagement.backend.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 通用API响应DTO
 *
 * 系统中所有API接口的统一响应格式，包含：
 * - 响应状态码
 * - 响应消息
 * - 响应数据
 * - 时间戳
 *
 * 常用状态码：
 * - 200: 操作成功
 * - 400: 请求参数错误
 * - 401: 未授权
 * - 404: 资源不存在
 * - 500: 服务器内部错误
 *
 * <AUTHOR> Management System
 * @version 1.0.0
 */
@ApiModel(description = "通用API响应格式")
public class ApiResponse<T> {

    /**
     * 响应码
     */
    @ApiModelProperty(value = "响应状态码", example = "200", notes = "200-成功, 400-参数错误, 401-未授权, 404-不存在, 500-服务器错误")
    private int code;

    /**
     * 响应消息
     */
    @ApiModelProperty(value = "响应消息", example = "操作成功")
    private String message;

    /**
     * 响应数据
     */
    @ApiModelProperty(value = "响应数据", notes = "具体的业务数据，类型根据接口而定")
    private T data;

    /**
     * 时间戳
     */
    @ApiModelProperty(value = "响应时间戳", example = "1640995200000")
    private long timestamp;

    // 构造函数
    public ApiResponse() {
        this.timestamp = System.currentTimeMillis();
    }

    public ApiResponse(int code, String message) {
        this();
        this.code = code;
        this.message = message;
    }

    public ApiResponse(int code, String message, T data) {
        this(code, message);
        this.data = data;
    }

    // 静态方法 - 成功响应
    public static <T> ApiResponse<T> success() {
        return new ApiResponse<>(200, "操作成功");
    }

    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<>(200, "操作成功", data);
    }

    public static <T> ApiResponse<T> success(String message, T data) {
        return new ApiResponse<>(200, message, data);
    }

    // 静态方法 - 失败响应
    public static <T> ApiResponse<T> error() {
        return new ApiResponse<>(500, "操作失败");
    }

    public static <T> ApiResponse<T> error(String message) {
        return new ApiResponse<>(500, message);
    }

    public static <T> ApiResponse<T> error(int code, String message) {
        return new ApiResponse<>(code, message);
    }

    // 静态方法 - 参数错误
    public static <T> ApiResponse<T> badRequest(String message) {
        return new ApiResponse<>(400, message);
    }

    // 静态方法 - 未找到
    public static <T> ApiResponse<T> notFound(String message) {
        return new ApiResponse<>(404, message);
    }

    // Getter 和 Setter 方法
    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    @Override
    public String toString() {
        return "ApiResponse{" +
                "code=" + code +
                ", message='" + message + '\'' +
                ", data=" + data +
                ", timestamp=" + timestamp +
                '}';
    }
}
