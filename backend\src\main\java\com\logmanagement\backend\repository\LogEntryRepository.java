package com.logmanagement.backend.repository;

import com.logmanagement.backend.entity.LogEntry;
import com.logmanagement.backend.entity.LogLevel;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 日志条目仓库接口
 * 
 * <AUTHOR> Management System
 * @version 1.0.0
 */
@Repository
public interface LogEntryRepository extends MongoRepository<LogEntry, String> {

    /**
     * 根据日志级别查找日志
     * 
     * @param level 日志级别
     * @param pageable 分页参数
     * @return 日志分页结果
     */
    Page<LogEntry> findByLevel(LogLevel level, Pageable pageable);

    /**
     * 根据日志来源查找日志
     *
     * @param source 日志来源
     * @param pageable 分页参数
     * @return 日志分页结果
     */
    Page<LogEntry> findBySource(String source, Pageable pageable);

    /**
     * 根据环境查找日志
     *
     * @param environment 环境
     * @param pageable 分页参数
     * @return 日志分页结果
     */
    Page<LogEntry> findByEnvironment(String environment, Pageable pageable);

    /**
     * 根据应用ID和环境查找日志
     *
     * @param applicationId 应用ID
     * @param environment 环境
     * @param pageable 分页参数
     * @return 日志分页结果
     */
    Page<LogEntry> findByApplicationIdAndEnvironment(String applicationId, String environment, Pageable pageable);

    /**
     * 根据扩展属性搜索日志（使用正则表达式）
     * 注意：这个方法在MongoDB中可能性能较低，建议在应用层进行过滤
     *
     * @param regex 正则表达式
     * @param pageable 分页参数
     * @return 日志分页结果
     */
    @Query("{ 'extendProperties': { $regex: ?0, $options: 'i' } }")
    Page<LogEntry> findByExtendPropertiesRegex(String regex, Pageable pageable);

    /**
     * 根据环境属性搜索日志（使用正则表达式）
     * 注意：这个方法在MongoDB中可能性能较低，建议在应用层进行过滤
     *
     * @param regex 正则表达式
     * @param pageable 分页参数
     * @return 日志分页结果
     */
    @Query("{ 'environmentProperties': { $regex: ?0, $options: 'i' } }")
    Page<LogEntry> findByEnvironmentPropertiesRegex(String regex, Pageable pageable);

    /**
     * 根据时间范围查找日志
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageable 分页参数
     * @return 日志分页结果
     */
    Page<LogEntry> findByTimestampBetween(LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);

    /**
     * 根据日志级别统计数量
     * 
     * @param level 日志级别
     * @return 数量
     */
    long countByLevel(LogLevel level);

    /**
     * 根据时间范围统计数量
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 数量
     */
    long countByTimestampBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取所有不同的日志来源
     * 
     * @return 日志来源列表
     */
    @Query(value = "{}", fields = "{ 'source' : 1 }")
    List<LogEntry> findDistinctSources();

    /**
     * 根据消息内容搜索日志
     * 
     * @param keyword 关键词
     * @param pageable 分页参数
     * @return 日志分页结果
     */
    @Query("{ 'message': { $regex: ?0, $options: 'i' } }")
    Page<LogEntry> findByMessageContainingIgnoreCase(String keyword, Pageable pageable);

    /**
     * 复合条件查询日志
     *
     * @param level 日志级别（可选）
     * @param source 日志来源（可选）
     * @param applicationName 应用名称（可选）
     * @param environment 环境（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param keyword 关键词（可选）
     * @param pageable 分页参数
     * @return 日志分页结果
     */
    // 删除复杂的查询，改用程序化查询
    // @Query("...")
    // Page<LogEntry> findByComplexConditions(LogLevel level, String source, String applicationName, String environment,
    //                                       LocalDateTime startTime, LocalDateTime endTime,
    //                                       String keyword, Pageable pageable);

    /**
     * 删除指定时间之前的日志
     * 
     * @param timestamp 时间戳
     * @return 删除的数量
     */
    long deleteByTimestampBefore(LocalDateTime timestamp);

    /**
     * 根据日志级别和时间范围统计数量
     * 
     * @param level 日志级别
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 数量
     */
    long countByLevelAndTimestampBetween(LogLevel level, LocalDateTime startTime, LocalDateTime endTime);
}
