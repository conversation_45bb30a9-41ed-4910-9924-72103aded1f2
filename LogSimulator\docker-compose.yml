version: '3.8'

services:
  log-simulator:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: log-simulator
    environment:
      # 日志管理API配置
      - LogManagement__ApiBaseUrl=http://host.docker.internal:8080/api
      - LogManagement__ApplicationId=log-simulator-docker
      - LogManagement__ApplicationName=日志模拟器Docker版
      - LogManagement__Environment=docker
      
      # 模拟配置
      - Simulation__LogCount=50
      - Simulation__BatchSize=5
      - Simulation__IntervalSeconds=2
      
      # 日志配置
      - Logging__LogLevel__Default=Information
    
    # 网络配置 - 允许访问宿主机服务
    extra_hosts:
      - "host.docker.internal:host-gateway"
    
    # 交互模式需要TTY
    tty: true
    stdin_open: true
    
    # 重启策略
    restart: "no"
    
    # 资源限制
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.25'

  # 批量模式示例
  log-simulator-batch:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: log-simulator-batch
    environment:
      - LogManagement__ApiBaseUrl=http://host.docker.internal:8080/api
      - LogManagement__ApplicationId=log-simulator-batch
      - LogManagement__ApplicationName=批量日志模拟器
      - LogManagement__Environment=docker
    extra_hosts:
      - "host.docker.internal:host-gateway"
    command: ["batch", "--count", "100", "--batch-size", "10"]
    restart: "no"
    profiles:
      - batch

  # 连续模式示例
  log-simulator-continuous:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: log-simulator-continuous
    environment:
      - LogManagement__ApiBaseUrl=http://host.docker.internal:8080/api
      - LogManagement__ApplicationId=log-simulator-continuous
      - LogManagement__ApplicationName=连续日志模拟器
      - LogManagement__Environment=docker
    extra_hosts:
      - "host.docker.internal:host-gateway"
    command: ["continuous", "--batch-size", "5", "--interval", "3"]
    restart: unless-stopped
    profiles:
      - continuous

# 使用说明:
# 
# 1. 构建镜像:
#    docker-compose build
#
# 2. 运行交互模式:
#    docker-compose run --rm log-simulator
#
# 3. 运行批量模式:
#    docker-compose --profile batch up log-simulator-batch
#
# 4. 运行连续模式:
#    docker-compose --profile continuous up -d log-simulator-continuous
#
# 5. 运行单次测试:
#    docker-compose run --rm log-simulator single
#
# 6. 测试API连接:
#    docker-compose run --rm log-simulator test
#
# 7. 查看配置:
#    docker-compose run --rm log-simulator config
#
# 8. 自定义批量发送:
#    docker-compose run --rm log-simulator batch --count 200 --batch-size 20
#
# 9. 停止连续模式:
#    docker-compose --profile continuous down
