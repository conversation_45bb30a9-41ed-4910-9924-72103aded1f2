@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: 通用日志管理系统 - Windows源码打包脚本
:: 作者: Logger Management System
:: 版本: 1.0.0

echo ==========================================
echo 通用日志管理系统 - 源码打包脚本
echo ==========================================

:: 设置变量
set PROJECT_NAME=logger-management
set TIMESTAMP=%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set TIMESTAMP=%TIMESTAMP: =0%
set PACKAGE_NAME=%PROJECT_NAME%-%TIMESTAMP%.zip
set TEMP_DIR=%TEMP%\%PROJECT_NAME%-package
set EXCLUDE_FILE=%~dp0package-exclude.txt

echo [INFO] 开始打包源码...
echo [INFO] 包名: %PACKAGE_NAME%
echo [INFO] 临时目录: %TEMP_DIR%

:: 清理临时目录
if exist "%TEMP_DIR%" (
    echo [INFO] 清理临时目录...
    rmdir /s /q "%TEMP_DIR%"
)

:: 创建临时目录
mkdir "%TEMP_DIR%"

:: 复制源码到临时目录
echo [INFO] 复制源码文件...
xcopy "%~dp0.." "%TEMP_DIR%\" /E /I /Q /Y

:: 删除不需要的文件和目录
echo [INFO] 清理不必要的文件...

:: 删除构建产物
if exist "%TEMP_DIR%\backend\target" rmdir /s /q "%TEMP_DIR%\backend\target"
if exist "%TEMP_DIR%\frontend\dist" rmdir /s /q "%TEMP_DIR%\frontend\dist"
if exist "%TEMP_DIR%\frontend\node_modules" rmdir /s /q "%TEMP_DIR%\frontend\node_modules"

:: 删除IDE文件
if exist "%TEMP_DIR%\.idea" rmdir /s /q "%TEMP_DIR%\.idea"
if exist "%TEMP_DIR%\.vscode" rmdir /s /q "%TEMP_DIR%\.vscode"
if exist "%TEMP_DIR%\.eclipse" rmdir /s /q "%TEMP_DIR%\.eclipse"

:: 删除系统文件
del /f /q "%TEMP_DIR%\Thumbs.db" 2>nul
del /f /q "%TEMP_DIR%\Desktop.ini" 2>nul
del /f /q "%TEMP_DIR%\.DS_Store" 2>nul

:: 删除日志文件
del /f /q "%TEMP_DIR%\*.log" 2>nul
if exist "%TEMP_DIR%\logs" rmdir /s /q "%TEMP_DIR%\logs"

:: 删除临时文件
del /f /q "%TEMP_DIR%\*.tmp" 2>nul
del /f /q "%TEMP_DIR%\*.temp" 2>nul

:: 删除备份文件
del /f /q "%TEMP_DIR%\*.bak" 2>nul
del /f /q "%TEMP_DIR%\*.backup" 2>nul

:: 删除Git文件（如果不需要版本历史）
if exist "%TEMP_DIR%\.git" (
    echo [INFO] 删除Git历史文件...
    rmdir /s /q "%TEMP_DIR%\.git"
)

:: 创建部署说明文件
echo [INFO] 创建部署说明文件...
(
echo # 通用日志管理系统 - Linux部署包
echo.
echo ## 包信息
echo - 打包时间: %date% %time%
echo - 包名: %PACKAGE_NAME%
echo - 源系统: Windows
echo - 目标系统: Linux Docker
echo.
echo ## 部署步骤
echo 1. 解压此包到Linux服务器
echo 2. 进入项目目录
echo 3. 执行部署脚本: ./scripts/deploy-on-linux.sh
echo.
echo ## 目录结构
echo - backend/     后端源码
echo - frontend/    前端源码
echo - docker/      Docker配置文件
echo - scripts/     部署脚本
echo.
echo ## 注意事项
echo - 确保Linux服务器已安装Docker和Docker Compose
echo - 根据需要修改docker/.env配置文件
echo - 首次部署可能需要较长时间下载依赖
) > "%TEMP_DIR%\DEPLOYMENT_README.md"

:: 检查是否安装了7-Zip或WinRAR
set ZIP_CMD=
if exist "C:\Program Files\7-Zip\7z.exe" (
    set ZIP_CMD="C:\Program Files\7-Zip\7z.exe" a -tzip
) else if exist "C:\Program Files (x86)\7-Zip\7z.exe" (
    set ZIP_CMD="C:\Program Files (x86)\7-Zip\7z.exe" a -tzip
) else if exist "C:\Program Files\WinRAR\WinRAR.exe" (
    set ZIP_CMD="C:\Program Files\WinRAR\WinRAR.exe" a -afzip
) else (
    echo [WARNING] 未找到7-Zip或WinRAR，使用PowerShell压缩...
    set USE_POWERSHELL=1
)

:: 创建压缩包
echo [INFO] 创建压缩包...
if defined USE_POWERSHELL (
    powershell -Command "Compress-Archive -Path '%TEMP_DIR%\*' -DestinationPath '%~dp0%PACKAGE_NAME%' -Force"
) else (
    %ZIP_CMD% "%~dp0%PACKAGE_NAME%" "%TEMP_DIR%\*"
)

if errorlevel 1 (
    echo [ERROR] 压缩失败
    goto :cleanup
)

:: 清理临时目录
:cleanup
echo [INFO] 清理临时文件...
if exist "%TEMP_DIR%" rmdir /s /q "%TEMP_DIR%"

:: 显示结果
if exist "%~dp0%PACKAGE_NAME%" (
    echo [SUCCESS] 打包完成！
    echo [INFO] 包文件: %~dp0%PACKAGE_NAME%
    echo [INFO] 文件大小: 
    for %%A in ("%~dp0%PACKAGE_NAME%") do echo   %%~zA 字节
    echo.
    echo ==========================================
    echo 下一步操作:
    echo 1. 将 %PACKAGE_NAME% 传输到Linux服务器
    echo 2. 在Linux服务器上解压: unzip %PACKAGE_NAME%
    echo 3. 进入项目目录执行部署脚本
    echo ==========================================
) else (
    echo [ERROR] 打包失败，请检查错误信息
)

pause
