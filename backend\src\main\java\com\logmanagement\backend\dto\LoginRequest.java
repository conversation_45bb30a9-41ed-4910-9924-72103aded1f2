package com.logmanagement.backend.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;

/**
 * 登录请求DTO
 *
 * 用于用户登录时传递认证信息
 *
 * <AUTHOR> Management System
 * @version 1.0.0
 */
@ApiModel(description = "用户登录请求信息")
public class LoginRequest {

    /**
     * 用户名或邮箱
     */
    @ApiModelProperty(value = "用户名或邮箱", required = true, example = "admin")
    @NotBlank(message = "用户名不能为空")
    private String username;

    /**
     * 密码
     */
    @ApiModelProperty(value = "用户密码", required = true, example = "123456")
    @NotBlank(message = "密码不能为空")
    private String password;

    /**
     * 记住我
     */
    @ApiModelProperty(value = "是否记住登录状态", example = "false")
    private boolean rememberMe = false;

    // 构造函数
    public LoginRequest() {
    }

    public LoginRequest(String username, String password) {
        this.username = username;
        this.password = password;
    }

    // Getter 和 Setter 方法
    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public boolean isRememberMe() {
        return rememberMe;
    }

    public void setRememberMe(boolean rememberMe) {
        this.rememberMe = rememberMe;
    }

    @Override
    public String toString() {
        return "LoginRequest{" +
                "username='" + username + '\'' +
                ", rememberMe=" + rememberMe +
                '}';
    }
}
