# 通用日志管理系统 - 跨平台部署脚本说明

## 概述

本目录包含了从Windows源码到Linux Docker环境的完整跨平台部署解决方案。

## 文件结构

```
scripts/
├── README.md                      # 本说明文件
├── deploy-config.example.txt      # 部署配置模板
├── package-source.bat            # Windows源码打包脚本
├── transfer-to-linux.bat         # 文件传输脚本
├── deploy-remote.bat             # 一键远程部署脚本
├── deploy-on-linux.sh            # Linux自动化部署脚本
└── setup-linux-server.sh         # Linux服务器环境设置脚本
```

## 使用方法

### 快速开始（推荐）

1. **配置部署参数**
```cmd
copy deploy-config.example.txt deploy-config.txt
notepad deploy-config.txt
```

2. **一键部署**
```cmd
deploy-remote.bat
```

### 分步部署

#### 1. 准备Linux服务器
```bash
# 在Linux服务器上执行
curl -O https://raw.githubusercontent.com/your-repo/scripts/setup-linux-server.sh
chmod +x setup-linux-server.sh
./setup-linux-server.sh
```

#### 2. 打包源码
```cmd
# 在Windows上执行
package-source.bat
```

#### 3. 传输文件
```cmd
transfer-to-linux.bat
```

#### 4. 远程部署
```bash
# SSH到Linux服务器
ssh username@server-ip
cd /home/<USER>/logger-management
unzip logger-management-*.zip
chmod +x scripts/deploy-on-linux.sh
./scripts/deploy-on-linux.sh
```

## 脚本详细说明

### package-source.bat
**功能**: 打包Windows源码为部署包
**特点**:
- 自动排除不必要文件（node_modules、target、.git等）
- 支持多种压缩工具（7-Zip、WinRAR、PowerShell）
- 生成带时间戳的压缩包
- 创建部署说明文件

**使用**:
```cmd
package-source.bat
```

### transfer-to-linux.bat
**功能**: 将源码包传输到Linux服务器
**特点**:
- 支持多种传输工具（PuTTY pscp、OpenSSH scp）
- 支持SSH密钥和密码认证
- 自动检测配置文件
- 可选自动解压和部署

**使用**:
```cmd
transfer-to-linux.bat
```

### deploy-remote.bat
**功能**: 一键完成打包、传输、部署流程
**特点**:
- 集成所有部署步骤
- 自动创建配置文件模板
- 错误处理和状态反馈
- 显示部署结果信息

**使用**:
```cmd
deploy-remote.bat
```

### deploy-on-linux.sh
**功能**: Linux服务器上的自动化部署脚本
**特点**:
- 系统要求检查
- 项目结构验证
- 支持完整部署和外部MongoDB模式
- 环境变量配置
- 服务健康检查

**使用**:
```bash
chmod +x deploy-on-linux.sh
./deploy-on-linux.sh
```

### setup-linux-server.sh
**功能**: Linux服务器环境初始化
**特点**:
- 自动检测操作系统
- 安装Docker和Docker Compose
- 配置防火墙规则
- 系统优化设置
- 创建部署目录

**使用**:
```bash
chmod +x setup-linux-server.sh
./setup-linux-server.sh
```

## 配置文件说明

### deploy-config.txt
主要配置项：

```ini
# 服务器连接
SERVER_HOST=*************
SERVER_USER=ubuntu
SERVER_PORT=22
DEPLOY_PATH=/home/<USER>/logger-management

# SSH认证
SSH_KEY_PATH=C:\Users\<USER>\.ssh\id_rsa

# 应用配置
FRONTEND_PORT=80
BACKEND_PORT=8080
USE_EXTERNAL_MONGODB=false

# 外部MongoDB配置
EXTERNAL_MONGODB_HOST=mongodb-server
EXTERNAL_MONGODB_PORT=27017
EXTERNAL_MONGODB_DATABASE=logger_management
EXTERNAL_MONGODB_USERNAME=app_user
EXTERNAL_MONGODB_PASSWORD=secure_password
```

## 部署模式

### 完整部署模式
- 包含MongoDB容器
- 适合测试和开发环境
- 一键启动所有服务

### 外部MongoDB模式
- 连接现有MongoDB服务器
- 适合生产环境
- 更好的数据管理和高可用性

## 网络要求

### Windows端
- 能够SSH连接到Linux服务器
- 网络传输工具（PuTTY或OpenSSH）

### Linux端
- 开放端口：22(SSH)、80(前端)、8080(后端)、8081(MongoDB管理)
- Docker和Docker Compose环境
- 互联网连接（下载镜像）

## 安全建议

1. **使用SSH密钥认证**
2. **配置防火墙规则**
3. **定期更新系统和Docker**
4. **使用强密码和密钥**
5. **限制网络访问范围**

## 故障排除

### 常见问题

1. **传输失败**
   - 检查网络连接
   - 验证SSH配置
   - 确认目标目录权限

2. **部署失败**
   - 查看Docker日志
   - 检查系统资源
   - 验证配置文件

3. **服务无法访问**
   - 检查防火墙设置
   - 验证端口配置
   - 确认服务状态

### 日志查看

```bash
# 查看部署日志
cat /var/log/deploy.log

# 查看Docker日志
docker-compose logs -f

# 查看系统日志
sudo journalctl -u docker
```

## 维护操作

### 更新部署
```bash
# 停止服务
docker-compose down

# 更新代码
unzip -o new-package.zip

# 重新部署
./scripts/deploy-on-linux.sh
```

### 备份数据
```bash
# 备份MongoDB
docker-compose exec mongodb mongodump --out /backup

# 备份配置
tar -czf config-backup.tar.gz docker/.env
```

### 监控服务
```bash
# 查看容器状态
docker-compose ps

# 查看资源使用
docker stats

# 健康检查
curl http://localhost:8080/api/actuator/health
```

## 技术支持

如遇到问题，请：
1. 查看相关日志文件
2. 检查配置文件设置
3. 验证网络连接
4. 确认系统资源

---

**版本**: 1.0.0  
**更新时间**: 2024-06-20  
**作者**: Logger Management System
