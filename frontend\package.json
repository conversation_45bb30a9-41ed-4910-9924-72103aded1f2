{"name": "logger-management-frontend", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.10.0", "element-plus": "^2.10.2", "pinia": "^2.1.7", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"@tsconfig/node22": "^22.0.1", "@types/node": "^22.14.0", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "npm-run-all2": "^7.0.2", "typescript": "~5.8.0", "vite": "^6.3.5", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.8"}}