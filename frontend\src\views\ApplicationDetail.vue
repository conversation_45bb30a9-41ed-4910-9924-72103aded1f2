<template>
  <div class="application-detail">
    <!-- 页面头部 -->
    <div class="page-header">
      <el-page-header @back="goBack" title="返回应用列表">
        <template #content>
          <span class="page-title">应用详情</span>
        </template>
      </el-page-header>
    </div>

    <!-- 应用信息卡片 -->
    <el-card class="app-info-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>应用信息</span>
        </div>
      </template>
      
      <el-descriptions :column="2" border>
        <el-descriptions-item label="应用名称">
          {{ application.name }}
        </el-descriptions-item>
        <el-descriptions-item label="应用ID">
          <div style="display: flex; align-items: center; gap: 8px;">
            <code style="background-color: #f5f7fa; padding: 2px 6px; border-radius: 4px; font-size: 12px; color: #606266;">
              {{ application.id }}
            </code>
            <el-button
              type="primary"
              size="small"
              @click="copyApplicationId"
              style="padding: 4px 8px; font-size: 12px;"
            >
              <el-icon><CopyDocument /></el-icon>
              复制
            </el-button>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="应用状态">
          <el-tag :type="getStatusTagType(application.status)">
            {{ getStatusText(application.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="环境">
          <el-tag v-if="application.environment" :type="getEnvTagType(application.environment)">
            {{ getEnvText(application.environment) }}
          </el-tag>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="版本">
          {{ application.version || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="创建者">
          {{ application.creatorUsername }}
        </el-descriptions-item>
        <el-descriptions-item label="日志保留天数">
          {{ application.logRetentionDays }} 天
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ formatTime(application.createdAt) }}
        </el-descriptions-item>
        <el-descriptions-item label="最后活跃">
          {{ application.lastActiveTime ? formatTime(application.lastActiveTime) : '从未活跃' }}
        </el-descriptions-item>
        <el-descriptions-item label="应用描述" :span="2">
          {{ application.description || '暂无描述' }}
        </el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 令牌信息卡片 -->
    <el-card class="token-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>API 令牌</span>
          <el-button type="primary" @click="copyToken">
            <el-icon><CopyDocument /></el-icon>
            复制令牌
          </el-button>
        </div>
      </template>
      
      <div class="token-content">
        <el-alert
          title="请妥善保管应用令牌"
          type="warning"
          description="令牌用于应用推送日志时的身份验证，请勿泄露给他人。"
          show-icon
          :closable="false"
        />
        
        <div class="token-display">
          <el-input
            v-model="application.token"
            readonly
            type="textarea"
            :rows="2"
            placeholder="令牌"
          />
        </div>
        
        <div class="usage-example">
          <h4>使用示例：</h4>
          <pre class="code-block">
# 推送日志示例
curl -X POST http://localhost:8080/api/logs \
  -H "X-App-ID: {{ application.id }}" \
  -H "X-Api-Key: {{ application.token }}" \
  -H "Content-Type: application/json" \
  -d '{
    "level": "INFO",
    "message": "这是一条测试日志",
    "source": "{{ application.name }}",
    "timestamp": "2024-01-01T12:00:00"
  }'
          </pre>
        </div>
      </div>
    </el-card>

    <!-- 日志统计卡片 -->
    <el-card class="stats-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>日志统计</span>
          <el-button type="primary" @click="viewLogs">
            <el-icon><View /></el-icon>
            查看日志
          </el-button>
        </div>
      </template>
      
      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-number">{{ stats.total }}</div>
          <div class="stat-label">总日志数</div>
        </div>
        <div class="stat-item error">
          <div class="stat-number">{{ stats.errorCount }}</div>
          <div class="stat-label">错误日志</div>
        </div>
        <div class="stat-item warning">
          <div class="stat-number">{{ stats.warnCount }}</div>
          <div class="stat-label">警告日志</div>
        </div>
        <div class="stat-item success">
          <div class="stat-number">{{ stats.todayCount }}</div>
          <div class="stat-label">今日日志</div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { CopyDocument, View } from '@element-plus/icons-vue'
import { formatTime } from '../utils/dateTime'
import { applicationApi } from '../api/applications'
import { logApi } from '../api/logs'
import { Message } from '../utils/message'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const application = ref({
  id: '',
  name: '',
  description: '',
  status: 'ACTIVE',
  environment: '',
  version: '',
  token: '',
  creatorUsername: '',
  logRetentionDays: 30,
  createdAt: '',
  lastActiveTime: ''
})

const stats = reactive({
  total: 0,
  errorCount: 0,
  warnCount: 0,
  infoCount: 0,
  debugCount: 0,
  todayCount: 0
})

// 获取状态标签类型
const getStatusTagType = (status) => {
  const typeMap = {
    ACTIVE: 'success',
    DISABLED: 'danger',
    MAINTENANCE: 'warning'
  }
  return typeMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const textMap = {
    ACTIVE: '激活',
    DISABLED: '禁用',
    MAINTENANCE: '维护'
  }
  return textMap[status] || status
}

// 获取环境标签类型
const getEnvTagType = (env) => {
  const typeMap = {
    dev: 'warning',
    test: 'info',
    staging: 'primary',
    prod: 'success'
  }
  return typeMap[env] || 'info'
}

// 获取环境文本
const getEnvText = (env) => {
  const textMap = {
    dev: '开发',
    test: '测试',
    staging: '模拟',
    prod: '生产'
  }
  return textMap[env] || env
}



// 返回上一页
const goBack = () => {
  router.push('/applications')
}



// 复制令牌
const copyToken = async () => {
  await copyToClipboard(application.value.token)
}

// 复制应用ID
const copyApplicationId = async () => {
  await copyToClipboard(application.value.id, '应用ID已复制到剪贴板')
}

// 通用复制到剪贴板函数（带降级方案）
const copyToClipboard = async (text, successMessage = '令牌已复制到剪贴板') => {
  try {
    // 首先尝试使用现代的 Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text)
      Message.success(successMessage)
      return
    }

    // 降级方案：使用传统的 document.execCommand
    const textArea = document.createElement('textarea')
    textArea.value = text
    textArea.style.position = 'fixed'
    textArea.style.left = '-999999px'
    textArea.style.top = '-999999px'
    document.body.appendChild(textArea)
    textArea.focus()
    textArea.select()

    const successful = document.execCommand('copy')
    document.body.removeChild(textArea)

    if (successful) {
      Message.success(successMessage)
    } else {
      throw new Error('execCommand failed')
    }
  } catch (error) {
    console.error('复制失败:', error)
    Message.error('复制失败，请手动选择并复制内容')
  }
}

// 查看日志
const viewLogs = () => {
  router.push(`/logs?appId=${application.value.id}`)
}

// 加载应用详情
const loadApplicationDetail = async () => {
  try {
    loading.value = true
    const appId = route.params.id

    if (!appId) {
      Message.error('应用ID不存在')
      router.push('/applications')
      return
    }

    // 调用真实的API获取应用详情
    const response = await applicationApi.getApplication(appId)

    if (response.code === 200 && response.data) {
      application.value = response.data

      // 加载应用的日志统计
      await loadApplicationStats(appId)
    } else {
      throw new Error(response.message || '应用不存在')
    }
  } catch (error) {
    console.error('加载应用详情失败:', error)
    Message.error('加载应用详情失败: ' + (error.message || '请检查后端服务是否正常运行'))
    // 如果应用不存在，返回应用列表
    if (error.message && error.message.includes('应用不存在')) {
      router.push('/applications')
    }
  } finally {
    loading.value = false
  }
}

// 加载应用日志统计
const loadApplicationStats = async (appId) => {
  try {
    console.log('加载应用日志统计:', appId)

    const response = await logApi.getApplicationLogStats(appId)
    console.log('应用日志统计API响应:', response)

    if (response.code === 200 && response.data) {
      // 更新统计数据
      Object.assign(stats, {
        total: response.data.total || 0,
        errorCount: response.data.errorCount || 0,
        warnCount: response.data.warnCount || 0,
        infoCount: response.data.infoCount || 0,
        debugCount: response.data.debugCount || 0,
        todayCount: response.data.todayCount || 0
      })
      console.log('应用日志统计加载成功:', stats)
    } else {
      console.warn('获取应用日志统计失败:', response.message)
      // 如果获取统计失败，保持默认值0
    }
  } catch (error) {
    console.error('加载应用日志统计失败:', error)
    // 如果加载失败，保持默认值0，不显示错误消息
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadApplicationDetail()
})
</script>

<style scoped>
.application-detail {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
  max-width: 1600px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 20px;
}

.page-title {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.app-info-card,
.token-card,
.stats-card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #303133;
}



.token-content {
  padding: 10px 0;
}

.token-display {
  margin: 20px 0;
}

.usage-example {
  margin-top: 20px;
}

.usage-example h4 {
  margin-bottom: 10px;
  color: #303133;
}

.code-block {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.5;
  overflow-x: auto;
  border: 1px solid #e4e7ed;
  color: #2c3e50;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  padding: 20px 0;
}

.stat-item {
  text-align: center;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.stat-item.error {
  border-left-color: #f56c6c;
}

.stat-item.warning {
  border-left-color: #e6a23c;
}

.stat-item.success {
  border-left-color: #67c23a;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .application-detail {
    padding: 10px;
  }
  

  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
  }
  
  .stat-item {
    padding: 15px;
  }
  
  .stat-number {
    font-size: 24px;
  }
}
</style>
