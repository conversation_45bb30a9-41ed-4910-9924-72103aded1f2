package com.logmanagement.backend.entity;

/**
 * 用户状态枚举
 * 
 * <AUTHOR> Management System
 * @version 1.0.0
 */
public enum UserStatus {
    /**
     * 激活状态
     */
    ACTIVE("ACTIVE", "激活", "用户账户正常可用"),
    
    /**
     * 禁用状态
     */
    DISABLED("DISABLED", "禁用", "用户账户被禁用"),
    
    /**
     * 锁定状态
     */
    LOCKED("LOCKED", "锁定", "用户账户被锁定"),
    
    /**
     * 待激活状态
     */
    PENDING("PENDING", "待激活", "用户账户等待激活");

    private final String code;
    private final String name;
    private final String description;

    UserStatus(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取用户状态
     * 
     * @param code 状态代码
     * @return 用户状态
     */
    public static UserStatus fromCode(String code) {
        for (UserStatus status : values()) {
            if (status.code.equalsIgnoreCase(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的用户状态: " + code);
    }

    /**
     * 判断用户是否可用
     * 
     * @return 是否可用
     */
    public boolean isAvailable() {
        return this == ACTIVE;
    }

    @Override
    public String toString() {
        return name;
    }
}
