package com.logmanagement.backend.entity;

/**
 * 应用状态枚举
 * 
 * <AUTHOR> Management System
 * @version 1.0.0
 */
public enum ApplicationStatus {
    /**
     * 激活状态
     */
    ACTIVE("ACTIVE", "激活", "应用正常运行"),
    
    /**
     * 禁用状态
     */
    DISABLED("DISABLED", "禁用", "应用被禁用"),
    
    /**
     * 维护状态
     */
    MAINTENANCE("MAINTENANCE", "维护", "应用正在维护"),
    
    /**
     * 已删除状态
     */
    DELETED("DELETED", "已删除", "应用已被删除");

    private final String code;
    private final String name;
    private final String description;

    ApplicationStatus(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取应用状态
     * 
     * @param code 状态代码
     * @return 应用状态
     */
    public static ApplicationStatus fromCode(String code) {
        for (ApplicationStatus status : values()) {
            if (status.code.equalsIgnoreCase(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的应用状态: " + code);
    }

    /**
     * 判断应用是否可用
     * 
     * @return 是否可用
     */
    public boolean isAvailable() {
        return this == ACTIVE;
    }

    /**
     * 判断应用是否可以接收日志
     * 
     * @return 是否可以接收日志
     */
    public boolean canReceiveLogs() {
        return this == ACTIVE || this == MAINTENANCE;
    }

    @Override
    public String toString() {
        return name;
    }
}
