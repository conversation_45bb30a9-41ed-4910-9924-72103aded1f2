package com.logmanagement.backend.dto;

/**
 * 日志统计响应DTO
 * 
 * <AUTHOR> Management System
 * @version 1.0.0
 */
public class LogStatsResponse {

    /**
     * 总日志数
     */
    private long total;

    /**
     * 错误日志数
     */
    private long errorCount;

    /**
     * 警告日志数
     */
    private long warnCount;

    /**
     * 信息日志数
     */
    private long infoCount;

    /**
     * 调试日志数
     */
    private long debugCount;

    /**
     * 今日日志数
     */
    private long todayCount;

    /**
     * 最近一小时日志数
     */
    private long recentHourCount;

    // 构造函数
    public LogStatsResponse() {
    }

    public LogStatsResponse(long total, long errorCount, long warnCount, 
                           long infoCount, long debugCount, long todayCount, 
                           long recentHourCount) {
        this.total = total;
        this.errorCount = errorCount;
        this.warnCount = warnCount;
        this.infoCount = infoCount;
        this.debugCount = debugCount;
        this.todayCount = todayCount;
        this.recentHourCount = recentHourCount;
    }

    // Getter 和 Setter 方法
    public long getTotal() {
        return total;
    }

    public void setTotal(long total) {
        this.total = total;
    }

    public long getErrorCount() {
        return errorCount;
    }

    public void setErrorCount(long errorCount) {
        this.errorCount = errorCount;
    }

    public long getWarnCount() {
        return warnCount;
    }

    public void setWarnCount(long warnCount) {
        this.warnCount = warnCount;
    }

    public long getInfoCount() {
        return infoCount;
    }

    public void setInfoCount(long infoCount) {
        this.infoCount = infoCount;
    }

    public long getDebugCount() {
        return debugCount;
    }

    public void setDebugCount(long debugCount) {
        this.debugCount = debugCount;
    }

    public long getTodayCount() {
        return todayCount;
    }

    public void setTodayCount(long todayCount) {
        this.todayCount = todayCount;
    }

    public long getRecentHourCount() {
        return recentHourCount;
    }

    public void setRecentHourCount(long recentHourCount) {
        this.recentHourCount = recentHourCount;
    }

    @Override
    public String toString() {
        return "LogStatsResponse{" +
                "total=" + total +
                ", errorCount=" + errorCount +
                ", warnCount=" + warnCount +
                ", infoCount=" + infoCount +
                ", debugCount=" + debugCount +
                ", todayCount=" + todayCount +
                ", recentHourCount=" + recentHourCount +
                '}';
    }
}
