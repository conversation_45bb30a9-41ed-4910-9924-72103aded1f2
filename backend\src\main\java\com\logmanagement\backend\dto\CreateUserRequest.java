package com.logmanagement.backend.dto;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 创建用户请求DTO
 * 
 * <AUTHOR> Management System
 * @version 1.0.0
 */
public class CreateUserRequest {

    /**
     * 用户名
     */
    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 50, message = "用户名长度必须在3-50个字符之间")
    private String username;

    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 100, message = "密码长度必须在6-100个字符之间")
    private String password;

    /**
     * 邮箱
     */
    @NotBlank(message = "邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    private String email;

    /**
     * 真实姓名
     */
    @NotBlank(message = "真实姓名不能为空")
    @Size(max = 100, message = "真实姓名长度不能超过100个字符")
    private String realName;

    /**
     * 用户角色
     */
    @NotBlank(message = "用户角色不能为空")
    private String role;

    /**
     * 授权的应用ID列表
     */
    private List<String> authorizedAppIds;

    // 构造函数
    public CreateUserRequest() {
    }

    public CreateUserRequest(String username, String password, String email, String realName, String role) {
        this.username = username;
        this.password = password;
        this.email = email;
        this.realName = realName;
        this.role = role;
    }

    // Getter 和 Setter 方法
    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public List<String> getAuthorizedAppIds() {
        return authorizedAppIds;
    }

    public void setAuthorizedAppIds(List<String> authorizedAppIds) {
        this.authorizedAppIds = authorizedAppIds;
    }

    @Override
    public String toString() {
        return "CreateUserRequest{" +
                "username='" + username + '\'' +
                ", email='" + email + '\'' +
                ", realName='" + realName + '\'' +
                ", role='" + role + '\'' +
                ", authorizedAppIds=" + authorizedAppIds +
                '}';
    }
}
