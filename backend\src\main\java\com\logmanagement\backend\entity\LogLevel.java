package com.logmanagement.backend.entity;

/**
 * 日志级别枚举
 * 
 * <AUTHOR> Management System
 * @version 1.0.0
 */
public enum LogLevel {
    /**
     * 错误级别
     */
    ERROR("ERROR", 1, "错误"),
    
    /**
     * 警告级别
     */
    WARN("WARN", 2, "警告"),
    
    /**
     * 信息级别
     */
    INFO("INFO", 3, "信息"),
    
    /**
     * 调试级别
     */
    DEBUG("DEBUG", 4, "调试");

    private final String code;
    private final int priority;
    private final String description;

    LogLevel(String code, int priority, String description) {
        this.code = code;
        this.priority = priority;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public int getPriority() {
        return priority;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取日志级别
     * 
     * @param code 级别代码
     * @return 日志级别
     */
    public static LogLevel fromCode(String code) {
        for (LogLevel level : values()) {
            if (level.code.equalsIgnoreCase(code)) {
                return level;
            }
        }
        throw new IllegalArgumentException("未知的日志级别: " + code);
    }

    /**
     * 判断当前级别是否高于或等于指定级别
     * 
     * @param other 其他级别
     * @return 是否高于或等于
     */
    public boolean isHigherOrEqualTo(LogLevel other) {
        return this.priority <= other.priority;
    }

    @Override
    public String toString() {
        return code;
    }
}
