import { defineStore } from 'pinia'
import { useRouter } from 'vue-router'

export const useTabStore = defineStore('tabs', {
  state: () => ({
    // 当前激活的tab
    activeTab: '/dashboard',
    // 所有打开的tabs
    tabs: [
      {
        name: '/dashboard',
        title: '仪表板',
        icon: 'Odometer',
        closable: false, // 仪表板不可关闭
        component: 'Dashboard'
      }
    ]
  }),

  getters: {
    // 获取当前激活的tab信息
    currentTab: (state) => {
      return state.tabs.find(tab => tab.name === state.activeTab)
    },

    // 获取可关闭的tabs
    closableTabs: (state) => {
      return state.tabs.filter(tab => tab.closable)
    }
  },

  actions: {
    // 添加新的tab
    addTab(tabInfo) {
      const { path, title, icon, component } = tabInfo
      
      // 检查tab是否已存在
      const existingTab = this.tabs.find(tab => tab.name === path)
      if (existingTab) {
        // 如果已存在，直接激活
        this.activeTab = path
        return
      }

      // 添加新tab
      const newTab = {
        name: path,
        title,
        icon,
        closable: path !== '/dashboard', // 仪表板不可关闭
        component
      }

      this.tabs.push(newTab)
      this.activeTab = path
    },

    // 关闭tab
    closeTab(tabName) {
      const tabIndex = this.tabs.findIndex(tab => tab.name === tabName)
      if (tabIndex === -1) return

      const tab = this.tabs[tabIndex]
      if (!tab.closable) return // 不可关闭的tab不能关闭

      // 如果关闭的是当前激活的tab，需要切换到其他tab
      if (this.activeTab === tabName) {
        // 优先切换到右边的tab，如果没有则切换到左边的tab
        if (tabIndex < this.tabs.length - 1) {
          this.activeTab = this.tabs[tabIndex + 1].name
        } else if (tabIndex > 0) {
          this.activeTab = this.tabs[tabIndex - 1].name
        } else {
          // 如果只剩一个tab，切换到仪表板
          this.activeTab = '/dashboard'
        }
      }

      // 移除tab
      this.tabs.splice(tabIndex, 1)
    },

    // 关闭其他tabs
    closeOtherTabs(keepTabName) {
      this.tabs = this.tabs.filter(tab => 
        tab.name === keepTabName || !tab.closable
      )
      this.activeTab = keepTabName
    },

    // 关闭所有可关闭的tabs
    closeAllTabs() {
      this.tabs = this.tabs.filter(tab => !tab.closable)
      this.activeTab = '/dashboard'
    },

    // 切换tab
    switchTab(tabName) {
      const tab = this.tabs.find(tab => tab.name === tabName)
      if (tab) {
        this.activeTab = tabName
      }
    },

    // 根据路由信息添加tab
    addTabFromRoute(route) {
      const routeTabMap = {
        '/dashboard': {
          title: '仪表板',
          icon: 'Odometer',
          component: 'Dashboard'
        },
        '/logs': {
          title: '日志管理',
          icon: 'Document',
          component: 'LogList'
        },
        '/applications': {
          title: '应用管理',
          icon: 'Grid',
          component: 'ApplicationList'
        },
        '/users': {
          title: '用户管理',
          icon: 'User',
          component: 'UserList'
        },
        '/api-docs': {
          title: 'API文档',
          icon: 'Files',
          component: 'ApiDocs'
        },
        '/profile': {
          title: '个人资料',
          icon: 'User',
          component: 'Profile'
        },
        '/settings': {
          title: '设置',
          icon: 'Setting',
          component: 'Settings'
        }
      }

      const tabInfo = routeTabMap[route.path]
      if (tabInfo) {
        this.addTab({
          path: route.path,
          ...tabInfo
        })
      }
    },

    // 初始化tabs（从路由初始化）
    initTabs(currentRoute) {
      // 确保仪表板tab存在
      if (!this.tabs.find(tab => tab.name === '/dashboard')) {
        this.tabs.unshift({
          name: '/dashboard',
          title: '仪表板',
          icon: 'Odometer',
          closable: false,
          component: 'Dashboard'
        })
      }

      // 如果当前路由不是仪表板，添加当前路由的tab
      if (currentRoute.path !== '/dashboard' && currentRoute.path !== '/login') {
        this.addTabFromRoute(currentRoute)
      }

      // 设置当前激活的tab
      if (currentRoute.path !== '/login') {
        this.activeTab = currentRoute.path
      }
    }
  }
})
