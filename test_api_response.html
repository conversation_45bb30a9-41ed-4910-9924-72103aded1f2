<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试API响应格式</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            background: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            color: green;
        }
        .error {
            color: red;
        }
        .query-time {
            color: #409eff;
            font-weight: bold;
        }
        .pagination-demo {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            background: #fafafa;
            border: 1px solid #ebeef5;
            border-radius: 4px;
            margin: 10px 0;
        }
        .pagination-total-demo {
            color: #606266;
            font-size: 13px;
            margin-right: 20px;
        }
        .pagination-controls-demo {
            color: #606266;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <h1>测试API响应格式和分页显示</h1>
    
    <div class="test-section">
        <h3>预期的分页显示效果</h3>
        <div class="pagination-demo">
            <span class="pagination-total-demo">查询用时 156 毫秒，共 1234 条</span>
            <span class="pagination-controls-demo">每页显示 10 条 | 上一页 1 2 3 4 5 下一页 | 跳至第 页</span>
        </div>
    </div>
    
    <div class="test-section">
        <h3>1. 测试健康检查API</h3>
        <button onclick="testHealth()">测试健康检查</button>
        <div id="healthResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>2. 测试登录API</h3>
        <button onclick="testLogin()">测试登录</button>
        <div id="loginTestResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>3. 测试日志查询API（需要登录）</h3>
        <button onclick="testLogsWithAuth()">测试日志查询</button>
        <div id="logsResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>4. 测试日志搜索API（需要登录）</h3>
        <button onclick="testSearchWithAuth()">测试日志搜索</button>
        <div id="searchResult" class="result"></div>
    </div>

    <script>
        let authToken = '';

        async function testHealth() {
            const resultDiv = document.getElementById('healthResult');
            resultDiv.textContent = '正在测试健康检查...';

            try {
                const response = await fetch('http://localhost:8080/api/system/health');
                const result = await response.json();

                resultDiv.innerHTML = `<span class="success">✓ 健康检查成功</span>
响应格式:
${JSON.stringify(result, null, 2)}

<span class="query-time">queryTime字段: ${result.queryTime}</span>`;
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">✗ 健康检查失败: ${error.message}</span>`;
            }
        }

        async function testLogin() {
            const resultDiv = document.getElementById('loginTestResult');
            resultDiv.innerHTML = '测试中...';

            try {
                const response = await fetch('http://localhost:8080/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: '123456'
                    })
                });
                const data = await response.json();

                if (data.code === 200 && data.data && data.data.token) {
                    authToken = data.data.token;
                    resultDiv.innerHTML = `
                        <span class="success">✓ 登录成功!</span>
                        <p>Token: ${authToken.substring(0, 50)}...</p>
                        <h4>完整响应:</h4>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <span class="error">✗ 登录失败</span>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">✗ 错误: ${error.message}</span>`;
            }
        }

        async function testLogsWithAuth() {
            const resultDiv = document.getElementById('logsResult');
            resultDiv.innerHTML = '测试中...';

            if (!authToken) {
                resultDiv.innerHTML = '<span class="error">请先登录获取Token</span>';
                return;
            }

            try {
                const response = await fetch('http://localhost:8080/api/logs?page=1&size=5', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                const data = await response.json();

                if (data.code === 200) {
                    const total = data.data?.totalElements || data.data?.total || 0;
                    const queryTime = data.queryTime || 0;

                    resultDiv.innerHTML = `<span class="success">✓ 日志查询成功</span>
总记录数: ${total}
<span class="query-time">查询用时: ${queryTime} 毫秒</span>

完整响应:
${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.innerHTML = `<span class="error">✗ 日志查询失败</span>
${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">✗ 错误: ${error.message}</span>`;
            }
        }

        async function testSearchWithAuth() {
            const resultDiv = document.getElementById('searchResult');
            resultDiv.innerHTML = '测试中...';

            if (!authToken) {
                resultDiv.innerHTML = '<span class="error">请先登录获取Token</span>';
                return;
            }

            try {
                const response = await fetch('http://localhost:8080/api/logs/search', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        page: 1,
                        size: 5,
                        level: null,
                        applicationId: null,
                        source: null,
                        environment: null,
                        startTime: null,
                        endTime: null,
                        keyword: null,
                        propertySearch: null,
                        propertyType: 'all'
                    })
                });
                const data = await response.json();

                if (data.code === 200) {
                    const total = data.data?.totalElements || data.data?.total || 0;
                    const queryTime = data.queryTime || 0;

                    resultDiv.innerHTML = `<span class="success">✓ 日志搜索成功</span>
总记录数: ${total}
<span class="query-time">查询用时: ${queryTime} 毫秒</span>

完整响应:
${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.innerHTML = `<span class="error">✗ 日志搜索失败</span>
${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">✗ 错误: ${error.message}</span>`;
            }
        }
    </script>
</body>
</html>
