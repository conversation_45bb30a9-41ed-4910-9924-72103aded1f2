# 使用官方 .NET 9 运行时镜像
FROM mcr.microsoft.com/dotnet/runtime:9.0 AS base
WORKDIR /app

# 使用官方 .NET 9 SDK 镜像进行构建
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# 复制项目文件
COPY LogSimulator.csproj .
RUN dotnet restore

# 复制源代码
COPY . .

# 构建应用程序
RUN dotnet build -c Release -o /app/build

# 发布应用程序
FROM build AS publish
RUN dotnet publish -c Release -o /app/publish

# 最终镜像
FROM base AS final
WORKDIR /app

# 复制发布的文件
COPY --from=publish /app/publish .

# 设置环境变量
ENV DOTNET_ENVIRONMENT=Production

# 创建非root用户
RUN adduser --disabled-password --gecos '' appuser && chown -R appuser /app
USER appuser

# 设置入口点
ENTRYPOINT ["dotnet", "LogSimulator.dll"]

# 默认命令（交互模式）
CMD ["interactive"]
