<template>
  <div class="message-test-container">
    <div class="test-header">
      <h2>消息提示系统测试</h2>
      <p>测试统一美观的消息提示UI，不需要人干预的消息从右下角弹出</p>
    </div>

    <div class="test-sections">
      <!-- 通知消息测试 -->
      <div class="test-section">
        <h3>通知消息（右下角弹出）</h3>
        <div class="button-group">
          <el-button type="success" @click="showSuccessNotification">
            成功消息
          </el-button>
          <el-button type="info" @click="showInfoNotification">
            信息消息
          </el-button>
          <el-button type="warning" @click="showWarningNotification">
            警告消息
          </el-button>
          <el-button type="danger" @click="showErrorNotification">
            错误消息
          </el-button>
          <el-button @click="showPersistentNotification">
            持久化消息
          </el-button>
        </div>
      </div>

      <!-- 对话框测试 -->
      <div class="test-section">
        <h3>对话框消息（需要用户交互）</h3>
        <div class="button-group">
          <el-button type="primary" @click="showConfirmDialog">
            确认对话框
          </el-button>
          <el-button type="warning" @click="showWarningDialog">
            警告对话框
          </el-button>
          <el-button type="danger" @click="showDeleteDialog">
            删除确认
          </el-button>
          <el-button type="info" @click="showAlertDialog">
            信息提示
          </el-button>
          <el-button @click="showPromptDialog">
            输入对话框
          </el-button>
        </div>
      </div>

      <!-- 批量测试 -->
      <div class="test-section">
        <h3>批量测试</h3>
        <div class="button-group">
          <el-button @click="showMultipleNotifications">
            显示多个通知
          </el-button>
          <el-button @click="clearAllNotifications">
            清空所有通知
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Message, MessageBox } from '../utils/message'

// 通知消息测试
const showSuccessNotification = () => {
  Message.success('操作成功！这是一个成功消息示例。')
}

const showInfoNotification = () => {
  Message.info('这是一个信息提示，用于向用户传达一般性信息。', {
    title: '信息提示'
  })
}

const showWarningNotification = () => {
  Message.warning('请注意！这是一个警告消息，提醒用户注意某些情况。', {
    title: '警告提示',
    duration: 6000
  })
}

const showErrorNotification = () => {
  Message.error('操作失败！这是一个错误消息，通常用于提示操作失败。', {
    title: '错误提示',
    duration: 8000
  })
}

const showPersistentNotification = () => {
  Message.persistent('info', '这是一个持久化消息，不会自动关闭，需要用户手动关闭。', {
    title: '持久化提示'
  })
}

// 对话框测试
const showConfirmDialog = async () => {
  try {
    await MessageBox.confirm('确定要执行这个操作吗？', '确认操作')
    Message.success('用户确认了操作')
  } catch (error) {
    if (error !== 'cancel') {
      Message.info('用户取消了操作')
    }
  }
}

const showWarningDialog = async () => {
  try {
    await MessageBox.warning('这个操作可能会产生不可逆的影响，请谨慎操作！', '警告')
    Message.success('用户确认了警告操作')
  } catch (error) {
    if (error !== 'cancel') {
      Message.info('用户取消了警告操作')
    }
  }
}

const showDeleteDialog = async () => {
  try {
    await MessageBox.delete('确定要删除这个项目吗？删除后无法恢复。', '确认删除')
    Message.success('删除操作已确认')
  } catch (error) {
    if (error !== 'cancel') {
      Message.info('删除操作已取消')
    }
  }
}

const showAlertDialog = async () => {
  try {
    await MessageBox.alert('这是一个信息提示对话框，只有确定按钮。', '信息提示')
    Message.success('用户确认了信息提示')
  } catch (error) {
    // Alert dialog closed
  }
}

const showPromptDialog = async () => {
  try {
    const result = await MessageBox.prompt('请输入您的姓名：', '输入信息', {
      inputPlaceholder: '请输入姓名'
    })
    Message.success(`您输入的姓名是：${result}`)
  } catch (error) {
    if (error !== 'cancel') {
      Message.info('用户取消了输入')
    }
  }
}

// 批量测试
const showMultipleNotifications = () => {
  Message.success('第一个成功消息')
  setTimeout(() => Message.info('第二个信息消息'), 500)
  setTimeout(() => Message.warning('第三个警告消息'), 1000)
  setTimeout(() => Message.error('第四个错误消息'), 1500)
}

const clearAllNotifications = () => {
  Message.clearAll()
  Message.info('所有通知已清空')
}
</script>

<style scoped>
.message-test-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-header {
  text-align: center;
  margin-bottom: 40px;
}

.test-header h2 {
  color: #303133;
  margin-bottom: 12px;
}

.test-header p {
  color: #606266;
  font-size: 14px;
}

.test-sections {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.test-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.test-section h3 {
  color: #303133;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 600;
}

.button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.button-group .el-button {
  min-width: 120px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .message-test-container {
    padding: 16px;
  }
  
  .test-section {
    padding: 16px;
  }
  
  .button-group {
    flex-direction: column;
  }
  
  .button-group .el-button {
    width: 100%;
    min-width: auto;
  }
}
</style>
