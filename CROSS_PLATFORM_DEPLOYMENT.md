# 通用日志管理系统 - 跨平台部署指南

## 概述

本指南详细介绍如何将Windows本地开发的源码部署到Linux Docker环境中。

### 部署架构

```
Windows开发环境 → Linux生产服务器
     ↓                    ↓
  源码打包            Docker容器化部署
     ↓                    ↓
  文件传输            服务运行监控
```

## 前置要求

### Windows端要求

- **操作系统**: Windows 10/11 或 Windows Server
- **网络工具**: 以下任一工具
  - PuTTY (推荐) - https://www.putty.org/
  - OpenSSH Client (Windows 10+ 内置)
  - Git for Windows (包含SSH工具)
- **压缩工具**: 以下任一工具
  - 7-Zip (推荐)
  - WinRAR
  - PowerShell (内置)

### Linux端要求

- **操作系统**: Ubuntu 18.04+, CentOS 7+, 或其他主流Linux发行版
- **硬件配置**: 
  - CPU: 2核心以上
  - 内存: 4GB以上
  - 磁盘: 20GB可用空间
- **网络**: 可访问互联网（用于下载Docker镜像）
- **权限**: sudo权限或docker组成员

## 快速开始

### 方法一：一键部署（推荐）

1. **配置部署参数**
```cmd
cd scripts
copy deploy-config.example.txt deploy-config.txt
notepad deploy-config.txt
```

2. **执行一键部署**
```cmd
deploy-remote.bat
```

### 方法二：分步部署

#### 步骤1: 准备Linux服务器

在Linux服务器上执行：
```bash
# 下载环境设置脚本
curl -O https://raw.githubusercontent.com/your-repo/logger-management/main/scripts/setup-linux-server.sh
chmod +x setup-linux-server.sh

# 运行环境设置
./setup-linux-server.sh
```

#### 步骤2: 打包源码

在Windows上执行：
```cmd
cd scripts
package-source.bat
```

#### 步骤3: 传输文件

```cmd
transfer-to-linux.bat
```

#### 步骤4: 远程部署

SSH登录到Linux服务器：
```bash
ssh username@your-server-ip
cd /home/<USER>/logger-management
unzip logger-management-*.zip
chmod +x scripts/deploy-on-linux.sh
./scripts/deploy-on-linux.sh
```

## 详细配置说明

### 部署配置文件

编辑 `scripts/deploy-config.txt`：

```ini
# 基本连接配置
SERVER_HOST=*************          # Linux服务器IP
SERVER_USER=ubuntu                 # SSH用户名
SERVER_PORT=22                     # SSH端口
DEPLOY_PATH=/home/<USER>/logger-management  # 部署目录

# 认证配置
SSH_KEY_PATH=C:\Users\<USER>\.ssh\id_rsa    # SSH密钥路径(可选)

# 应用配置
FRONTEND_PORT=80                   # 前端端口
BACKEND_PORT=8080                  # 后端端口
USE_EXTERNAL_MONGODB=false         # 是否使用外部MongoDB
```

### SSH密钥认证设置

1. **生成SSH密钥对**（在Windows上）：
```cmd
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"
```

2. **复制公钥到Linux服务器**：
```cmd
scp C:\Users\<USER>\.ssh\id_rsa.pub username@server-ip:~/.ssh/authorized_keys
```

3. **设置正确权限**（在Linux上）：
```bash
chmod 700 ~/.ssh
chmod 600 ~/.ssh/authorized_keys
```

## 部署模式选择

### 完整部署模式

**适用场景**：
- 测试环境
- 开发环境
- 独立部署

**特点**：
- 包含MongoDB容器
- 一键启动所有服务
- 数据完全隔离

**配置**：
```ini
USE_EXTERNAL_MONGODB=false
```

### 外部MongoDB模式

**适用场景**：
- 生产环境
- 已有数据库集群
- 多应用共享数据库

**特点**：
- 连接外部MongoDB
- 更好的数据管理
- 高可用性支持

**配置**：
```ini
USE_EXTERNAL_MONGODB=true
EXTERNAL_MONGODB_HOST=mongodb-cluster.company.com
EXTERNAL_MONGODB_PORT=27017
EXTERNAL_MONGODB_DATABASE=logger_management
EXTERNAL_MONGODB_USERNAME=app_user
EXTERNAL_MONGODB_PASSWORD=SecurePassword123
```

## 网络和安全配置

### 防火墙设置

在Linux服务器上开放必要端口：

```bash
# Ubuntu/Debian (UFW)
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # 前端
sudo ufw allow 8080/tcp  # 后端API
sudo ufw allow 8081/tcp  # MongoDB管理(可选)

# CentOS/RHEL (firewalld)
sudo firewall-cmd --permanent --add-port=22/tcp
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=8080/tcp
sudo firewall-cmd --permanent --add-port=8081/tcp
sudo firewall-cmd --reload
```

### HTTPS配置（生产环境推荐）

使用Nginx反向代理配置HTTPS：

```nginx
server {
    listen 443 ssl;
    server_name your-domain.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    location / {
        proxy_pass http://localhost:80;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    location /api/ {
        proxy_pass http://localhost:8080/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 监控和维护

### 服务状态检查

```bash
# 查看容器状态
docker-compose ps

# 查看服务日志
docker-compose logs -f backend
docker-compose logs -f frontend

# 检查资源使用
docker stats
```

### 备份和恢复

```bash
# 备份MongoDB数据
docker-compose exec mongodb mongodump --out /backup
docker cp logger-management-mongodb:/backup ./backup-$(date +%Y%m%d)

# 备份应用配置
tar -czf config-backup-$(date +%Y%m%d).tar.gz docker/.env docker/docker-compose.yml
```

### 更新部署

```bash
# 停止服务
docker-compose down

# 更新代码
unzip -o new-package.zip

# 重新构建和启动
docker-compose build
docker-compose up -d
```

## 故障排除

### 常见问题

1. **连接被拒绝**
   - 检查防火墙设置
   - 验证端口是否正确开放
   - 确认服务是否正常运行

2. **权限错误**
   - 检查文件权限：`ls -la`
   - 确认用户在docker组中：`groups $USER`
   - 重新登录以刷新组权限

3. **内存不足**
   - 检查系统内存：`free -h`
   - 调整Docker内存限制
   - 优化JVM参数

4. **磁盘空间不足**
   - 检查磁盘使用：`df -h`
   - 清理Docker镜像：`docker system prune`
   - 清理日志文件

### 日志分析

```bash
# 查看系统日志
sudo journalctl -u docker

# 查看应用日志
docker-compose logs --tail=100 backend

# 实时监控日志
docker-compose logs -f
```

## 性能优化

### 系统级优化

```bash
# 增加文件描述符限制
echo "* soft nofile 65536" | sudo tee -a /etc/security/limits.conf
echo "* hard nofile 65536" | sudo tee -a /etc/security/limits.conf

# 优化内核参数
echo "vm.max_map_count=262144" | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

### 应用级优化

```yaml
# docker-compose.yml 中添加资源限制
services:
  backend:
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
```

## 技术支持

### 获取帮助

1. **查看日志**：首先检查应用和系统日志
2. **验证配置**：确认所有配置文件正确
3. **网络测试**：验证网络连接和端口访问
4. **资源检查**：确认系统资源充足

### 联系方式

- 项目文档：查看项目README.md
- 问题反馈：提交GitHub Issue
- 技术支持：联系系统管理员

---

**版本**: 1.0.0  
**更新时间**: 2024-06-20  
**适用系统**: Windows → Linux Docker
