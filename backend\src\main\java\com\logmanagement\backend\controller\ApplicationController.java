package com.logmanagement.backend.controller;

import com.logmanagement.backend.config.PaginationConfig;
import com.logmanagement.backend.dto.ApiResponse;
import com.logmanagement.backend.dto.CreateApplicationRequest;
import com.logmanagement.backend.dto.UpdateApplicationRequest;
import com.logmanagement.backend.entity.Application;
import com.logmanagement.backend.entity.ApplicationStatus;
import com.logmanagement.backend.entity.User;
import com.logmanagement.backend.repository.ApplicationRepository;
import com.logmanagement.backend.repository.UserRepository;
import com.logmanagement.backend.util.JwtUtil;
import io.swagger.annotations.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 应用管理控制器
 *
 * 提供应用管理的完整功能，包括：
 * - 应用的创建、查询、更新、删除
 * - 应用Token的生成和重新生成
 * - 应用状态管理
 * - 应用权限控制
 *
 * 权限控制：
 * - 管理员可以管理所有应用
 * - 普通用户只能管理自己创建的应用和被授权的应用
 *
 * 每个应用都有唯一的Token，用于日志推送时的身份验证
 *
 * <AUTHOR> Management System
 * @version 1.0.0
 */
@Api(tags = "应用管理", description = "应用的增删改查、Token管理、权限控制等功能")
@RestController
@RequestMapping("/applications")
public class ApplicationController {

    private static final Logger logger = LoggerFactory.getLogger(ApplicationController.class);

    @Autowired
    private ApplicationRepository applicationRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private PaginationConfig paginationConfig;

    /**
     * 获取应用列表（分页）
     *
     * 支持多种过滤条件的分页查询，包括：
     * - 应用状态过滤（ACTIVE、INACTIVE）
     * - 环境过滤（dev、test、prod等）
     * - 关键词搜索（应用名称）
     *
     * 权限控制：
     * - 管理员可以查看所有应用
     * - 普通用户只能查看自己创建的应用和被授权的应用
     *
     * @param page 页码，从1开始
     * @param size 每页大小
     * @param sort 排序字段
     * @param order 排序方向
     * @param status 应用状态过滤
     * @param environment 环境过滤
     * @param keyword 关键词搜索
     * @param authHeader JWT认证令牌
     * @return 应用分页结果
     */
    @ApiOperation(
        value = "获取应用列表",
        notes = "分页查询应用列表，支持按状态、环境、关键词过滤。" +
                "管理员可以查看所有应用，普通用户只能查看有权限的应用。"
    )
    @GetMapping
    public ResponseEntity<ApiResponse<Page<Application>>> getApplications(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(required = false) Integer size,
            @RequestParam(defaultValue = "createdAt") String sort,
            @RequestParam(defaultValue = "desc") String order,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String environment,
            @RequestParam(required = false) String keyword,
            @RequestHeader(value = "Authorization", required = false) String authHeader) {

        // 使用配置的默认页面大小，并验证大小限制
        int validatedSize = size != null ? paginationConfig.validatePageSize(size) : paginationConfig.getDefaultPageSize();

        logger.info("获取应用列表: page={}, size={}, sort={}, order={}, status={}, environment={}, keyword={}",
                   page, validatedSize, sort, order, status, environment, keyword);

        try {
            // 验证用户权限
            String currentUserId = getCurrentUserId(authHeader);
            if (currentUserId == null) {
                return ResponseEntity.ok(ApiResponse.error("未授权访问"));
            }

            // 创建分页参数
            Sort.Direction direction = "desc".equalsIgnoreCase(order) ? Sort.Direction.DESC : Sort.Direction.ASC;
            Pageable pageable = PageRequest.of(page - 1, validatedSize, Sort.by(direction, sort));

            Page<Application> applications;

            // 根据用户权限过滤应用
            User currentUser = userRepository.findById(currentUserId).orElse(null);
            if (currentUser == null) {
                return ResponseEntity.ok(ApiResponse.error("用户不存在"));
            }

            // 超级管理员可以看到所有应用
            if ("SUPER_ADMIN".equals(currentUser.getRole().name())) {
                applications = searchApplicationsForAdmin(keyword, status, environment, pageable);
            } else {
                // 其他用户只能看到自己创建的应用
                applications = searchApplicationsForUser(currentUserId, keyword, status, environment, pageable);
            }

            return ResponseEntity.ok(ApiResponse.success("获取应用列表成功", applications));

        } catch (Exception e) {
            logger.error("获取应用列表失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取应用列表失败"));
        }
    }

    /**
     * 获取应用详情
     *
     * 根据应用ID获取应用的详细信息
     *
     * 权限控制：
     * - 管理员可以查看所有应用详情
     * - 普通用户只能查看自己创建的应用和被授权的应用详情
     *
     * @param id 应用ID
     * @param authHeader JWT认证令牌
     * @return 应用详细信息
     */
    @ApiOperation(
        value = "获取应用详情",
        notes = "根据应用ID获取应用的详细信息，包括应用配置、Token等。" +
                "管理员可以查看所有应用，普通用户只能查看有权限的应用。"
    )
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<Application>> getApplication(
            @ApiParam(value = "应用ID", required = true, example = "app123456")
            @PathVariable String id,
            @ApiParam(value = "JWT认证令牌，格式：Bearer <token>", required = true)
            @RequestHeader(value = "Authorization", required = false) String authHeader) {

        logger.info("获取应用详情: {}", id);

        try {
            String currentUserId = getCurrentUserId(authHeader);
            if (currentUserId == null) {
                return ResponseEntity.ok(ApiResponse.error("未授权访问"));
            }

            Optional<Application> applicationOpt = applicationRepository.findById(id);
            if (!applicationOpt.isPresent()) {
                return ResponseEntity.ok(ApiResponse.error("应用不存在"));
            }

            Application application = applicationOpt.get();

            // 检查权限
            if (!hasPermission(currentUserId, application)) {
                return ResponseEntity.ok(ApiResponse.error("无权限访问该应用"));
            }

            return ResponseEntity.ok(ApiResponse.success("获取应用详情成功", application));

        } catch (Exception e) {
            logger.error("获取应用详情失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取应用详情失败"));
        }
    }

    /**
     * 创建应用
     *
     * 创建新的应用，每个应用会自动生成唯一的Token用于日志推送
     *
     * 权限控制：
     * - 所有登录用户都可以创建应用
     * - 创建者自动获得该应用的管理权限
     *
     * @param request 创建应用请求信息
     * @param authHeader JWT认证令牌
     * @return 创建成功的应用信息（包含生成的Token）
     */
    @ApiOperation(
        value = "创建应用",
        notes = "创建新的应用，系统会自动生成唯一的Token用于日志推送。" +
                "创建者自动获得该应用的管理权限。应用名称必须唯一。"
    )
    @PostMapping
    public ResponseEntity<ApiResponse<Application>> createApplication(
            @ApiParam(value = "创建应用请求信息", required = true)
            @Valid @RequestBody CreateApplicationRequest request,
            @ApiParam(value = "JWT认证令牌，格式：Bearer <token>", required = true)
            @RequestHeader(value = "Authorization", required = false) String authHeader) {

        logger.info("创建应用: {}", request.getName());

        try {
            String currentUserId = getCurrentUserId(authHeader);
            if (currentUserId == null) {
                return ResponseEntity.ok(ApiResponse.error("未授权访问"));
            }

            User currentUser = userRepository.findById(currentUserId).orElse(null);
            if (currentUser == null) {
                return ResponseEntity.ok(ApiResponse.error("用户不存在"));
            }

            // 检查应用名称是否已存在
            if (applicationRepository.existsByName(request.getName())) {
                return ResponseEntity.ok(ApiResponse.error("应用名称已存在"));
            }

            // 创建应用
            Application application = new Application();
            application.setId("app-" + UUID.randomUUID().toString().replace("-", ""));
            application.setName(request.getName());
            application.setDescription(request.getDescription());
            application.setToken(UUID.randomUUID().toString().replace("-", ""));
            application.setStatus(ApplicationStatus.ACTIVE);
            application.setCreatorId(currentUserId);
            application.setCreatorUsername(currentUser.getUsername());
            application.setEnvironment(request.getEnvironment());
            application.setVersion(request.getVersion());
            application.setLogRetentionDays(request.getLogRetentionDays());
            application.setLastActiveTime(LocalDateTime.now());
            application.setCreatedAt(LocalDateTime.now());

            Application savedApplication = applicationRepository.save(application);

            logger.info("应用创建成功: {}", savedApplication.getName());
            return ResponseEntity.ok(ApiResponse.success("应用创建成功", savedApplication));

        } catch (Exception e) {
            logger.error("创建应用失败", e);
            return ResponseEntity.ok(ApiResponse.error("创建应用失败"));
        }
    }

    /**
     * 更新应用
     *
     * 更新应用的基本信息，如名称、描述、环境等
     *
     * 权限控制：
     * - 管理员可以更新所有应用
     * - 普通用户只能更新自己创建的应用和被授权的应用
     *
     * 注意：应用Token不会被更新，如需更新Token请使用重新生成Token接口
     *
     * @param id 应用ID
     * @param request 更新应用请求信息
     * @param authHeader JWT认证令牌
     * @return 更新后的应用信息
     */
    @ApiOperation(
        value = "更新应用",
        notes = "更新应用的基本信息，如名称、描述、环境等。" +
                "管理员可以更新所有应用，普通用户只能更新有权限的应用。" +
                "应用Token不会被更新。"
    )
    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<Application>> updateApplication(
            @ApiParam(value = "应用ID", required = true, example = "app123456")
            @PathVariable String id,
            @ApiParam(value = "更新应用请求信息", required = true)
            @Valid @RequestBody UpdateApplicationRequest request,
            @ApiParam(value = "JWT认证令牌，格式：Bearer <token>", required = true)
            @RequestHeader(value = "Authorization", required = false) String authHeader) {

        logger.info("更新应用: {}", id);

        try {
            String currentUserId = getCurrentUserId(authHeader);
            if (currentUserId == null) {
                return ResponseEntity.ok(ApiResponse.error("未授权访问"));
            }

            Optional<Application> applicationOpt = applicationRepository.findById(id);
            if (!applicationOpt.isPresent()) {
                return ResponseEntity.ok(ApiResponse.error("应用不存在"));
            }

            Application application = applicationOpt.get();

            // 检查权限
            if (!hasPermission(currentUserId, application)) {
                return ResponseEntity.ok(ApiResponse.error("无权限修改该应用"));
            }

            // 检查应用名称是否已被其他应用使用
            if (!application.getName().equals(request.getName()) && 
                applicationRepository.existsByName(request.getName())) {
                return ResponseEntity.ok(ApiResponse.error("应用名称已存在"));
            }

            // 更新应用信息
            application.setName(request.getName());
            application.setDescription(request.getDescription());

            // 更新状态
            if (request.getStatus() != null && !request.getStatus().trim().isEmpty()) {
                try {
                    ApplicationStatus status = ApplicationStatus.valueOf(request.getStatus().toUpperCase());
                    application.setStatus(status);
                } catch (IllegalArgumentException e) {
                    return ResponseEntity.ok(ApiResponse.error("无效的应用状态"));
                }
            }

            application.setEnvironment(request.getEnvironment());
            application.setVersion(request.getVersion());
            application.setLogRetentionDays(request.getLogRetentionDays());
            application.setUpdatedAt(LocalDateTime.now());

            Application savedApplication = applicationRepository.save(application);

            logger.info("应用更新成功: {}", savedApplication.getName());
            return ResponseEntity.ok(ApiResponse.success("应用更新成功", savedApplication));

        } catch (Exception e) {
            logger.error("更新应用失败", e);
            return ResponseEntity.ok(ApiResponse.error("更新应用失败"));
        }
    }

    /**
     * 删除应用
     *
     * 删除指定的应用，删除后该应用的Token将失效
     *
     * 权限控制：
     * - 管理员可以删除所有应用
     * - 普通用户只能删除自己创建的应用和被授权的应用
     *
     * 警告：删除操作不可逆，请谨慎操作
     *
     * @param id 应用ID
     * @param authHeader JWT认证令牌
     * @return 删除结果
     */
    @ApiOperation(
        value = "删除应用",
        notes = "删除指定的应用，删除后该应用的Token将失效。" +
                "管理员可以删除所有应用，普通用户只能删除有权限的应用。" +
                "删除操作不可逆，请谨慎操作。"
    )
    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<String>> deleteApplication(
            @ApiParam(value = "应用ID", required = true, example = "app123456")
            @PathVariable String id,
            @ApiParam(value = "JWT认证令牌，格式：Bearer <token>", required = true)
            @RequestHeader(value = "Authorization", required = false) String authHeader) {

        logger.info("删除应用: {}", id);

        try {
            String currentUserId = getCurrentUserId(authHeader);
            if (currentUserId == null) {
                return ResponseEntity.ok(ApiResponse.error("未授权访问"));
            }

            Optional<Application> applicationOpt = applicationRepository.findById(id);
            if (!applicationOpt.isPresent()) {
                return ResponseEntity.ok(ApiResponse.error("应用不存在"));
            }

            Application application = applicationOpt.get();

            // 检查权限
            if (!hasPermission(currentUserId, application)) {
                return ResponseEntity.ok(ApiResponse.error("无权限删除该应用"));
            }

            applicationRepository.delete(application);

            logger.info("应用删除成功: {}", application.getName());
            return ResponseEntity.ok(ApiResponse.success("应用删除成功", null));

        } catch (Exception e) {
            logger.error("删除应用失败", e);
            return ResponseEntity.ok(ApiResponse.error("删除应用失败"));
        }
    }

    /**
     * 重新生成应用Token
     *
     * 为指定应用重新生成Token，旧Token将立即失效
     *
     * 权限控制：
     * - 管理员可以重新生成所有应用的Token
     * - 普通用户只能重新生成自己创建的应用和被授权的应用的Token
     *
     * 注意：重新生成后，使用旧Token的日志推送将失败，需要更新客户端配置
     *
     * @param id 应用ID
     * @param authHeader JWT认证令牌
     * @return 新生成的Token
     */
    @ApiOperation(
        value = "重新生成应用Token",
        notes = "为指定应用重新生成Token，旧Token将立即失效。" +
                "管理员可以重新生成所有应用的Token，普通用户只能重新生成有权限的应用的Token。" +
                "重新生成后需要更新客户端配置。"
    )
    @PostMapping("/{id}/regenerate-token")
    public ResponseEntity<ApiResponse<Map<String, String>>> regenerateToken(
            @ApiParam(value = "应用ID", required = true, example = "app123456")
            @PathVariable String id,
            @ApiParam(value = "JWT认证令牌，格式：Bearer <token>", required = true)
            @RequestHeader(value = "Authorization", required = false) String authHeader) {

        logger.info("重新生成应用Token: {}", id);

        try {
            String currentUserId = getCurrentUserId(authHeader);
            if (currentUserId == null) {
                return ResponseEntity.ok(ApiResponse.error("未授权访问"));
            }

            Optional<Application> applicationOpt = applicationRepository.findById(id);
            if (!applicationOpt.isPresent()) {
                return ResponseEntity.ok(ApiResponse.error("应用不存在"));
            }

            Application application = applicationOpt.get();

            // 检查权限
            if (!hasPermission(currentUserId, application)) {
                return ResponseEntity.ok(ApiResponse.error("无权限操作该应用"));
            }

            // 生成新Token（也作为ApiKey）
            String newToken = UUID.randomUUID().toString().replace("-", "");
            application.setToken(newToken);
            application.setUpdatedAt(LocalDateTime.now());

            applicationRepository.save(application);

            Map<String, String> result = new HashMap<>();
            result.put("token", newToken);
            result.put("apiKey", newToken); // ApiKey就是Token

            logger.info("应用Token重新生成成功: {}", application.getName());
            return ResponseEntity.ok(ApiResponse.success("Token重新生成成功", result));

        } catch (Exception e) {
            logger.error("重新生成Token失败", e);
            return ResponseEntity.ok(ApiResponse.error("重新生成Token失败"));
        }
    }

    /**
     * 获取当前用户ID
     */
    private String getCurrentUserId(String authHeader) {
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            return null;
        }

        try {
            String token = authHeader.substring(7);
            return jwtUtil.getUserIdFromToken(token);
        } catch (Exception e) {
            logger.error("获取用户ID失败", e);
            return null;
        }
    }

    /**
     * 检查用户是否有权限访问应用
     */
    private boolean hasPermission(String userId, Application application) {
        try {
            User user = userRepository.findById(userId).orElse(null);
            if (user == null) {
                return false;
            }

            // 超级管理员有所有权限
            if ("SUPER_ADMIN".equals(user.getRole().name())) {
                return true;
            }

            // 应用创建者有权限
            if (application.getCreatorId().equals(userId)) {
                return true;
            }

            // 检查用户是否被授权访问该应用
            if (user.getAuthorizedAppIds() != null &&
                user.getAuthorizedAppIds().contains(application.getId())) {
                return true;
            }

            return false;
        } catch (Exception e) {
            logger.error("检查权限失败", e);
            return false;
        }
    }

    /**
     * 为管理员搜索应用
     */
    private Page<Application> searchApplicationsForAdmin(String keyword, String status, String environment, Pageable pageable) {
        try {
            // 如果有多个搜索条件，需要组合查询
            if (hasMultipleSearchConditions(keyword, status, environment)) {
                // 这里简化处理，优先使用状态和环境条件
                if (status != null && !status.trim().isEmpty() && environment != null && !environment.trim().isEmpty()) {
                    ApplicationStatus appStatus = ApplicationStatus.valueOf(status.toUpperCase());
                    return applicationRepository.findByStatusAndEnvironment(appStatus, environment, pageable);
                } else if (status != null && !status.trim().isEmpty()) {
                    ApplicationStatus appStatus = ApplicationStatus.valueOf(status.toUpperCase());
                    return applicationRepository.findByStatus(appStatus, pageable);
                } else if (environment != null && !environment.trim().isEmpty()) {
                    return applicationRepository.findByEnvironment(environment, pageable);
                } else if (keyword != null && !keyword.trim().isEmpty()) {
                    return applicationRepository.findByNameContainingIgnoreCase(keyword.trim(), pageable);
                }
            } else {
                // 单个搜索条件
                if (status != null && !status.trim().isEmpty()) {
                    ApplicationStatus appStatus = ApplicationStatus.valueOf(status.toUpperCase());
                    return applicationRepository.findByStatus(appStatus, pageable);
                } else if (environment != null && !environment.trim().isEmpty()) {
                    return applicationRepository.findByEnvironment(environment, pageable);
                } else if (keyword != null && !keyword.trim().isEmpty()) {
                    return applicationRepository.findByNameContainingIgnoreCase(keyword.trim(), pageable);
                }
            }

            return applicationRepository.findAll(pageable);
        } catch (Exception e) {
            logger.error("管理员搜索应用失败", e);
            return applicationRepository.findAll(pageable);
        }
    }

    /**
     * 为普通用户搜索应用
     */
    private Page<Application> searchApplicationsForUser(String userId, String keyword, String status, String environment, Pageable pageable) {
        try {
            // 获取用户信息
            User user = userRepository.findById(userId).orElse(null);
            if (user == null) {
                return Page.empty();
            }

            // 获取用户有权限的应用ID列表（包括自己创建的和被授权的）
            List<String> authorizedAppIds = user.getAuthorizedAppIds();
            if (authorizedAppIds == null) {
                authorizedAppIds = new ArrayList<>();
            }

            // 查找用户创建的应用
            List<Application> createdApps = applicationRepository.findByCreatorId(userId, Pageable.unpaged()).getContent();

            // 查找用户被授权的应用
            List<Application> authorizedApps = new ArrayList<>();
            if (!authorizedAppIds.isEmpty()) {
                authorizedApps = applicationRepository.findByIdIn(authorizedAppIds);
            }

            // 合并应用列表，去重
            Set<String> appIdSet = new HashSet<>();
            List<Application> allApps = new ArrayList<>();

            for (Application app : createdApps) {
                if (!appIdSet.contains(app.getId())) {
                    allApps.add(app);
                    appIdSet.add(app.getId());
                }
            }

            for (Application app : authorizedApps) {
                if (!appIdSet.contains(app.getId())) {
                    allApps.add(app);
                    appIdSet.add(app.getId());
                }
            }

            // 应用过滤条件
            List<Application> filteredApps = allApps.stream()
                .filter(app -> {
                    // 状态过滤
                    if (status != null && !status.trim().isEmpty()) {
                        try {
                            ApplicationStatus appStatus = ApplicationStatus.valueOf(status.toUpperCase());
                            if (!app.getStatus().equals(appStatus)) {
                                return false;
                            }
                        } catch (IllegalArgumentException e) {
                            return false;
                        }
                    }

                    // 环境过滤
                    if (environment != null && !environment.trim().isEmpty()) {
                        if (app.getEnvironment() == null || !app.getEnvironment().equalsIgnoreCase(environment.trim())) {
                            return false;
                        }
                    }

                    // 关键词过滤
                    if (keyword != null && !keyword.trim().isEmpty()) {
                        String lowerKeyword = keyword.trim().toLowerCase();
                        if (app.getName() == null || !app.getName().toLowerCase().contains(lowerKeyword)) {
                            return false;
                        }
                    }

                    return true;
                })
                .collect(Collectors.toList());

            // 手动分页
            int start = (int) pageable.getOffset();
            int end = Math.min(start + pageable.getPageSize(), filteredApps.size());

            if (start >= filteredApps.size()) {
                return new PageImpl<>(Collections.emptyList(), pageable, filteredApps.size());
            }

            List<Application> pageContent = filteredApps.subList(start, end);
            return new PageImpl<>(pageContent, pageable, filteredApps.size());

        } catch (Exception e) {
            logger.error("用户搜索应用失败", e);
            return Page.empty();
        }
    }

    /**
     * 检查是否有多个搜索条件
     */
    private boolean hasMultipleSearchConditions(String keyword, String status, String environment) {
        int conditionCount = 0;
        if (keyword != null && !keyword.trim().isEmpty()) conditionCount++;
        if (status != null && !status.trim().isEmpty()) conditionCount++;
        if (environment != null && !environment.trim().isEmpty()) conditionCount++;
        return conditionCount > 1;
    }
}
