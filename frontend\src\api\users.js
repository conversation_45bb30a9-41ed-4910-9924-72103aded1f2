import request from './request'

export const userApi = {
  // 获取用户列表
  getUsers(params) {
    return request({
      url: '/users',
      method: 'get',
      params
    })
  },

  // 获取用户详情
  getUser(id) {
    return request({
      url: `/users/${id}`,
      method: 'get'
    })
  },

  // 创建用户
  createUser(data) {
    return request({
      url: '/users',
      method: 'post',
      data
    })
  },

  // 更新用户
  updateUser(id, data) {
    return request({
      url: `/users/${id}`,
      method: 'put',
      data
    })
  },

  // 删除用户
  deleteUser(id) {
    return request({
      url: `/users/${id}`,
      method: 'delete'
    })
  },

  // 切换用户状态
  toggleUserStatus(id) {
    return request({
      url: `/users/${id}/toggle-status`,
      method: 'post'
    })
  },

  // 修改密码
  changePassword(data) {
    return request({
      url: '/users/change-password',
      method: 'post',
      data
    })
  }
}

// 模拟用户API（备用）
export const mockUserApi = {
  // 获取用户列表
  async getUsers(params = {}) {
    await new Promise(resolve => setTimeout(resolve, 500))
    
    const mockData = [
      {
        id: 'admin-001',
        username: 'admin',
        email: '<EMAIL>',
        realName: '系统管理员',
        role: 'SUPER_ADMIN',
        status: 'ACTIVE',
        authorizedAppIds: [],
        createdAt: '2025-06-17T02:18:47.966Z',
        lastLoginTime: '2025-06-17T02:21:49.93Z'
      },
      {
        id: 'dev-001',
        username: 'developer',
        email: '<EMAIL>',
        realName: '开发者',
        role: 'DEVELOPER',
        status: 'ACTIVE',
        authorizedAppIds: ['app-001', 'app-002'],
        createdAt: '2025-06-17T02:18:48.069Z',
        lastLoginTime: null
      },
      {
        id: 'user-001',
        username: 'user',
        email: '<EMAIL>',
        realName: '普通用户',
        role: 'VIEWER',
        status: 'ACTIVE',
        authorizedAppIds: ['app-001'],
        createdAt: '2025-06-17T02:18:48.161Z',
        lastLoginTime: null
      }
    ]

    // 简单的分页处理
    const page = params.page || 1
    const size = params.size || 10
    const start = (page - 1) * size
    const end = start + size

    return {
      code: 200,
      message: '获取用户列表成功',
      data: {
        content: mockData.slice(start, end),
        totalElements: mockData.length,
        totalPages: Math.ceil(mockData.length / size),
        size: size,
        number: page - 1,
        numberOfElements: Math.min(size, mockData.length - start)
      }
    }
  },

  // 获取用户详情
  async getUser(id) {
    await new Promise(resolve => setTimeout(resolve, 300))
    
    const mockUsers = await this.getUsers()
    const user = mockUsers.data.content.find(user => user.id === id)
    
    if (user) {
      return {
        code: 200,
        message: '获取用户详情成功',
        data: user
      }
    } else {
      return {
        code: 404,
        message: '用户不存在',
        data: null
      }
    }
  },

  // 创建用户
  async createUser(data) {
    await new Promise(resolve => setTimeout(resolve, 800))
    
    const newUser = {
      id: 'user-' + Date.now(),
      username: data.username,
      email: data.email,
      realName: data.realName,
      role: data.role,
      status: 'ACTIVE',
      authorizedAppIds: data.authorizedAppIds || [],
      createdAt: new Date().toISOString(),
      lastLoginTime: null
    }

    return {
      code: 200,
      message: '用户创建成功',
      data: newUser
    }
  },

  // 更新用户
  async updateUser(id, data) {
    await new Promise(resolve => setTimeout(resolve, 600))

    // 模拟从现有用户数据中获取用户信息
    const mockUsers = await this.getUsers()
    const existingUser = mockUsers.data.content.find(user => user.id === id)

    if (!existingUser) {
      return {
        code: 404,
        message: '用户不存在',
        data: null
      }
    }

    // 合并更新数据
    const updatedUser = {
      ...existingUser,
      ...data,
      updatedAt: new Date().toISOString()
    }

    // 如果没有提供密码，则不更新密码字段
    if (!data.password) {
      delete updatedUser.password
    }

    return {
      code: 200,
      message: '用户更新成功',
      data: updatedUser
    }
  },

  // 删除用户
  async deleteUser(id) {
    await new Promise(resolve => setTimeout(resolve, 400))
    
    return {
      code: 200,
      message: '用户删除成功',
      data: null
    }
  },

  // 切换用户状态
  async toggleUserStatus(id) {
    await new Promise(resolve => setTimeout(resolve, 500))

    // 模拟从现有用户数据中获取用户信息
    const mockUsers = await this.getUsers()
    const existingUser = mockUsers.data.content.find(user => user.id === id)

    if (!existingUser) {
      return {
        code: 404,
        message: '用户不存在',
        data: null
      }
    }

    // 切换状态
    const newStatus = existingUser.status === 'ACTIVE' ? 'DISABLED' : 'ACTIVE'
    const action = newStatus === 'ACTIVE' ? '启用' : '禁用'

    return {
      code: 200,
      message: `用户${action}成功`,
      data: {
        ...existingUser,
        status: newStatus,
        updatedAt: new Date().toISOString()
      }
    }
  }
}
