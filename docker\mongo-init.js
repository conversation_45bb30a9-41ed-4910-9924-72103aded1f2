// MongoDB 初始化脚本
// 创建日志管理数据库和用户

// 切换到 logger_management 数据库
db = db.getSiblingDB('logger_management');

// 创建应用用户
db.createUser({
  user: 'logger_user',
  pwd: 'logger_password',
  roles: [
    {
      role: 'readWrite',
      db: 'logger_management'
    }
  ]
});

// 创建日志条目集合
db.createCollection('log_entries');

// 为日志条目集合创建索引
db.log_entries.createIndex({ "timestamp": -1 });
db.log_entries.createIndex({ "level": 1 });
db.log_entries.createIndex({ "source": 1 });
db.log_entries.createIndex({ "level": 1, "timestamp": -1 });
db.log_entries.createIndex({ "source": 1, "timestamp": -1 });

// 创建复合索引用于复杂查询
db.log_entries.createIndex({ 
  "level": 1, 
  "source": 1, 
  "timestamp": -1 
});

// 为消息字段创建文本索引，支持全文搜索
db.log_entries.createIndex({ 
  "message": "text",
  "exception": "text"
});

print('MongoDB 初始化完成');
print('数据库: logger_management');
print('用户: logger_user');
print('集合: log_entries');
print('索引已创建完成');
