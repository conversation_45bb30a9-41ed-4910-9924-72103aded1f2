@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: 通用日志管理系统 - 文件传输脚本 (Windows to Linux)
:: 作者: Logger Management System
:: 版本: 1.0.0

echo ==========================================
echo 通用日志管理系统 - 文件传输脚本
echo ==========================================

:: 检查配置文件
if not exist "%~dp0deploy-config.txt" (
    echo [ERROR] 配置文件不存在: deploy-config.txt
    echo [INFO] 请先创建配置文件，参考 deploy-config.example.txt
    pause
    exit /b 1
)

:: 读取配置文件
for /f "tokens=1,2 delims==" %%a in (%~dp0deploy-config.txt) do (
    if "%%a"=="SERVER_HOST" set SERVER_HOST=%%b
    if "%%a"=="SERVER_USER" set SERVER_USER=%%b
    if "%%a"=="SERVER_PORT" set SERVER_PORT=%%b
    if "%%a"=="DEPLOY_PATH" set DEPLOY_PATH=%%b
    if "%%a"=="SSH_KEY_PATH" set SSH_KEY_PATH=%%b
)

:: 设置默认值
if not defined SERVER_PORT set SERVER_PORT=22
if not defined DEPLOY_PATH set DEPLOY_PATH=/home/<USER>/logger-management

:: 验证必要配置
if not defined SERVER_HOST (
    echo [ERROR] 请在配置文件中设置 SERVER_HOST
    pause
    exit /b 1
)

if not defined SERVER_USER (
    echo [ERROR] 请在配置文件中设置 SERVER_USER
    pause
    exit /b 1
)

echo [INFO] 服务器配置:
echo   主机: %SERVER_HOST%
echo   用户: %SERVER_USER%
echo   端口: %SERVER_PORT%
echo   路径: %DEPLOY_PATH%

:: 查找最新的源码包
echo [INFO] 查找源码包...
set PACKAGE_FILE=
for /f "delims=" %%i in ('dir /b /o-d "%~dp0logger-management-*.zip" 2^>nul') do (
    set PACKAGE_FILE=%%i
    goto :found_package
)

:found_package
if not defined PACKAGE_FILE (
    echo [ERROR] 未找到源码包文件
    echo [INFO] 请先运行 package-source.bat 创建源码包
    pause
    exit /b 1
)

echo [INFO] 找到源码包: %PACKAGE_FILE%

:: 检查传输工具
set TRANSFER_METHOD=
if exist "C:\Program Files\PuTTY\pscp.exe" (
    set TRANSFER_METHOD=PSCP
    set PSCP_PATH="C:\Program Files\PuTTY\pscp.exe"
) else if exist "C:\Program Files (x86)\PuTTY\pscp.exe" (
    set TRANSFER_METHOD=PSCP
    set PSCP_PATH="C:\Program Files (x86)\PuTTY\pscp.exe"
) else (
    where scp >nul 2>&1
    if !errorlevel! equ 0 (
        set TRANSFER_METHOD=SCP
    ) else (
        echo [ERROR] 未找到文件传输工具
        echo [INFO] 请安装以下工具之一:
        echo   - PuTTY (推荐): https://www.putty.org/
        echo   - OpenSSH Client (Windows 10+)
        echo   - Git for Windows (包含SSH工具)
        pause
        exit /b 1
    )
)

echo [INFO] 使用传输方法: %TRANSFER_METHOD%

:: 构建SSH连接参数
set SSH_PARAMS=-P %SERVER_PORT%
if defined SSH_KEY_PATH (
    if exist "%SSH_KEY_PATH%" (
        set SSH_PARAMS=%SSH_PARAMS% -i "%SSH_KEY_PATH%"
        echo [INFO] 使用SSH密钥: %SSH_KEY_PATH%
    ) else (
        echo [WARNING] SSH密钥文件不存在: %SSH_KEY_PATH%
        echo [INFO] 将使用密码认证
    )
)

:: 开始传输
echo [INFO] 开始传输文件...
echo [INFO] 这可能需要几分钟时间，请耐心等待...

if "%TRANSFER_METHOD%"=="PSCP" (
    :: 使用PuTTY的pscp
    %PSCP_PATH% %SSH_PARAMS% "%~dp0%PACKAGE_FILE%" %SERVER_USER%@%SERVER_HOST%:%DEPLOY_PATH%/
) else (
    :: 使用标准scp
    if defined SSH_KEY_PATH (
        scp -P %SERVER_PORT% -i "%SSH_KEY_PATH%" "%~dp0%PACKAGE_FILE%" %SERVER_USER%@%SERVER_HOST%:%DEPLOY_PATH%/
    ) else (
        scp -P %SERVER_PORT% "%~dp0%PACKAGE_FILE%" %SERVER_USER%@%SERVER_HOST%:%DEPLOY_PATH%/
    )
)

if errorlevel 1 (
    echo [ERROR] 文件传输失败
    echo [INFO] 请检查:
    echo   1. 网络连接是否正常
    echo   2. 服务器地址和端口是否正确
    echo   3. 用户名和认证信息是否正确
    echo   4. 目标目录是否存在且有写入权限
    pause
    exit /b 1
)

echo [SUCCESS] 文件传输完成！

:: 询问是否自动解压和部署
echo.
set /p AUTO_DEPLOY="是否自动解压并部署? (y/N): "
if /i "!AUTO_DEPLOY!"=="y" (
    echo [INFO] 连接到服务器执行部署...
    
    :: 构建SSH命令
    set SSH_CMD=
    if "%TRANSFER_METHOD%"=="PSCP" (
        if exist "C:\Program Files\PuTTY\plink.exe" (
            set SSH_CMD="C:\Program Files\PuTTY\plink.exe"
        ) else if exist "C:\Program Files (x86)\PuTTY\plink.exe" (
            set SSH_CMD="C:\Program Files (x86)\PuTTY\plink.exe"
        )
    ) else (
        where ssh >nul 2>&1
        if !errorlevel! equ 0 set SSH_CMD=ssh
    )
    
    if defined SSH_CMD (
        :: 执行远程部署命令
        if defined SSH_KEY_PATH (
            %SSH_CMD% %SSH_PARAMS% %SERVER_USER%@%SERVER_HOST% "cd %DEPLOY_PATH% && unzip -o %PACKAGE_FILE% && chmod +x scripts/deploy-on-linux.sh && ./scripts/deploy-on-linux.sh"
        ) else (
            %SSH_CMD% -p %SERVER_PORT% %SERVER_USER%@%SERVER_HOST% "cd %DEPLOY_PATH% && unzip -o %PACKAGE_FILE% && chmod +x scripts/deploy-on-linux.sh && ./scripts/deploy-on-linux.sh"
        )
        
        if !errorlevel! equ 0 (
            echo [SUCCESS] 自动部署完成！
        ) else (
            echo [WARNING] 自动部署失败，请手动登录服务器执行部署
        )
    ) else (
        echo [WARNING] 未找到SSH客户端，无法自动部署
        echo [INFO] 请手动登录服务器执行以下命令:
        echo   cd %DEPLOY_PATH%
        echo   unzip -o %PACKAGE_FILE%
        echo   chmod +x scripts/deploy-on-linux.sh
        echo   ./scripts/deploy-on-linux.sh
    )
) else (
    echo [INFO] 请手动登录服务器执行以下命令:
    echo   ssh %SERVER_USER%@%SERVER_HOST%
    echo   cd %DEPLOY_PATH%
    echo   unzip -o %PACKAGE_FILE%
    echo   chmod +x scripts/deploy-on-linux.sh
    echo   ./scripts/deploy-on-linux.sh
)

echo.
echo ==========================================
echo 传输完成！
echo ==========================================
echo 源码包: %PACKAGE_FILE%
echo 目标服务器: %SERVER_USER%@%SERVER_HOST%:%DEPLOY_PATH%
echo ==========================================

pause
