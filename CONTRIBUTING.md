# 贡献指南

感谢您对通用日志管理系统项目的关注！本文档将指导您如何参与项目开发。

## 开发环境设置

### 前置要求
- Node.js 16+
- Java 8+
- Maven 3.6+
- MongoDB 5.0+
- Git

### 克隆项目
```bash
git clone <repository-url>
cd LoggerManagement
```

### 安装依赖
```bash
# 前端依赖
cd frontend
npm install

# 后端依赖（Maven会自动下载）
cd ../backend
mvn clean compile
```

## 分支策略

### 主要分支
- `main` - 主分支，包含稳定的生产代码
- `develop` - 开发分支，包含最新的开发功能

### 功能分支
- `feature/功能名称` - 新功能开发
- `bugfix/问题描述` - Bug修复
- `hotfix/紧急修复` - 生产环境紧急修复

### 分支命名规范
```bash
feature/user-authentication    # 用户认证功能
bugfix/login-validation-error  # 登录验证错误修复
hotfix/security-vulnerability  # 安全漏洞紧急修复
```

## 提交规范

### 提交信息格式
```
<类型>: <简短描述>

<详细描述>

<相关问题编号>
```

### 类型说明
- `feat`: 新功能
- `fix`: Bug修复
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建工具或依赖更新
- `perf`: 性能优化

### 示例
```
feat: 添加用户登录功能

- 实现JWT认证机制
- 添加登录表单验证
- 集成Spring Security

Closes #123
```

## 代码规范

### 前端代码规范
- 使用ES6+语法
- 组件名使用PascalCase
- 文件名使用kebab-case
- 使用Composition API
- 添加适当的注释

### 后端代码规范
- 遵循Java命名约定
- 使用Spring Boot最佳实践
- 添加适当的JavaDoc注释
- 使用统一的异常处理

### 数据库规范
- 集合名使用snake_case
- 字段名使用camelCase
- 添加适当的索引

## 测试要求

### 前端测试
- 组件单元测试
- 页面集成测试
- E2E测试（关键流程）

### 后端测试
- 单元测试（Service层）
- 集成测试（Controller层）
- 数据库测试

## Pull Request流程

1. **创建功能分支**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **开发和测试**
   - 编写代码
   - 添加测试
   - 确保所有测试通过

3. **提交代码**
   ```bash
   git add .
   git commit -m "feat: 添加新功能"
   ```

4. **推送分支**
   ```bash
   git push origin feature/your-feature-name
   ```

5. **创建Pull Request**
   - 填写详细的PR描述
   - 关联相关Issue
   - 请求代码审查

6. **代码审查**
   - 响应审查意见
   - 修改代码
   - 更新PR

7. **合并代码**
   - 审查通过后合并到develop分支
   - 删除功能分支

## 发布流程

1. **准备发布**
   ```bash
   git checkout develop
   git pull origin develop
   git checkout -b release/v1.0.0
   ```

2. **版本更新**
   - 更新版本号
   - 更新CHANGELOG
   - 最终测试

3. **合并到主分支**
   ```bash
   git checkout main
   git merge release/v1.0.0
   git tag v1.0.0
   git push origin main --tags
   ```

## 问题报告

### Bug报告模板
- 问题描述
- 复现步骤
- 期望行为
- 实际行为
- 环境信息
- 截图（如有）

### 功能请求模板
- 功能描述
- 使用场景
- 期望效果
- 替代方案

## 联系方式

如有任何问题，请通过以下方式联系：
- 创建Issue
- 发送邮件
- 项目讨论区

感谢您的贡献！
