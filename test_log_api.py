#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志API测试脚本
测试基于AppID和ApiKey的日志创建API
"""

import requests
import json
import time
from datetime import datetime

# API配置
BASE_URL = "http://localhost:8080/api"
LOG_API_URL = f"{BASE_URL}/api/logs"

# 测试应用信息（从数据库中获取）
TEST_APPS = [
    {
        "app_id": "app-001",
        "api_key": "1e2a828af9614708834af6f87deb091a",
        "name": "用户管理系统"
    },
    {
        "app_id": "app-002", 
        "api_key": "9397a5a38fd6448e8dba9755d3414efa",
        "name": "订单管理系统"
    },
    {
        "app_id": "app-003",
        "api_key": "ef64b3b116e04fb0be05352ce48dd7b1", 
        "name": "支付系统"
    }
]

def test_create_single_log():
    """测试创建单条日志"""
    print("=== 测试创建单条日志 ===")
    
    app = TEST_APPS[0]  # 使用第一个测试应用
    
    # 构建请求头
    headers = {
        "Content-Type": "application/json",
        "X-App-ID": app["app_id"],
        "X-Api-Key": app["api_key"]
    }
    
    # 构建日志数据
    log_data = {
        "timestamp": datetime.now().isoformat(),
        "level": "INFO",
        "message": "API测试 - 单条日志创建",
        "source": "TestScript",
        "thread": "main",
        "environment": "test",
        "environmentProperties": {
            "hostname": "test-server",
            "instanceId": "test-instance-001",
            "region": "test-region",
            "cpuUsage": "15%",
            "memoryUsage": "2GB"
        },
        "extendProperties": {
            "testType": "single_log",
            "requestId": f"test_req_{int(time.time())}",
            "userId": "test_user_001"
        }
    }
    
    try:
        response = requests.post(
            f"{LOG_API_URL}/create",
            headers=headers,
            json=log_data,
            timeout=10
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                print("✅ 单条日志创建成功")
                return True
            else:
                print(f"❌ 单条日志创建失败: {result.get('message')}")
                return False
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_create_batch_logs():
    """测试批量创建日志"""
    print("\n=== 测试批量创建日志 ===")
    
    app = TEST_APPS[1]  # 使用第二个测试应用
    
    # 构建请求头
    headers = {
        "Content-Type": "application/json",
        "X-App-ID": app["app_id"],
        "X-Api-Key": app["api_key"]
    }
    
    # 构建批量日志数据
    batch_logs = []
    for i in range(5):
        log_data = {
            "timestamp": datetime.now().isoformat(),
            "level": ["INFO", "WARN", "ERROR", "DEBUG"][i % 4],
            "message": f"API测试 - 批量日志 #{i+1}",
            "source": "TestScript",
            "thread": f"worker-{i+1}",
            "environment": "test",
            "environmentProperties": {
                "hostname": f"test-server-{i+1}",
                "instanceId": f"test-instance-{i+1:03d}",
                "region": "test-region",
                "cpuUsage": f"{15 + i*5}%",
                "memoryUsage": f"{2 + i}GB"
            },
            "extendProperties": {
                "testType": "batch_log",
                "batchIndex": i,
                "requestId": f"batch_req_{int(time.time())}_{i}",
                "userId": f"test_user_{i+1:03d}"
            }
        }
        batch_logs.append(log_data)
    
    try:
        response = requests.post(
            f"{LOG_API_URL}/batch",
            headers=headers,
            json=batch_logs,
            timeout=10
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                data = result.get("data", {})
                print(f"✅ 批量日志创建成功，成功数量: {data.get('successCount', 0)}")
                return True
            else:
                print(f"❌ 批量日志创建失败: {result.get('message')}")
                return False
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_invalid_credentials():
    """测试无效凭据"""
    print("\n=== 测试无效凭据 ===")
    
    # 测试无效的AppID
    headers = {
        "Content-Type": "application/json",
        "X-App-ID": "invalid-app-id",
        "X-Api-Key": "invalid-api-key"
    }
    
    log_data = {
        "timestamp": datetime.now().isoformat(),
        "level": "INFO",
        "message": "测试无效凭据",
        "source": "TestScript"
    }
    
    try:
        response = requests.post(
            f"{LOG_API_URL}/create",
            headers=headers,
            json=log_data,
            timeout=10
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if not result.get("success"):
                print("✅ 无效凭据测试通过 - 正确拒绝了无效请求")
                return True
            else:
                print("❌ 无效凭据测试失败 - 应该拒绝无效请求")
                return False
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_missing_headers():
    """测试缺少请求头"""
    print("\n=== 测试缺少请求头 ===")
    
    # 测试缺少X-App-ID头
    headers = {
        "Content-Type": "application/json",
        "X-Api-Key": TEST_APPS[0]["api_key"]
    }
    
    log_data = {
        "timestamp": datetime.now().isoformat(),
        "level": "INFO",
        "message": "测试缺少AppID头",
        "source": "TestScript"
    }
    
    try:
        response = requests.post(
            f"{LOG_API_URL}/create",
            headers=headers,
            json=log_data,
            timeout=10
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        
        # 应该返回400错误或者认证失败
        if response.status_code == 400 or (response.status_code == 200 and not response.json().get("success")):
            print("✅ 缺少请求头测试通过 - 正确拒绝了缺少头部的请求")
            return True
        else:
            print("❌ 缺少请求头测试失败 - 应该拒绝缺少头部的请求")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试日志API...")
    print(f"API地址: {LOG_API_URL}")
    
    test_results = []
    
    # 执行各项测试
    test_results.append(("创建单条日志", test_create_single_log()))
    test_results.append(("批量创建日志", test_create_batch_logs()))
    test_results.append(("无效凭据测试", test_invalid_credentials()))
    test_results.append(("缺少请求头测试", test_missing_headers()))
    
    # 输出测试结果
    print("\n" + "="*50)
    print("测试结果汇总:")
    print("="*50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！")
    else:
        print("⚠️  有测试失败，请检查API实现")

if __name__ == "__main__":
    main()
