@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ========================================
echo    日志管理系统模拟器
echo ========================================
echo.

REM 检查.NET是否安装
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo [错误] 未找到 .NET SDK，请先安装 .NET 9.0 SDK
    echo 下载地址: https://dotnet.microsoft.com/download
    pause
    exit /b 1
)

echo [信息] 检测到 .NET SDK 版本:
dotnet --version

echo.
echo 请选择运行模式:
echo 1. 交互模式 (推荐)
echo 2. 发送单条日志
echo 3. 批量发送日志 (100条)
echo 4. 连续发送日志
echo 5. 测试API连接
echo 6. 显示配置信息
echo 7. 自定义批量发送
echo 8. 构建项目
echo 9. 退出
echo.

set /p choice="请输入选项 (1-9): "

if "%choice%"=="1" goto interactive
if "%choice%"=="2" goto single
if "%choice%"=="3" goto batch
if "%choice%"=="4" goto continuous
if "%choice%"=="5" goto test
if "%choice%"=="6" goto config
if "%choice%"=="7" goto custom_batch
if "%choice%"=="8" goto build
if "%choice%"=="9" goto exit
goto invalid

:interactive
echo.
echo [信息] 启动交互模式...
dotnet run
goto end

:single
echo.
echo [信息] 发送单条日志...
dotnet run -- single
goto end

:batch
echo.
echo [信息] 批量发送100条日志...
dotnet run -- batch --count 100 --batch-size 10
goto end

:continuous
echo.
echo [信息] 开始连续发送日志，按 Ctrl+C 停止...
dotnet run -- continuous
goto end

:test
echo.
echo [信息] 测试API连接...
dotnet run -- test
goto end

:config
echo.
echo [信息] 显示配置信息...
dotnet run -- config
goto end

:custom_batch
echo.
set /p count="请输入日志总数 (默认100): "
if "%count%"=="" set count=100

set /p batch_size="请输入批次大小 (默认10): "
if "%batch_size%"=="" set batch_size=10

set /p interval="请输入间隔时间/秒 (默认1): "
if "%interval%"=="" set interval=1

echo.
echo [信息] 批量发送 %count% 条日志，批次大小 %batch_size%，间隔 %interval% 秒...
dotnet run -- batch --count %count% --batch-size %batch_size% --interval %interval%
goto end

:build
echo.
echo [信息] 构建项目...
dotnet build
if errorlevel 1 (
    echo [错误] 构建失败
) else (
    echo [成功] 构建完成
)
goto end

:invalid
echo.
echo [错误] 无效选项，请重新选择
pause
cls
goto start

:exit
echo.
echo 退出程序
exit /b 0

:end
echo.
echo 操作完成
pause
