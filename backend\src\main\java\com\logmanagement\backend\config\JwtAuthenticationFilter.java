package com.logmanagement.backend.config;

import com.logmanagement.backend.entity.Application;
import com.logmanagement.backend.entity.ApplicationStatus;
import com.logmanagement.backend.repository.ApplicationRepository;
import com.logmanagement.backend.util.JwtUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Collections;
import java.util.Optional;

/**
 * JWT认证过滤器
 * 
 * 用于处理HTTP请求中的JWT token，验证token的有效性，
 * 并将用户信息设置到Spring Security的上下文中
 * 
 * <AUTHOR> Management System
 * @version 1.0.0
 */
@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    private static final Logger logger = LoggerFactory.getLogger(JwtAuthenticationFilter.class);

    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private ApplicationRepository applicationRepository;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response,
                                  FilterChain filterChain) throws ServletException, IOException {

        String requestPath = request.getRequestURI();
        String method = request.getMethod();

        // 跳过不需要认证的路径
        if (shouldSkipAuthentication(requestPath, method)) {
            filterChain.doFilter(request, response);
            return;
        }

        // 首先检查App ID和API Key认证
        String appId = request.getHeader("X-App-ID");
        String apiKey = request.getHeader("X-Api-Key");

        if (StringUtils.hasText(appId) && StringUtils.hasText(apiKey)) {
            // 验证App ID和API Key
            if (validateAppCredentials(appId, apiKey)) {
                // 创建应用认证对象
                UsernamePasswordAuthenticationToken authentication =
                    new UsernamePasswordAuthenticationToken(
                        appId,
                        null,
                        Collections.singletonList(new SimpleGrantedAuthority("ROLE_APPLICATION"))
                    );

                authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));

                // 设置到Security上下文
                SecurityContextHolder.getContext().setAuthentication(authentication);

                logger.debug("应用认证成功: appId={}", appId);
                filterChain.doFilter(request, response);
                return;
            } else {
                logger.warn("应用认证失败: appId={}", appId);
            }
        }

        // 如果App认证失败或不存在，则尝试JWT认证
        String authHeader = request.getHeader("Authorization");

        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            String token = authHeader.substring(7);

            try {
                if (jwtUtil.validateToken(token)) {
                    String userId = jwtUtil.getUserIdFromToken(token);
                    String username = jwtUtil.getUsernameFromToken(token);
                    String role = jwtUtil.getRoleFromToken(token);

                    // 创建认证对象
                    UsernamePasswordAuthenticationToken authentication =
                        new UsernamePasswordAuthenticationToken(
                            userId,
                            null,
                            Collections.singletonList(new SimpleGrantedAuthority("ROLE_" + role))
                        );

                    authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));

                    // 设置到Security上下文
                    SecurityContextHolder.getContext().setAuthentication(authentication);

                    logger.debug("JWT认证成功: userId={}, username={}, role={}", userId, username, role);
                } else {
                    logger.warn("JWT token验证失败: {}", token.substring(0, Math.min(20, token.length())) + "...");
                }
            } catch (Exception e) {
                logger.error("JWT token处理失败", e);
            }
        } else {
            logger.debug("请求未包含有效的Authorization头或App认证信息: {}", requestPath);
        }

        filterChain.doFilter(request, response);
    }

    /**
     * 判断是否应该跳过认证
     */
    private boolean shouldSkipAuthentication(String requestPath, String method) {
        // OPTIONS请求跳过认证
        if ("OPTIONS".equalsIgnoreCase(method)) {
            return true;
        }
        
        // 认证相关接口跳过认证
        if (requestPath.startsWith("/auth/")) {
            return true;
        }
        
        // 健康检查接口跳过认证
        if (requestPath.equals("/system/health") || requestPath.startsWith("/actuator/")) {
            return true;
        }
        
        // 系统配置接口跳过认证
        if (requestPath.equals("/system/config") || 
            requestPath.equals("/system/pagination-config") ||
            requestPath.startsWith("/config/")) {
            return true;
        }
        
        // API文档相关接口跳过认证
        if (requestPath.equals("/doc.html") ||
            requestPath.startsWith("/webjars/") ||
            requestPath.startsWith("/swagger-resources/") ||
            requestPath.startsWith("/v2/api-docs/") ||
            requestPath.equals("/favicon.ico")) {
            return true;
        }
        
        // 错误页面跳过认证
        if (requestPath.equals("/error")) {
            return true;
        }
        
        return false;
    }

    /**
     * 验证应用凭据
     *
     * @param appId 应用ID
     * @param apiKey API密钥
     * @return 是否验证成功
     */
    private boolean validateAppCredentials(String appId, String apiKey) {
        try {
            // 根据应用ID查找应用
            Optional<Application> applicationOpt = applicationRepository.findById(appId);

            if (applicationOpt.isPresent()) {
                Application application = applicationOpt.get();

                // 检查应用状态是否为活跃
                if (application.getStatus() != ApplicationStatus.ACTIVE) {
                    logger.warn("应用状态不活跃: appId={}, status={}", appId, application.getStatus());
                    return false;
                }

                // 验证API Key（token字段）
                if (apiKey.equals(application.getToken())) {
                    logger.debug("应用凭据验证成功: appId={}", appId);
                    return true;
                } else {
                    logger.warn("API Key验证失败: appId={}", appId);
                    return false;
                }
            } else {
                logger.warn("应用不存在: appId={}", appId);
                return false;
            }
        } catch (Exception e) {
            logger.error("验证应用凭据时发生异常: appId={}", appId, e);
            return false;
        }
    }
}
