@import './base.css';

#app {
  font-weight: normal;
}

a,
.green {
  text-decoration: none;
  color: hsla(160, 100%, 37%, 1);
  transition: 0.4s;
  padding: 3px;
}

@media (hover: hover) {
  a:hover {
    background-color: hsla(160, 100%, 37%, 0.2);
  }
}

/* 全局表格样式优化 */
.el-table th .cell {
  font-weight: bold !important;
}

.el-table th {
  font-weight: bold !important;
  padding: 6px 0 !important;
}

.el-table td {
  padding: 6px 0 !important;
}

/* 表格内容行高优化 */
.el-table .cell {
  line-height: 1.4 !important;
}

/* 时间列样式优化 */
.time-column .cell {
  white-space: nowrap !important;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace !important;
  font-size: 12px !important;
  min-width: 200px !important;
}

/* 移除了会影响应用布局的响应式样式 */
