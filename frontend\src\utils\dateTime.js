/**
 * 日期时间格式化工具函数
 */

/**
 * 格式化时间为 yyyy-MM-dd HH:mm:ss.fff 格式
 * @param {string|number|Date} timestamp - 时间戳、日期字符串或Date对象
 * @returns {string} 格式化后的时间字符串
 */
export const formatTime = (timestamp) => {
  if (!timestamp) return '-'
  
  let date
  if (timestamp instanceof Date) {
    date = timestamp
  } else if (typeof timestamp === 'string') {
    // 处理各种可能的日期字符串格式
    date = new Date(timestamp)
  } else if (typeof timestamp === 'number') {
    // 处理时间戳（毫秒或秒）
    date = new Date(timestamp < 10000000000 ? timestamp * 1000 : timestamp)
  } else {
    return '-'
  }
  
  // 检查日期是否有效
  if (isNaN(date.getTime())) {
    return '-'
  }
  
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  const milliseconds = String(date.getMilliseconds()).padStart(3, '0')
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}`
}

/**
 * 格式化日期为 yyyy-MM-dd 格式
 * @param {string|number|Date} timestamp - 时间戳、日期字符串或Date对象
 * @returns {string} 格式化后的日期字符串
 */
export const formatDate = (timestamp) => {
  if (!timestamp) return '-'
  
  let date
  if (timestamp instanceof Date) {
    date = timestamp
  } else if (typeof timestamp === 'string') {
    date = new Date(timestamp)
  } else if (typeof timestamp === 'number') {
    date = new Date(timestamp < 10000000000 ? timestamp * 1000 : timestamp)
  } else {
    return '-'
  }
  
  if (isNaN(date.getTime())) {
    return '-'
  }
  
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  
  return `${year}-${month}-${day}`
}

/**
 * 格式化时间为 HH:mm:ss.fff 格式
 * @param {string|number|Date} timestamp - 时间戳、日期字符串或Date对象
 * @returns {string} 格式化后的时间字符串
 */
export const formatTimeOnly = (timestamp) => {
  if (!timestamp) return '-'
  
  let date
  if (timestamp instanceof Date) {
    date = timestamp
  } else if (typeof timestamp === 'string') {
    date = new Date(timestamp)
  } else if (typeof timestamp === 'number') {
    date = new Date(timestamp < 10000000000 ? timestamp * 1000 : timestamp)
  } else {
    return '-'
  }
  
  if (isNaN(date.getTime())) {
    return '-'
  }
  
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  const milliseconds = String(date.getMilliseconds()).padStart(3, '0')
  
  return `${hours}:${minutes}:${seconds}.${milliseconds}`
}

/**
 * 获取相对时间描述（如：2分钟前、1小时前等）
 * @param {string|number|Date} timestamp - 时间戳、日期字符串或Date对象
 * @returns {string} 相对时间描述
 */
export const getRelativeTime = (timestamp) => {
  if (!timestamp) return '-'
  
  let date
  if (timestamp instanceof Date) {
    date = timestamp
  } else if (typeof timestamp === 'string') {
    date = new Date(timestamp)
  } else if (typeof timestamp === 'number') {
    date = new Date(timestamp < 10000000000 ? timestamp * 1000 : timestamp)
  } else {
    return '-'
  }
  
  if (isNaN(date.getTime())) {
    return '-'
  }
  
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  if (diff < 1000) {
    return '刚刚'
  } else if (diff < 60000) {
    return `${Math.floor(diff / 1000)}秒前`
  } else if (diff < 3600000) {
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) {
    return `${Math.floor(diff / 3600000)}小时前`
  } else if (diff < 2592000000) {
    return `${Math.floor(diff / 86400000)}天前`
  } else {
    return formatTime(timestamp)
  }
}
