package com.logmanagement.backend.service.impl;

import com.logmanagement.backend.dto.LogSearchRequest;
import com.logmanagement.backend.dto.LogStatsResponse;
import com.logmanagement.backend.entity.LogEntry;
import com.logmanagement.backend.entity.LogLevel;
import com.logmanagement.backend.repository.LogEntryRepository;
import com.logmanagement.backend.service.LogEntryService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.support.PageableExecutionUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 日志条目服务实现类
 * 
 * <AUTHOR> Management System
 * @version 1.0.0
 */
@Service
public class LogEntryServiceImpl implements LogEntryService {

    private static final Logger logger = LoggerFactory.getLogger(LogEntryServiceImpl.class);

    @Autowired
    private LogEntryRepository logEntryRepository;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    public LogEntry save(LogEntry logEntry) {
        logger.debug("保存日志条目: {}", logEntry);
        return logEntryRepository.save(logEntry);
    }

    @Override
    public List<LogEntry> saveAll(List<LogEntry> logEntries) {
        logger.debug("批量保存日志条目，数量: {}", logEntries.size());
        return logEntryRepository.saveAll(logEntries);
    }

    @Override
    public Optional<LogEntry> findById(String id) {
        logger.debug("根据ID查找日志条目: {}", id);
        return logEntryRepository.findById(id);
    }

    @Override
    public Page<LogEntry> findAll(LogSearchRequest request) {
        logger.debug("分页查询所有日志条目: {}", request);
        Pageable pageable = createPageable(request);

        // 如果没有任何搜索条件，返回所有日志
        if (isEmptySearchConditions(request)) {
            return logEntryRepository.findAll(pageable);
        }

        // 使用程序化查询
        return findByConditions(request, pageable);
    }

    @Override
    public Page<LogEntry> search(LogSearchRequest request) {
        logger.debug("搜索日志条目: {}", request);
        Pageable pageable = createPageable(request);
        
        // 如果没有任何搜索条件，返回所有日志
        if (isEmptySearchConditions(request)) {
            return logEntryRepository.findAll(pageable);
        }
        
        // 使用程序化查询
        return findByConditions(request, pageable);
    }

    @Override
    public LogStatsResponse getStats() {
        logger.debug("获取日志统计信息");
        
        // 总数
        long total = logEntryRepository.count();
        
        // 各级别统计
        long errorCount = logEntryRepository.countByLevel(LogLevel.ERROR);
        long warnCount = logEntryRepository.countByLevel(LogLevel.WARN);
        long infoCount = logEntryRepository.countByLevel(LogLevel.INFO);
        long debugCount = logEntryRepository.countByLevel(LogLevel.DEBUG);
        
        // 今日统计
        LocalDateTime todayStart = LocalDateTime.now().with(LocalTime.MIN);
        LocalDateTime todayEnd = LocalDateTime.now().with(LocalTime.MAX);
        long todayCount = logEntryRepository.countByTimestampBetween(todayStart, todayEnd);
        
        // 最近一小时统计
        LocalDateTime oneHourAgo = LocalDateTime.now().minusHours(1);
        LocalDateTime now = LocalDateTime.now();
        long recentHourCount = logEntryRepository.countByTimestampBetween(oneHourAgo, now);
        
        return new LogStatsResponse(total, errorCount, warnCount, infoCount,
                                   debugCount, todayCount, recentHourCount);
    }

    @Override
    public LogStatsResponse getStatsByApplicationIds(List<String> applicationIds) {
        logger.debug("根据应用ID列表获取日志统计信息: {}", applicationIds);

        if (applicationIds == null || applicationIds.isEmpty()) {
            // 如果没有授权应用，返回空统计
            return new LogStatsResponse(0L, 0L, 0L, 0L, 0L, 0L, 0L);
        }

        // 构建基础查询条件
        Criteria baseCriteria = Criteria.where("applicationId").in(applicationIds);

        // 总数
        Query totalQuery = new Query(baseCriteria);
        long total = mongoTemplate.count(totalQuery, LogEntry.class);

        // 各级别统计 - 每次都创建新的Criteria对象
        Criteria errorCriteria = Criteria.where("applicationId").in(applicationIds).and("level").is(LogLevel.ERROR);
        Query errorQuery = new Query(errorCriteria);
        long errorCount = mongoTemplate.count(errorQuery, LogEntry.class);

        Criteria warnCriteria = Criteria.where("applicationId").in(applicationIds).and("level").is(LogLevel.WARN);
        Query warnQuery = new Query(warnCriteria);
        long warnCount = mongoTemplate.count(warnQuery, LogEntry.class);

        Criteria infoCriteria = Criteria.where("applicationId").in(applicationIds).and("level").is(LogLevel.INFO);
        Query infoQuery = new Query(infoCriteria);
        long infoCount = mongoTemplate.count(infoQuery, LogEntry.class);

        Criteria debugCriteria = Criteria.where("applicationId").in(applicationIds).and("level").is(LogLevel.DEBUG);
        Query debugQuery = new Query(debugCriteria);
        long debugCount = mongoTemplate.count(debugQuery, LogEntry.class);

        // 今日统计
        LocalDateTime todayStart = LocalDateTime.now().with(LocalTime.MIN);
        LocalDateTime todayEnd = LocalDateTime.now().with(LocalTime.MAX);
        Criteria todayCriteria = Criteria.where("applicationId").in(applicationIds)
                .and("timestamp").gte(todayStart).lte(todayEnd);
        Query todayQuery = new Query(todayCriteria);
        long todayCount = mongoTemplate.count(todayQuery, LogEntry.class);

        // 最近一小时统计
        LocalDateTime oneHourAgo = LocalDateTime.now().minusHours(1);
        LocalDateTime now = LocalDateTime.now();
        Criteria recentCriteria = Criteria.where("applicationId").in(applicationIds)
                .and("timestamp").gte(oneHourAgo).lte(now);
        Query recentQuery = new Query(recentCriteria);
        long recentHourCount = mongoTemplate.count(recentQuery, LogEntry.class);

        return new LogStatsResponse(total, errorCount, warnCount, infoCount,
                                   debugCount, todayCount, recentHourCount);
    }

    @Override
    public List<String> getAllSources() {
        logger.debug("获取所有日志来源");
        return logEntryRepository.findDistinctSources()
                .stream()
                .map(LogEntry::getSource)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public boolean deleteById(String id) {
        logger.debug("根据ID删除日志条目: {}", id);
        if (logEntryRepository.existsById(id)) {
            logEntryRepository.deleteById(id);
            return true;
        }
        return false;
    }

    @Override
    public long deleteByIds(List<String> ids) {
        logger.debug("批量删除日志条目，ID数量: {}", ids.size());
        long deletedCount = 0;
        for (String id : ids) {
            if (logEntryRepository.existsById(id)) {
                logEntryRepository.deleteById(id);
                deletedCount++;
            }
        }
        return deletedCount;
    }

    @Override
    public long deleteAll() {
        logger.debug("删除所有日志条目");
        long count = logEntryRepository.count();
        logEntryRepository.deleteAll();
        return count;
    }

    @Override
    public long cleanupOldLogs(int days) {
        logger.debug("清理{}天前的日志", days);
        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(days);
        return logEntryRepository.deleteByTimestampBefore(cutoffTime);
    }

    @Override
    public long count() {
        return logEntryRepository.count();
    }

    /**
     * 创建分页对象
     */
    private Pageable createPageable(LogSearchRequest request) {
        Sort sort = Sort.by(
            "desc".equalsIgnoreCase(request.getOrder()) ? 
                Sort.Direction.DESC : Sort.Direction.ASC,
            request.getSort()
        );
        return PageRequest.of(request.getPage() - 1, request.getSize(), sort);
    }

    /**
     * 检查是否为空搜索条件
     */
    private boolean isEmptySearchConditions(LogSearchRequest request) {
        return request.getLevel() == null &&
               StringUtils.isBlank(request.getSource()) &&
               StringUtils.isBlank(request.getApplicationId()) &&
               StringUtils.isBlank(request.getEnvironment()) &&
               request.getStartTime() == null &&
               request.getEndTime() == null &&
               StringUtils.isBlank(request.getKeyword()) &&
               StringUtils.isBlank(request.getThread()) &&
               (request.getApplicationIds() == null || request.getApplicationIds().isEmpty());
    }

    /**
     * 使用程序化查询构建条件
     */
    private Page<LogEntry> findByConditions(LogSearchRequest request, Pageable pageable) {
        Query query = new Query();

        // 构建查询条件
        if (request.getLevel() != null) {
            query.addCriteria(Criteria.where("level").is(request.getLevel()));
        }

        if (StringUtils.isNotBlank(request.getSource())) {
            query.addCriteria(Criteria.where("source").is(request.getSource()));
        }



        // 处理应用ID过滤逻辑
        if (StringUtils.isNotBlank(request.getApplicationId())) {
            // 如果指定了单个应用ID，需要检查权限
            if (request.getApplicationIds() != null && !request.getApplicationIds().isEmpty()) {
                // 有权限控制，需要确保指定的应用ID在权限范围内
                if (request.getApplicationIds().contains(request.getApplicationId())) {
                    query.addCriteria(Criteria.where("applicationId").is(request.getApplicationId()));
                } else {
                    // 指定的应用ID不在权限范围内，返回空结果
                    query.addCriteria(Criteria.where("applicationId").is("__NO_PERMISSION__"));
                }
            } else {
                // 没有权限控制（管理员），直接使用指定的应用ID
                query.addCriteria(Criteria.where("applicationId").is(request.getApplicationId()));
            }
        } else if (request.getApplicationIds() != null && !request.getApplicationIds().isEmpty()) {
            // 没有指定单个应用ID，但有应用ID列表（权限控制），则使用应用ID列表
            query.addCriteria(Criteria.where("applicationId").in(request.getApplicationIds()));
        }

        if (StringUtils.isNotBlank(request.getEnvironment())) {
            query.addCriteria(Criteria.where("environment").is(request.getEnvironment()));
        }

        // 处理时间范围查询 - 需要合并为一个条件避免重复字段错误
        if (request.getStartTime() != null || request.getEndTime() != null) {
            Criteria timestampCriteria = Criteria.where("timestamp");
            if (request.getStartTime() != null) {
                timestampCriteria = timestampCriteria.gte(request.getStartTime());
            }
            if (request.getEndTime() != null) {
                timestampCriteria = timestampCriteria.lte(request.getEndTime());
            }
            query.addCriteria(timestampCriteria);
        }

        if (StringUtils.isNotBlank(request.getKeyword())) {
            query.addCriteria(Criteria.where("message").regex(request.getKeyword(), "i"));
        }

        if (StringUtils.isNotBlank(request.getThread())) {
            query.addCriteria(Criteria.where("thread").is(request.getThread()));
        }

        // 处理属性搜索
        logger.debug("检查属性搜索参数: propertySearch='{}', propertyType='{}'", request.getPropertySearch(), request.getPropertyType());
        if (StringUtils.isNotBlank(request.getPropertySearch())) {
            logger.info("执行属性搜索: 关键词='{}', 类型='{}'", request.getPropertySearch(), request.getPropertyType());
            addPropertySearchCriteria(query, request.getPropertySearch(), request.getPropertyType());
        } else {
            logger.debug("属性搜索参数为空，跳过属性搜索");
        }

        // 添加分页和排序
        query.with(pageable);

        // 执行查询
        List<LogEntry> logEntries = mongoTemplate.find(query, LogEntry.class);

        // 获取总数
        long total = mongoTemplate.count(query.skip(0).limit(0), LogEntry.class);

        return PageableExecutionUtils.getPage(logEntries, pageable, () -> total);
    }

    /**
     * 添加属性搜索条件
     *
     * @param query 查询对象
     * @param searchTerm 搜索关键词
     * @param propertyType 属性类型 (all, extend, environment, metadata)
     */
    private void addPropertySearchCriteria(Query query, String searchTerm, String propertyType) {
        if (StringUtils.isBlank(searchTerm)) {
            return;
        }

        logger.debug("添加属性搜索条件: searchTerm='{}', propertyType='{}'", searchTerm, propertyType);

        // 创建属性搜索条件列表
        List<Criteria> propertyCriteriaList = new ArrayList<>();

        // 根据属性类型决定搜索范围
        if ("all".equals(propertyType) || StringUtils.isBlank(propertyType)) {
            // 搜索所有属性类型
            propertyCriteriaList.add(createPropertySearchCriteria("extendProperties", searchTerm));
            propertyCriteriaList.add(createPropertySearchCriteria("environmentProperties", searchTerm));
            propertyCriteriaList.add(createPropertySearchCriteria("metadata", searchTerm));
        } else if ("extend".equals(propertyType)) {
            // 只搜索扩展属性
            propertyCriteriaList.add(createPropertySearchCriteria("extendProperties", searchTerm));
        } else if ("environment".equals(propertyType)) {
            // 只搜索环境属性
            propertyCriteriaList.add(createPropertySearchCriteria("environmentProperties", searchTerm));
        } else if ("metadata".equals(propertyType)) {
            // 只搜索元数据
            propertyCriteriaList.add(createPropertySearchCriteria("metadata", searchTerm));
        }

        // 如果有搜索条件，使用OR连接
        if (!propertyCriteriaList.isEmpty()) {
            Criteria propertyCriteria = new Criteria().orOperator(propertyCriteriaList.toArray(new Criteria[0]));
            query.addCriteria(propertyCriteria);
        }
    }

    /**
     * 创建单个属性字段的搜索条件
     * 确保只在指定的属性字段中搜索，不会跨字段匹配
     *
     * @param fieldName 字段名称
     * @param searchTerm 搜索关键词
     * @return 搜索条件
     */
    private Criteria createPropertySearchCriteria(String fieldName, String searchTerm) {
        logger.debug("为字段 '{}' 创建搜索条件，搜索词: '{}'", fieldName, searchTerm);

        // 转义搜索词中的特殊字符
        String escapedSearchTerm = Pattern.quote(searchTerm);
        String regex = ".*" + escapedSearchTerm + ".*";

        // 创建严格的搜索条件：
        // 1. 字段必须存在
        // 2. 字段不能为null
        // 3. 字段不能为空字符串
        // 4. 字段内容必须匹配搜索词
        return new Criteria().andOperator(
            Criteria.where(fieldName).exists(true),     // 字段必须存在
            Criteria.where(fieldName).ne(null),         // 字段不能为null
            Criteria.where(fieldName).ne(""),           // 字段不能为空字符串
            Criteria.where(fieldName).ne("{}"),         // 字段不能为空对象字符串
            Criteria.where(fieldName).regex(regex, "i") // 字段内容匹配搜索词（不区分大小写）
        );
    }
}
