<template>
  <!-- 右下角通知容器 -->
  <teleport to="body">
    <div class="notification-container">
      <transition-group name="notification" tag="div">
        <div
          v-for="notification in notifications"
          :key="notification.id"
          :class="[
            'notification-item',
            `notification-${notification.type}`,
            { 'notification-closable': notification.closable }
          ]"
          @click="handleNotificationClick(notification)"
        >
          <div class="notification-icon">
            <el-icon :size="20">
              <component :is="getIcon(notification.type)" />
            </el-icon>
          </div>
          
          <div class="notification-content">
            <div v-if="notification.title" class="notification-title">
              {{ notification.title }}
            </div>
            <div class="notification-message">
              {{ notification.message }}
            </div>
            <div v-if="notification.timestamp" class="notification-time">
              {{ formatTime(notification.timestamp) }}
            </div>
          </div>
          
          <div v-if="notification.closable" class="notification-close" @click.stop="closeNotification(notification.id)">
            <el-icon :size="16">
              <Close />
            </el-icon>
          </div>
          
          <!-- 进度条 -->
          <div v-if="notification.duration > 0" class="notification-progress">
            <div 
              class="notification-progress-bar"
              :style="{ animationDuration: notification.duration + 'ms' }"
            ></div>
          </div>
        </div>
      </transition-group>
    </div>
  </teleport>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import {
  CircleCheckFilled,
  WarningFilled,
  CircleCloseFilled,
  InfoFilled,
  Close
} from '@element-plus/icons-vue'

// 通知列表
const notifications = ref([])

// 通知ID计数器
let notificationId = 0

// 获取图标
const getIcon = (type) => {
  const iconMap = {
    success: CircleCheckFilled,
    warning: WarningFilled,
    error: CircleCloseFilled,
    info: InfoFilled
  }
  return iconMap[type] || InfoFilled
}

// 格式化时间
const formatTime = (timestamp) => {
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now - date
  
  if (diff < 60000) { // 1分钟内
    return '刚刚'
  } else if (diff < 3600000) { // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) { // 24小时内
    return `${Math.floor(diff / 3600000)}小时前`
  } else {
    return date.toLocaleString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }
}

// 添加通知
const addNotification = (options) => {
  const notification = {
    id: ++notificationId,
    type: options.type || 'info',
    title: options.title,
    message: options.message || '',
    duration: options.duration !== undefined ? options.duration : 4000,
    closable: options.closable !== undefined ? options.closable : true,
    timestamp: Date.now(),
    onClick: options.onClick
  }
  
  notifications.value.push(notification)
  
  // 自动关闭
  if (notification.duration > 0) {
    setTimeout(() => {
      closeNotification(notification.id)
    }, notification.duration)
  }
  
  return notification.id
}

// 关闭通知
const closeNotification = (id) => {
  const index = notifications.value.findIndex(n => n.id === id)
  if (index > -1) {
    notifications.value.splice(index, 1)
  }
}

// 处理通知点击
const handleNotificationClick = (notification) => {
  if (notification.onClick) {
    notification.onClick()
  }
}

// 清空所有通知
const clearAll = () => {
  notifications.value = []
}

// 暴露方法给外部使用
defineExpose({
  addNotification,
  closeNotification,
  clearAll
})
</script>

<style scoped>
.notification-container {
  position: fixed;
  top: 80px;
  right: 20px;
  z-index: 9999;
  pointer-events: none;
  max-width: 400px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .notification-container {
    top: 70px;
    right: 10px;
    left: 10px;
    max-width: none;
  }
}

.notification-item {
  position: relative;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  margin-bottom: 12px;
  padding: 16px;
  display: flex;
  align-items: flex-start;
  gap: 12px;
  pointer-events: auto;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-left: 4px solid;
  min-width: 320px;
  max-width: 400px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 移动端适配 */
@media (max-width: 768px) {
  .notification-item {
    min-width: auto;
    max-width: none;
    margin-bottom: 8px;
    padding: 12px;
    border-radius: 8px;
  }
}

.notification-item:hover {
  transform: translateX(-6px) scale(1.02);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.18);
}

.notification-success {
  border-left-color: #67c23a;
}

.notification-warning {
  border-left-color: #e6a23c;
}

.notification-error {
  border-left-color: #f56c6c;
}

.notification-info {
  border-left-color: #409eff;
}

.notification-icon {
  flex-shrink: 0;
  margin-top: 2px;
}

.notification-success .notification-icon {
  color: #67c23a;
}

.notification-warning .notification-icon {
  color: #e6a23c;
}

.notification-error .notification-icon {
  color: #f56c6c;
}

.notification-info .notification-icon {
  color: #409eff;
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-title {
  font-weight: 600;
  font-size: 14px;
  color: #303133;
  margin-bottom: 4px;
  line-height: 1.4;
}

.notification-message {
  font-size: 13px;
  color: #606266;
  line-height: 1.5;
  word-break: break-word;
}

.notification-time {
  font-size: 11px;
  color: #909399;
  margin-top: 6px;
}

.notification-close {
  flex-shrink: 0;
  color: #c0c4cc;
  cursor: pointer;
  transition: color 0.3s ease;
  padding: 2px;
  border-radius: 4px;
}

.notification-close:hover {
  color: #909399;
  background-color: #f5f7fa;
}

.notification-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 0 0 8px 8px;
  overflow: hidden;
}

.notification-progress-bar {
  height: 100%;
  background-color: currentColor;
  opacity: 0.3;
  animation: progress-countdown linear forwards;
  transform-origin: left;
}

@keyframes progress-countdown {
  from {
    transform: scaleX(1);
  }
  to {
    transform: scaleX(0);
  }
}

/* 动画效果 */
.notification-enter-active {
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.notification-leave-active {
  transition: all 0.3s cubic-bezier(0.55, 0, 0.1, 1);
}

.notification-enter-from {
  transform: translateX(100%) scale(0.9);
  opacity: 0;
}

.notification-leave-to {
  transform: translateX(100%) scale(0.9);
  opacity: 0;
}

.notification-move {
  transition: transform 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}


</style>
