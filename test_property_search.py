#!/usr/bin/env python3
"""
测试属性搜索功能
"""

import requests
import json

# API配置
BASE_URL = "http://localhost:8080/api"
HEADERS = {
    "Content-Type": "application/json",
    "Authorization": "Bearer eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJhZG1pbiIsInJvbGUiOiJBRE1JTiIsImlhdCI6MTczNTEwNzQ5NCwiZXhwIjoxNzM1MTkzODk0fQ.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8"  # 使用管理员token
}

def test_property_search(property_search, property_type="all"):
    """测试属性搜索"""
    print(f"\n=== 测试属性搜索: '{property_search}' (类型: {property_type}) ===")

    params = {
        "page": 0,
        "size": 10,
        "propertySearch": property_search,
        "propertyType": property_type
    }

    try:
        response = requests.get(f"{BASE_URL}/logs", headers=HEADERS, params=params)
        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            if data.get("code") == 200:
                logs = data.get("data", {}).get("content", [])
                print(f"找到 {len(logs)} 条日志")

                # 显示前3条日志的属性信息
                for i, log in enumerate(logs[:3]):
                    print(f"\n日志 {i+1}:")
                    print(f"  消息: {log.get('message', 'N/A')}")
                    print(f"  扩展属性: {log.get('extendProperties', {})}")
                    print(f"  环境属性: {log.get('environmentProperties', {})}")
                    print(f"  元数据: {log.get('metadata', {})}")
            else:
                print(f"API错误: {data.get('message', 'Unknown error')}")
        else:
            print(f"HTTP错误: {response.text}")

    except Exception as e:
        print(f"请求异常: {e}")

def main():
    print("开始测试属性搜索功能...")

    # 测试1: 搜索版本号
    test_property_search("1.0.0", "all")

    # 测试2: 搜索环境
    test_property_search("development", "all")

    # 测试3: 搜索特定类型的属性 - 扩展属性
    test_property_search("version", "extend")

    # 测试4: 搜索特定类型的属性 - 元数据
    test_property_search("trace", "metadata")

    # 测试5: 搜索环境属性
    test_property_search("server", "environment")

    # 测试6: 搜索不存在的属性
    test_property_search("nonexistent", "all")

if __name__ == "__main__":
    main()
