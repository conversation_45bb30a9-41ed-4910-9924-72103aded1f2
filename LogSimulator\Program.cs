using LogSimulator.Services;
using LogSimulator.Tests;
using LogSimulator.Tools;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System.CommandLine;

namespace LogSimulator;

class Program
{
    static async Task<int> Main(string[] args)
    {
        // 创建主机构建器
        var hostBuilder = Host.CreateDefaultBuilder(args)
            .ConfigureAppConfiguration((context, config) =>
            {
                config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
                config.AddEnvironmentVariables();
                config.AddCommandLine(args);
            })
            .ConfigureServices((context, services) =>
            {
                // 注册服务
                services.AddHttpClient<LogApiService>();
                services.AddSingleton<LogGeneratorService>();
                services.AddSingleton<LogSimulatorService>();
                
                // 配置日志
                services.AddLogging(builder =>
                {
                    builder.AddConsole();
                    builder.SetMinimumLevel(LogLevel.Information);
                });
            });

        var host = hostBuilder.Build();

        // 创建命令行接口
        var rootCommand = new RootCommand("日志管理系统模拟器 - 用于测试日志API的控制台应用程序");

        // 单次发送命令
        var singleCommand = new Command("single", "发送单条日志")
        {
            new Option<string?>("--level", "日志级别 (INFO, WARN, ERROR, DEBUG)"),
            new Option<string?>("--message", "自定义日志消息")
        };
        singleCommand.SetHandler(async (string? level, string? message) =>
        {
            await RunSingleCommand(host, level, message);
        }, singleCommand.Options[0] as Option<string?>, singleCommand.Options[1] as Option<string?>);

        // 批量发送命令
        var batchCommand = new Command("batch", "批量发送日志")
        {
            new Option<int>("--count", () => 100, "日志总数"),
            new Option<int>("--batch-size", () => 10, "批次大小"),
            new Option<int>("--interval", () => 1, "批次间隔时间(秒)")
        };
        batchCommand.SetHandler(async (int count, int batchSize, int interval) =>
        {
            await RunBatchCommand(host, count, batchSize, interval);
        }, batchCommand.Options[0] as Option<int>, batchCommand.Options[1] as Option<int>, batchCommand.Options[2] as Option<int>);

        // 连续发送命令
        var continuousCommand = new Command("continuous", "连续发送日志 (按 Ctrl+C 停止)")
        {
            new Option<int>("--batch-size", () => 10, "批次大小"),
            new Option<int>("--interval", () => 5, "发送间隔时间(秒)")
        };
        continuousCommand.SetHandler(async (int batchSize, int interval) =>
        {
            await RunContinuousCommand(host, batchSize, interval);
        }, continuousCommand.Options[0] as Option<int>, continuousCommand.Options[1] as Option<int>);

        // 测试连接命令
        var testCommand = new Command("test", "测试API连接");
        testCommand.SetHandler(async () =>
        {
            await RunTestCommand(host);
        });

        // 配置信息命令
        var configCommand = new Command("config", "显示配置信息");
        configCommand.SetHandler(async () =>
        {
            await RunConfigCommand(host);
        });

        // 交互模式命令
        var interactiveCommand = new Command("interactive", "进入交互模式");
        interactiveCommand.SetHandler(async () =>
        {
            await RunInteractiveMode(host);
        });

        // 数据库查询命令
        var dbCommand = new Command("db", "查询数据库中的应用信息");
        dbCommand.SetHandler(async () =>
        {
            await RunDatabaseCommand();
        });

        // 添加命令到根命令
        rootCommand.AddCommand(singleCommand);
        rootCommand.AddCommand(batchCommand);
        rootCommand.AddCommand(continuousCommand);
        rootCommand.AddCommand(testCommand);
        rootCommand.AddCommand(configCommand);
        rootCommand.AddCommand(interactiveCommand);
        rootCommand.AddCommand(dbCommand);

        // 如果没有参数，显示帮助信息
        if (args.Length == 0)
        {
            Console.WriteLine("日志管理系统模拟器");
            Console.WriteLine("==================");
            Console.WriteLine();
            Console.WriteLine("使用方法:");
            Console.WriteLine("  LogSimulator <command> [options]");
            Console.WriteLine();
            Console.WriteLine("可用命令:");
            Console.WriteLine("  single       发送单条日志");
            Console.WriteLine("  batch        批量发送日志");
            Console.WriteLine("  continuous   连续发送日志");
            Console.WriteLine("  test         测试API连接");
            Console.WriteLine("  config       显示配置信息");
            Console.WriteLine("  interactive  进入交互模式");
            Console.WriteLine("  db           查询数据库应用信息");
            Console.WriteLine();
            Console.WriteLine("使用 'LogSimulator <command> --help' 查看具体命令的帮助信息");
            Console.WriteLine();
            
            // 自动进入交互模式
            await RunInteractiveMode(host);
            return 0;
        }

        // 执行命令
        return await rootCommand.InvokeAsync(args);
    }

    static async Task RunSingleCommand(IHost host, string? level, string? message)
    {
        using var scope = host.Services.CreateScope();
        var simulator = scope.ServiceProvider.GetRequiredService<LogSimulatorService>();
        var logger = scope.ServiceProvider.GetRequiredService<ILogger<Program>>();

        logger.LogInformation("执行单次日志发送命令");
        
        var success = await simulator.RunSingleSimulationAsync();
        Environment.ExitCode = success ? 0 : 1;
    }

    static async Task RunBatchCommand(IHost host, int count, int batchSize, int interval)
    {
        using var scope = host.Services.CreateScope();
        var simulator = scope.ServiceProvider.GetRequiredService<LogSimulatorService>();
        var logger = scope.ServiceProvider.GetRequiredService<ILogger<Program>>();

        logger.LogInformation("执行批量日志发送命令: 数量={Count}, 批次={BatchSize}, 间隔={Interval}秒", 
            count, batchSize, interval);

        var result = await simulator.RunBatchSimulationAsync(count, batchSize);
        
        logger.LogInformation("批量发送完成:");
        logger.LogInformation("  总数: {TotalLogs}", result.TotalLogs);
        logger.LogInformation("  成功: {SuccessfulLogs}", result.SuccessfulLogs);
        logger.LogInformation("  失败: {FailedLogs}", result.FailedLogs);
        logger.LogInformation("  成功率: {SuccessRate:F2}%", result.SuccessRate);
        logger.LogInformation("  耗时: {Duration}", result.Duration);

        Environment.ExitCode = result.FailedLogs == 0 ? 0 : 1;
    }

    static async Task RunContinuousCommand(IHost host, int batchSize, int interval)
    {
        using var scope = host.Services.CreateScope();
        var simulator = scope.ServiceProvider.GetRequiredService<LogSimulatorService>();
        var logger = scope.ServiceProvider.GetRequiredService<ILogger<Program>>();

        logger.LogInformation("执行连续日志发送命令: 批次={BatchSize}, 间隔={Interval}秒", batchSize, interval);

        // 设置取消令牌
        using var cts = new CancellationTokenSource();
        Console.CancelKeyPress += (sender, e) =>
        {
            e.Cancel = true;
            cts.Cancel();
        };

        await simulator.RunContinuousSimulationAsync(cts.Token);
    }

    static async Task RunTestCommand(IHost host)
    {
        using var scope = host.Services.CreateScope();
        var simulator = scope.ServiceProvider.GetRequiredService<LogSimulatorService>();
        var logger = scope.ServiceProvider.GetRequiredService<ILogger<Program>>();

        logger.LogInformation("执行API连接测试命令");
        
        var success = await simulator.TestApiConnectionAsync();
        Environment.ExitCode = success ? 0 : 1;
    }

    static async Task RunConfigCommand(IHost host)
    {
        using var scope = host.Services.CreateScope();
        var simulator = scope.ServiceProvider.GetRequiredService<LogSimulatorService>();

        simulator.ShowConfiguration();
        await Task.CompletedTask;
    }

    static async Task RunInteractiveMode(IHost host)
    {
        using var scope = host.Services.CreateScope();
        var simulator = scope.ServiceProvider.GetRequiredService<LogSimulatorService>();
        var logger = scope.ServiceProvider.GetRequiredService<ILogger<Program>>();

        logger.LogInformation("进入交互模式");
        Console.WriteLine();
        Console.WriteLine("=== 日志模拟器交互模式 ===");
        Console.WriteLine();

        while (true)
        {
            Console.WriteLine("请选择操作:");
            Console.WriteLine("1. 发送单条日志");
            Console.WriteLine("2. 批量发送日志");
            Console.WriteLine("3. 连续发送日志");
            Console.WriteLine("4. 测试API连接");
            Console.WriteLine("5. 显示配置信息");
            Console.WriteLine("6. 测试ApiResponse");
            Console.WriteLine("7. 退出");
            Console.Write("请输入选项 (1-7): ");

            var input = Console.ReadLine();
            Console.WriteLine();

            switch (input)
            {
                case "1":
                    await simulator.RunSingleSimulationAsync();
                    break;

                case "2":
                    Console.Write("请输入日志总数 (默认100): ");
                    var countInput = Console.ReadLine();
                    var count = int.TryParse(countInput, out var c) ? c : 100;

                    Console.Write("请输入批次大小 (默认10): ");
                    var batchInput = Console.ReadLine();
                    var batchSize = int.TryParse(batchInput, out var b) ? b : 10;

                    var result = await simulator.RunBatchSimulationAsync(count, batchSize);
                    Console.WriteLine($"批量发送完成: 成功 {result.SuccessfulLogs}/{result.TotalLogs} ({result.SuccessRate:F2}%)");
                    break;

                case "3":
                    Console.WriteLine("开始连续发送日志，按任意键停止...");
                    using (var cts = new CancellationTokenSource())
                    {
                        var task = simulator.RunContinuousSimulationAsync(cts.Token);
                        Console.ReadKey(true);
                        cts.Cancel();
                        await task;
                    }
                    break;

                case "4":
                    await simulator.TestApiConnectionAsync();
                    break;

                case "5":
                    simulator.ShowConfiguration();
                    break;

                case "6":
                    Console.WriteLine("=== 测试 ApiResponse ===");
                    ApiResponseTest.TestApiResponseSerialization();
                    Console.WriteLine();
                    ApiResponseTest.TestBackendCompatibility();
                    break;

                case "7":
                    Console.WriteLine("退出程序");
                    return;

                default:
                    Console.WriteLine("无效选项，请重新选择");
                    break;
            }

            Console.WriteLine();
            Console.WriteLine("按任意键继续...");
            Console.ReadKey(true);
            Console.Clear();
        }
    }

    static async Task RunDatabaseCommand()
    {
        Console.WriteLine("=== 数据库查询工具 ===");

        var dbHelper = new DatabaseHelper();

        while (true)
        {
            Console.WriteLine();
            Console.WriteLine("请选择操作:");
            Console.WriteLine("1. 查看所有应用");
            Console.WriteLine("2. 查找指定应用");
            Console.WriteLine("3. 创建测试应用");
            Console.WriteLine("4. 退出");
            Console.Write("请输入选项 (1-4): ");

            var input = Console.ReadLine();
            Console.WriteLine();

            switch (input)
            {
                case "1":
                    await dbHelper.ShowAllApplicationsAsync();
                    break;

                case "2":
                    Console.Write("请输入应用ID: ");
                    var appId = Console.ReadLine();
                    if (!string.IsNullOrEmpty(appId))
                    {
                        var app = await dbHelper.FindApplicationAsync(appId);
                        if (app != null)
                        {
                            Console.WriteLine($"找到应用:");
                            Console.WriteLine($"  ID: {app.Id}");
                            Console.WriteLine($"  名称: {app.Name}");
                            Console.WriteLine($"  Token: {app.Token}");
                            Console.WriteLine($"  状态: {app.Status}");
                        }
                        else
                        {
                            Console.WriteLine("未找到指定应用");
                        }
                    }
                    break;

                case "3":
                    var existingApp = await dbHelper.FindApplicationAsync("app-7ddacc3f3cef4af894ac1dbf4e8dcadf");
                    if (existingApp != null)
                    {
                        Console.WriteLine("测试应用已存在:");
                        Console.WriteLine($"  ID: {existingApp.Id}");
                        Console.WriteLine($"  Token: {existingApp.Token}");
                    }
                    else
                    {
                        await dbHelper.CreateTestApplicationAsync();
                    }
                    break;

                case "4":
                    Console.WriteLine("退出数据库查询工具");
                    return;

                default:
                    Console.WriteLine("无效选项，请重新选择");
                    break;
            }

            Console.WriteLine();
            Console.WriteLine("按任意键继续...");
            Console.ReadKey(true);
        }
    }
}
