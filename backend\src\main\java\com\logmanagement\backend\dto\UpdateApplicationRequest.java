package com.logmanagement.backend.dto;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Min;
import javax.validation.constraints.Max;

/**
 * 更新应用请求DTO
 * 
 * <AUTHOR> Management System
 * @version 1.0.0
 */
public class UpdateApplicationRequest {

    /**
     * 应用名称
     */
    @NotBlank(message = "应用名称不能为空")
    private String name;

    /**
     * 应用描述
     */
    private String description;

    /**
     * 应用状态
     */
    @NotNull(message = "应用状态不能为空")
    private String status;

    /**
     * 应用环境
     */
    private String environment;

    /**
     * 应用版本
     */
    private String version;

    /**
     * 日志保留天数
     */
    @Min(value = 1, message = "日志保留天数不能少于1天")
    @Max(value = 365, message = "日志保留天数不能超过365天")
    private Integer logRetentionDays = 30;

    // 构造函数
    public UpdateApplicationRequest() {
    }

    // Getter 和 Setter 方法
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getEnvironment() {
        return environment;
    }

    public void setEnvironment(String environment) {
        this.environment = environment;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public Integer getLogRetentionDays() {
        return logRetentionDays;
    }

    public void setLogRetentionDays(Integer logRetentionDays) {
        this.logRetentionDays = logRetentionDays;
    }

    @Override
    public String toString() {
        return "UpdateApplicationRequest{" +
                "name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", status='" + status + '\'' +
                ", environment='" + environment + '\'' +
                ", version='" + version + '\'' +
                ", logRetentionDays=" + logRetentionDays +
                '}';
    }
}
