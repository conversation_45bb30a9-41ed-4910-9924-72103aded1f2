# Jenkins自动化部署指南

本文档详细说明如何使用Jenkins进行日志管理系统的自动化部署。

## 前提条件

- Jenkins服务器已安装并运行
- Jenkins服务器可以访问Git仓库
- Jenkins服务器已安装Docker
- 目标部署服务器已配置SSH访问

## Jenkins环境准备

### 1. 安装必要的Jenkins插件

在Jenkins管理界面安装以下插件：

```
- Git Plugin
- Docker Plugin
- Docker Pipeline Plugin
- SSH Agent Plugin
- Pipeline Plugin
- Credentials Plugin
- Environment Injector Plugin
```

### 2. 配置全局工具

在 `Manage Jenkins > Global Tool Configuration` 中配置：

- **Git**: 配置Git可执行文件路径
- **Docker**: 配置Docker可执行文件路径
- **Maven**: 配置Maven安装（用于后端构建）
- **Node.js**: 配置Node.js安装（用于前端构建）

### 3. 配置凭据

在 `Manage Jenkins > Manage Credentials` 中添加：

- **Git凭据**: 用于访问代码仓库
- **Docker Registry凭据**: 用于推送镜像到Docker仓库
- **SSH凭据**: 用于访问部署服务器
- **MongoDB凭据**: 数据库连接信息

## Pipeline配置

### 1. 创建Jenkins Pipeline

创建一个新的Pipeline项目，使用以下Jenkinsfile：

```groovy
pipeline {
    agent any
    
    environment {
        // 项目配置
        PROJECT_NAME = 'log-management'
        DOCKER_REGISTRY = 'your-docker-registry.com'
        
        // 镜像标签
        IMAGE_TAG = "${BUILD_NUMBER}"
        FRONTEND_IMAGE = "${DOCKER_REGISTRY}/${PROJECT_NAME}-frontend:${IMAGE_TAG}"
        BACKEND_IMAGE = "${DOCKER_REGISTRY}/${PROJECT_NAME}-backend:${IMAGE_TAG}"
        
        // MongoDB配置
        MONGODB_HOST = credentials('mongodb-host')
        MONGODB_USERNAME = credentials('mongodb-username')
        MONGODB_PASSWORD = credentials('mongodb-password')
        MONGODB_DATABASE = 'logger_management'
        
        // 部署服务器配置
        DEPLOY_SERVER = credentials('deploy-server-host')
        SSH_CREDENTIALS = 'deploy-server-ssh'
    }
    
    stages {
        stage('Checkout') {
            steps {
                echo 'Checking out source code...'
                checkout scm
            }
        }
        
        stage('Build Backend') {
            steps {
                echo 'Building backend application...'
                dir('backend') {
                    sh '''
                        # 构建后端镜像
                        docker build -t ${BACKEND_IMAGE} .
                        
                        # 推送到镜像仓库
                        docker push ${BACKEND_IMAGE}
                    '''
                }
            }
        }
        
        stage('Build Frontend') {
            steps {
                echo 'Building frontend application...'
                dir('frontend') {
                    sh '''
                        # 构建前端镜像
                        docker build -t ${FRONTEND_IMAGE} .
                        
                        # 推送到镜像仓库
                        docker push ${FRONTEND_IMAGE}
                    '''
                }
            }
        }
        
        stage('Deploy to Server') {
            steps {
                echo 'Deploying to production server...'
                sshagent([SSH_CREDENTIALS]) {
                    sh '''
                        # 复制部署脚本到目标服务器
                        scp -o StrictHostKeyChecking=no scripts/jenkins-deploy.sh root@${DEPLOY_SERVER}:/tmp/
                        
                        # 在目标服务器上执行部署
                        ssh -o StrictHostKeyChecking=no root@${DEPLOY_SERVER} "
                            chmod +x /tmp/jenkins-deploy.sh
                            /tmp/jenkins-deploy.sh \\
                                --backend-image ${BACKEND_IMAGE} \\
                                --frontend-image ${FRONTEND_IMAGE} \\
                                --mongodb-host ${MONGODB_HOST} \\
                                --mongodb-username ${MONGODB_USERNAME} \\
                                --mongodb-password ${MONGODB_PASSWORD} \\
                                --mongodb-database ${MONGODB_DATABASE}
                        "
                    '''
                }
            }
        }
        
        stage('Health Check') {
            steps {
                echo 'Performing health check...'
                script {
                    def backendHealth = sh(
                        script: "curl -f http://${DEPLOY_SERVER}:8080/api/actuator/health",
                        returnStatus: true
                    )
                    
                    def frontendHealth = sh(
                        script: "curl -f http://${DEPLOY_SERVER}/",
                        returnStatus: true
                    )
                    
                    if (backendHealth != 0 || frontendHealth != 0) {
                        error("Health check failed!")
                    }
                }
            }
        }
    }
    
    post {
        always {
            echo 'Cleaning up...'
            sh '''
                # 清理本地镜像
                docker rmi ${BACKEND_IMAGE} || true
                docker rmi ${FRONTEND_IMAGE} || true
            '''
        }
        
        success {
            echo 'Deployment successful!'
            // 可以添加成功通知，如发送邮件或Slack消息
        }
        
        failure {
            echo 'Deployment failed!'
            // 可以添加失败通知和回滚操作
        }
    }
}
```

### 2. 创建部署脚本

创建 `scripts/jenkins-deploy.sh` 脚本：

```bash
#!/bin/bash

# Jenkins部署脚本
# 用于在目标服务器上部署应用

set -e

# 默认值
PROJECT_NAME="log-management"
NETWORK_NAME="${PROJECT_NAME}-network"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --backend-image)
            BACKEND_IMAGE="$2"
            shift 2
            ;;
        --frontend-image)
            FRONTEND_IMAGE="$2"
            shift 2
            ;;
        --mongodb-host)
            MONGODB_HOST="$2"
            shift 2
            ;;
        --mongodb-username)
            MONGODB_USERNAME="$2"
            shift 2
            ;;
        --mongodb-password)
            MONGODB_PASSWORD="$2"
            shift 2
            ;;
        --mongodb-database)
            MONGODB_DATABASE="$2"
            shift 2
            ;;
        *)
            echo "Unknown parameter: $1"
            exit 1
            ;;
    esac
done

echo "Starting deployment..."
echo "Backend Image: $BACKEND_IMAGE"
echo "Frontend Image: $FRONTEND_IMAGE"
echo "MongoDB Host: $MONGODB_HOST"

# 创建Docker网络
if ! docker network ls | grep -q "$NETWORK_NAME"; then
    echo "Creating Docker network: $NETWORK_NAME"
    docker network create "$NETWORK_NAME"
fi

# 停止并删除旧容器
echo "Stopping old containers..."
docker stop "${PROJECT_NAME}-backend" "${PROJECT_NAME}-frontend" 2>/dev/null || true
docker rm "${PROJECT_NAME}-backend" "${PROJECT_NAME}-frontend" 2>/dev/null || true

# 拉取新镜像
echo "Pulling new images..."
docker pull "$BACKEND_IMAGE"
docker pull "$FRONTEND_IMAGE"

# 部署后端
echo "Deploying backend..."
docker run -d \
    --name "${PROJECT_NAME}-backend" \
    --network "$NETWORK_NAME" \
    -p 8080:8080 \
    -e SPRING_PROFILES_ACTIVE=docker \
    -e MONGODB_HOST="$MONGODB_HOST" \
    -e MONGODB_USERNAME="$MONGODB_USERNAME" \
    -e MONGODB_PASSWORD="$MONGODB_PASSWORD" \
    -e MONGODB_DATABASE="$MONGODB_DATABASE" \
    -v backend_logs:/app/logs \
    --restart unless-stopped \
    "$BACKEND_IMAGE"

# 等待后端启动
echo "Waiting for backend to start..."
for i in {1..30}; do
    if curl -f http://localhost:8080/api/actuator/health &>/dev/null; then
        echo "Backend started successfully"
        break
    fi
    sleep 5
done

# 部署前端
echo "Deploying frontend..."
docker run -d \
    --name "${PROJECT_NAME}-frontend" \
    --network "$NETWORK_NAME" \
    -p 80:80 \
    --restart unless-stopped \
    "$FRONTEND_IMAGE"

# 等待前端启动
echo "Waiting for frontend to start..."
for i in {1..15}; do
    if curl -f http://localhost/ &>/dev/null; then
        echo "Frontend started successfully"
        break
    fi
    sleep 3
done

# 清理旧镜像
echo "Cleaning up old images..."
docker image prune -f

echo "Deployment completed successfully!"
```

## 多环境部署配置

### 1. 开发环境Pipeline

```groovy
pipeline {
    agent any
    
    environment {
        DEPLOY_ENV = 'development'
        DEPLOY_SERVER = credentials('dev-server-host')
        MONGODB_HOST = credentials('dev-mongodb-host')
    }
    
    stages {
        // ... 构建阶段相同
        
        stage('Deploy to Development') {
            when {
                branch 'develop'
            }
            steps {
                // 部署到开发环境
            }
        }
    }
}
```

### 2. 生产环境Pipeline

```groovy
pipeline {
    agent any
    
    environment {
        DEPLOY_ENV = 'production'
        DEPLOY_SERVER = credentials('prod-server-host')
        MONGODB_HOST = credentials('prod-mongodb-host')
    }
    
    stages {
        // ... 构建阶段相同
        
        stage('Deploy to Production') {
            when {
                branch 'main'
            }
            steps {
                input message: 'Deploy to production?', ok: 'Deploy'
                // 部署到生产环境
            }
        }
    }
}
```

## Docker Compose集成

### 1. 创建动态docker-compose文件

```yaml
# docker-compose.jenkins.yml.template
version: '3.8'

services:
  backend:
    image: ${BACKEND_IMAGE}
    container_name: ${PROJECT_NAME}-backend
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - MONGODB_HOST=${MONGODB_HOST}
      - MONGODB_USERNAME=${MONGODB_USERNAME}
      - MONGODB_PASSWORD=${MONGODB_PASSWORD}
      - MONGODB_DATABASE=${MONGODB_DATABASE}
    volumes:
      - backend_logs:/app/logs
    networks:
      - app-network

  frontend:
    image: ${FRONTEND_IMAGE}
    container_name: ${PROJECT_NAME}-frontend
    restart: unless-stopped
    ports:
      - "80:80"
    depends_on:
      - backend
    networks:
      - app-network

volumes:
  backend_logs:

networks:
  app-network:
    driver: bridge
```

### 2. 在Jenkins中使用

```bash
# 在部署脚本中
envsubst < docker-compose.jenkins.yml.template > docker-compose.yml
docker-compose up -d
```

## 回滚策略

### 1. 蓝绿部署

```groovy
stage('Blue-Green Deploy') {
    steps {
        script {
            // 部署到绿色环境
            sh "docker-compose -f docker-compose.green.yml up -d"
            
            // 健康检查
            def healthCheck = sh(
                script: "curl -f http://green.${DEPLOY_SERVER}:8080/api/actuator/health",
                returnStatus: true
            )
            
            if (healthCheck == 0) {
                // 切换流量到绿色环境
                sh "nginx -s reload"  // 更新负载均衡配置
                
                // 停止蓝色环境
                sh "docker-compose -f docker-compose.blue.yml down"
            } else {
                error("Green environment health check failed!")
            }
        }
    }
}
```

### 2. 快速回滚

```groovy
stage('Rollback') {
    when {
        expression { params.ROLLBACK == true }
    }
    steps {
        script {
            def previousImage = sh(
                script: "docker images --format 'table {{.Repository}}:{{.Tag}}' | grep ${PROJECT_NAME} | head -2 | tail -1",
                returnStdout: true
            ).trim()
            
            sh """
                docker stop ${PROJECT_NAME}-backend ${PROJECT_NAME}-frontend
                docker run -d --name ${PROJECT_NAME}-backend ${previousImage}
                # ... 启动前一个版本
            """
        }
    }
}
```

## 监控和通知

### 1. 集成Slack通知

```groovy
post {
    success {
        slackSend(
            channel: '#deployments',
            color: 'good',
            message: "✅ Deployment successful! \n" +
                    "Project: ${PROJECT_NAME}\n" +
                    "Build: ${BUILD_NUMBER}\n" +
                    "Frontend: http://${DEPLOY_SERVER}\n" +
                    "Backend: http://${DEPLOY_SERVER}:8080"
        )
    }

    failure {
        slackSend(
            channel: '#deployments',
            color: 'danger',
            message: "❌ Deployment failed! \n" +
                    "Project: ${PROJECT_NAME}\n" +
                    "Build: ${BUILD_NUMBER}\n" +
                    "Check: ${BUILD_URL}"
        )
    }
}
```

### 2. 邮件通知配置

```groovy
post {
    always {
        emailext(
            subject: "Deployment ${currentBuild.result}: ${PROJECT_NAME} #${BUILD_NUMBER}",
            body: """
                <h2>部署结果: ${currentBuild.result}</h2>
                <p><strong>项目:</strong> ${PROJECT_NAME}</p>
                <p><strong>构建号:</strong> ${BUILD_NUMBER}</p>
                <p><strong>分支:</strong> ${BRANCH_NAME}</p>
                <p><strong>提交:</strong> ${GIT_COMMIT}</p>
                <p><strong>构建时间:</strong> ${BUILD_TIMESTAMP}</p>
                <p><strong>构建日志:</strong> <a href="${BUILD_URL}console">${BUILD_URL}console</a></p>

                <h3>访问地址:</h3>
                <ul>
                    <li>前端: <a href="http://${DEPLOY_SERVER}">http://${DEPLOY_SERVER}</a></li>
                    <li>后端API: <a href="http://${DEPLOY_SERVER}:8080">http://${DEPLOY_SERVER}:8080</a></li>
                </ul>
            """,
            to: "${env.CHANGE_AUTHOR_EMAIL}, <EMAIL>",
            mimeType: 'text/html'
        )
    }
}
```

## 安全配置

### 1. 凭据管理

在Jenkins中配置以下凭据：

```
ID: mongodb-host
Type: Secret text
Secret: your-mongodb-host

ID: mongodb-username
Type: Secret text
Secret: your-mongodb-username

ID: mongodb-password
Type: Secret text
Secret: your-mongodb-password

ID: docker-registry-credentials
Type: Username with password
Username: your-docker-username
Password: your-docker-password

ID: deploy-server-ssh
Type: SSH Username with private key
Username: root
Private Key: [SSH私钥内容]
```

### 2. 权限控制

```groovy
pipeline {
    agent any

    options {
        // 构建保留策略
        buildDiscarder(logRotator(numToKeepStr: '10'))

        // 超时设置
        timeout(time: 30, unit: 'MINUTES')

        // 禁止并发构建
        disableConcurrentBuilds()
    }

    // 参数化构建
    parameters {
        choice(
            name: 'DEPLOY_ENV',
            choices: ['development', 'staging', 'production'],
            description: '选择部署环境'
        )

        booleanParam(
            name: 'SKIP_TESTS',
            defaultValue: false,
            description: '跳过测试'
        )

        booleanParam(
            name: 'ROLLBACK',
            defaultValue: false,
            description: '执行回滚'
        )
    }
}
```

## 性能优化

### 1. 并行构建

```groovy
stage('Build Applications') {
    parallel {
        stage('Build Backend') {
            steps {
                dir('backend') {
                    sh 'docker build -t ${BACKEND_IMAGE} .'
                }
            }
        }

        stage('Build Frontend') {
            steps {
                dir('frontend') {
                    sh 'docker build -t ${FRONTEND_IMAGE} .'
                }
            }
        }
    }
}
```

### 2. 缓存优化

```groovy
stage('Build with Cache') {
    steps {
        sh '''
            # 使用构建缓存
            docker build \
                --cache-from ${BACKEND_IMAGE}:latest \
                -t ${BACKEND_IMAGE} \
                backend/
        '''
    }
}
```

### 3. 多阶段部署

```groovy
stage('Deploy') {
    parallel {
        stage('Deploy Backend') {
            steps {
                sh '''
                    docker run -d \
                        --name ${PROJECT_NAME}-backend-new \
                        ${BACKEND_IMAGE}

                    # 健康检查
                    sleep 30
                    curl -f http://localhost:8080/api/actuator/health

                    # 切换服务
                    docker stop ${PROJECT_NAME}-backend || true
                    docker rename ${PROJECT_NAME}-backend-new ${PROJECT_NAME}-backend
                '''
            }
        }

        stage('Deploy Frontend') {
            steps {
                sh '''
                    docker run -d \
                        --name ${PROJECT_NAME}-frontend-new \
                        ${FRONTEND_IMAGE}

                    # 健康检查
                    sleep 10
                    curl -f http://localhost/

                    # 切换服务
                    docker stop ${PROJECT_NAME}-frontend || true
                    docker rename ${PROJECT_NAME}-frontend-new ${PROJECT_NAME}-frontend
                '''
            }
        }
    }
}
```

## 故障排除

### 1. 常见问题

**问题1: Docker权限不足**
```bash
# 解决方案：将jenkins用户添加到docker组
sudo usermod -aG docker jenkins
sudo systemctl restart jenkins
```

**问题2: SSH连接失败**
```bash
# 检查SSH配置
ssh -o StrictHostKeyChecking=no -i /path/to/key user@server

# 在Jenkins中配置SSH Agent
sshagent(['ssh-credentials-id']) {
    sh 'ssh user@server "command"'
}
```

**问题3: 镜像推送失败**
```groovy
// 在Pipeline中添加Docker登录
withCredentials([usernamePassword(credentialsId: 'docker-registry', usernameVariable: 'DOCKER_USER', passwordVariable: 'DOCKER_PASS')]) {
    sh 'echo $DOCKER_PASS | docker login -u $DOCKER_USER --password-stdin'
}
```

### 2. 调试技巧

```groovy
stage('Debug') {
    steps {
        script {
            // 打印环境变量
            sh 'env | sort'

            // 检查Docker状态
            sh 'docker ps -a'
            sh 'docker images'

            // 检查网络连接
            sh 'ping -c 3 ${DEPLOY_SERVER}'

            // 检查磁盘空间
            sh 'df -h'
        }
    }
}
```

## 最佳实践

### 1. 版本管理

```groovy
environment {
    // 使用Git提交哈希作为版本标签
    VERSION = sh(
        script: 'git rev-parse --short HEAD',
        returnStdout: true
    ).trim()

    IMAGE_TAG = "${VERSION}-${BUILD_NUMBER}"
}
```

### 2. 环境隔离

```groovy
stage('Deploy') {
    when {
        anyOf {
            branch 'main'
            branch 'develop'
            branch 'release/*'
        }
    }
    steps {
        script {
            def deployEnv = env.BRANCH_NAME == 'main' ? 'production' : 'staging'

            sh """
                export DEPLOY_ENV=${deployEnv}
                ./scripts/deploy.sh
            """
        }
    }
}
```

### 3. 自动化测试集成

```groovy
stage('Tests') {
    parallel {
        stage('Backend Tests') {
            steps {
                dir('backend') {
                    sh 'mvn test'
                }
            }
            post {
                always {
                    junit 'backend/target/surefire-reports/*.xml'
                }
            }
        }

        stage('Frontend Tests') {
            steps {
                dir('frontend') {
                    sh 'npm test'
                }
            }
            post {
                always {
                    publishHTML([
                        allowMissing: false,
                        alwaysLinkToLastBuild: true,
                        keepAll: true,
                        reportDir: 'frontend/coverage',
                        reportFiles: 'index.html',
                        reportName: 'Frontend Coverage Report'
                    ])
                }
            }
        }
    }
}
```

## 快速开始指南

### 1. 创建Jenkins任务

1. 登录Jenkins管理界面
2. 点击"新建任务"
3. 选择"Pipeline"类型
4. 配置Git仓库地址
5. 选择Jenkinsfile路径

### 2. 配置必要凭据

```bash
# 在Jenkins中添加以下凭据
- mongodb-host: MongoDB服务器地址
- mongodb-username: MongoDB用户名
- mongodb-password: MongoDB密码
- docker-registry: Docker仓库凭据
- deploy-server-ssh: 部署服务器SSH密钥
```

### 3. 运行第一次部署

1. 点击"立即构建"
2. 查看构建日志
3. 验证部署结果

## 总结

使用Jenkins进行自动化部署的优势：

1. **自动化流程**: 从代码提交到部署全程自动化
2. **多环境支持**: 支持开发、测试、生产环境的差异化部署
3. **回滚机制**: 快速回滚到上一个稳定版本
4. **监控通知**: 实时监控部署状态并发送通知
5. **权限控制**: 细粒度的权限管理和审批流程

通过Jenkins Pipeline，您可以实现：
- 代码提交触发自动构建
- 自动化测试和质量检查
- 多环境自动部署
- 部署状态监控和通知
- 快速回滚和故障恢复

Jenkins为您的日志管理系统提供了完整的CI/CD解决方案，大大提高了开发和运维效率。
