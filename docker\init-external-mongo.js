// MongoDB 外部数据库初始化脚本
// 用于在现有MongoDB服务器中创建日志管理系统所需的数据库结构
// 
// 使用方法:
// mongosh "**********************************************" < init-external-mongo.js
// 或
// mongo "**********************************************" < init-external-mongo.js

// 配置变量 - 请根据实际情况修改
const DB_NAME = 'logger_management';
const APP_USER = 'logger_user';
const APP_PASSWORD = 'logger_password';

print('===========================================');
print('通用日志管理系统 - MongoDB 初始化脚本');
print('===========================================');

// 切换到目标数据库
print('切换到数据库: ' + DB_NAME);
db = db.getSiblingDB(DB_NAME);

// 创建应用用户
print('创建应用用户: ' + APP_USER);
try {
    db.createUser({
        user: APP_USER,
        pwd: APP_PASSWORD,
        roles: [
            {
                role: 'readWrite',
                db: DB_NAME
            }
        ]
    });
    print('✓ 用户创建成功');
} catch (e) {
    if (e.code === 51003) {
        print('⚠ 用户已存在，跳过创建');
    } else {
        print('✗ 用户创建失败: ' + e.message);
    }
}

// 创建集合
print('创建集合...');

// 日志条目集合
if (!db.getCollectionNames().includes('log_entries')) {
    db.createCollection('log_entries');
    print('✓ log_entries 集合创建成功');
} else {
    print('⚠ log_entries 集合已存在');
}

// 应用集合
if (!db.getCollectionNames().includes('applications')) {
    db.createCollection('applications');
    print('✓ applications 集合创建成功');
} else {
    print('⚠ applications 集合已存在');
}

// 用户集合
if (!db.getCollectionNames().includes('users')) {
    db.createCollection('users');
    print('✓ users 集合创建成功');
} else {
    print('⚠ users 集合已存在');
}

// 用户权限集合
if (!db.getCollectionNames().includes('user_permissions')) {
    db.createCollection('user_permissions');
    print('✓ user_permissions 集合创建成功');
} else {
    print('⚠ user_permissions 集合已存在');
}

// 创建索引
print('创建索引...');

// 日志条目索引
print('为 log_entries 集合创建索引...');
try {
    db.log_entries.createIndex({ "timestamp": -1 });
    db.log_entries.createIndex({ "level": 1 });
    db.log_entries.createIndex({ "applicationId": 1 });
    db.log_entries.createIndex({ "level": 1, "timestamp": -1 });
    db.log_entries.createIndex({ "applicationId": 1, "timestamp": -1 });
    
    // 复合索引用于复杂查询
    db.log_entries.createIndex({ 
        "level": 1, 
        "applicationId": 1, 
        "timestamp": -1 
    });
    
    // 文本索引支持全文搜索
    db.log_entries.createIndex({ 
        "message": "text",
        "exception": "text",
        "environmentProperties": "text",
        "extendProperties": "text"
    });
    
    print('✓ log_entries 索引创建成功');
} catch (e) {
    print('⚠ log_entries 索引创建部分失败: ' + e.message);
}

// 应用集合索引
print('为 applications 集合创建索引...');
try {
    db.applications.createIndex({ "name": 1 }, { unique: true });
    db.applications.createIndex({ "token": 1 }, { unique: true });
    db.applications.createIndex({ "creatorId": 1 });
    db.applications.createIndex({ "status": 1 });
    
    print('✓ applications 索引创建成功');
} catch (e) {
    print('⚠ applications 索引创建部分失败: ' + e.message);
}

// 用户集合索引
print('为 users 集合创建索引...');
try {
    db.users.createIndex({ "username": 1 }, { unique: true });
    db.users.createIndex({ "email": 1 }, { unique: true });
    db.users.createIndex({ "role": 1 });
    
    print('✓ users 索引创建成功');
} catch (e) {
    print('⚠ users 索引创建部分失败: ' + e.message);
}

// 用户权限集合索引
print('为 user_permissions 集合创建索引...');
try {
    db.user_permissions.createIndex({ "userId": 1 });
    db.user_permissions.createIndex({ "applicationId": 1 });
    db.user_permissions.createIndex({ "userId": 1, "applicationId": 1 }, { unique: true });
    
    print('✓ user_permissions 索引创建成功');
} catch (e) {
    print('⚠ user_permissions 索引创建部分失败: ' + e.message);
}

// 显示统计信息
print('===========================================');
print('初始化完成统计信息:');
print('===========================================');
print('数据库: ' + DB_NAME);
print('应用用户: ' + APP_USER);

print('集合列表:');
db.getCollectionNames().forEach(function(collection) {
    print('  - ' + collection);
});

print('===========================================');
print('初始化完成！');
print('===========================================');

// 验证连接
print('验证应用用户连接...');
try {
    // 切换到应用用户进行测试
    db.auth(APP_USER, APP_PASSWORD);
    print('✓ 应用用户认证成功');
    
    // 测试写入权限
    db.log_entries.insertOne({
        message: "测试日志条目",
        level: "INFO",
        timestamp: new Date(),
        applicationId: "test"
    });
    
    // 删除测试数据
    db.log_entries.deleteOne({ message: "测试日志条目" });
    
    print('✓ 读写权限测试通过');
} catch (e) {
    print('✗ 应用用户验证失败: ' + e.message);
}

print('===========================================');
print('脚本执行完成');
print('===========================================');
