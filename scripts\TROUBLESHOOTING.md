# 跨平台部署故障排除指南

## 常见问题及解决方案

### 1. "不是内部命令"错误

**问题描述**: 运行批处理脚本时出现"xxx不是内部或外部命令"错误

**可能原因**:
- 脚本路径问题
- 缺少必要的工具
- 环境变量配置问题

**解决方案**:

#### 方案A: 使用简化脚本
```cmd
cd scripts
simple-deploy.bat
```

#### 方案B: 手动执行步骤
```cmd
cd scripts

:: 1. 打包源码
package-source.bat

:: 2. 检查生成的包文件
dir logger-management-*.zip

:: 3. 手动传输文件到Linux服务器
```

#### 方案C: 检查工具安装
```cmd
:: 检查是否安装了必要工具
where scp
where ssh
where powershell
```

### 2. 文件传输失败

**问题描述**: 无法将文件传输到Linux服务器

**解决方案**:

#### 使用WinSCP (推荐)
1. 下载安装 WinSCP: https://winscp.net/
2. 配置连接信息
3. 手动传输源码包

#### 使用命令行SCP
```cmd
:: 基本语法
scp 源文件 用户名@服务器IP:目标路径

:: 示例
scp logger-management-20241220_143022.zip ubuntu@*************:/home/<USER>/
```

#### 使用SSH密钥认证
```cmd
:: 生成SSH密钥
ssh-keygen -t rsa -b 4096

:: 复制公钥到服务器
scp %USERPROFILE%\.ssh\id_rsa.pub ubuntu@*************:~/.ssh/authorized_keys
```

### 3. Linux服务器环境问题

**问题描述**: Linux服务器缺少必要环境

**解决方案**:

#### 安装Docker (Ubuntu/Debian)
```bash
# 更新包索引
sudo apt update

# 安装必要包
sudo apt install apt-transport-https ca-certificates curl gnupg lsb-release

# 添加Docker官方GPG密钥
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg

# 添加Docker仓库
echo "deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

# 安装Docker
sudo apt update
sudo apt install docker-ce docker-ce-cli containerd.io

# 启动Docker服务
sudo systemctl start docker
sudo systemctl enable docker

# 将用户添加到docker组
sudo usermod -aG docker $USER
```

#### 安装Docker (CentOS/RHEL)
```bash
# 安装必要包
sudo yum install -y yum-utils

# 添加Docker仓库
sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo

# 安装Docker
sudo yum install -y docker-ce docker-ce-cli containerd.io

# 启动Docker服务
sudo systemctl start docker
sudo systemctl enable docker

# 将用户添加到docker组
sudo usermod -aG docker $USER
```

#### 安装Docker Compose
```bash
# 下载Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose

# 添加执行权限
sudo chmod +x /usr/local/bin/docker-compose

# 验证安装
docker-compose --version
```

### 4. 权限问题

**问题描述**: 权限不足或访问被拒绝

**解决方案**:

#### 检查文件权限
```bash
# 查看文件权限
ls -la

# 修改脚本执行权限
chmod +x scripts/deploy-on-linux.sh

# 修改目录权限
chmod 755 /home/<USER>/logger-management
```

#### 检查用户组
```bash
# 查看当前用户组
groups

# 检查是否在docker组中
groups $USER | grep docker

# 如果不在docker组，重新登录
logout
```

### 5. 网络连接问题

**问题描述**: 无法连接到服务器或下载失败

**解决方案**:

#### 检查网络连接
```cmd
:: 测试服务器连通性
ping *************

:: 测试SSH端口
telnet ************* 22
```

#### 配置防火墙
```bash
# Ubuntu/Debian (UFW)
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 8080/tcp

# CentOS/RHEL (firewalld)
sudo firewall-cmd --permanent --add-port=22/tcp
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=8080/tcp
sudo firewall-cmd --reload
```

### 6. Docker部署问题

**问题描述**: Docker容器启动失败

**解决方案**:

#### 查看容器状态
```bash
# 查看所有容器
docker ps -a

# 查看容器日志
docker-compose logs backend
docker-compose logs frontend
docker-compose logs mongodb
```

#### 检查资源使用
```bash
# 查看系统资源
free -h
df -h

# 查看Docker资源使用
docker stats
```

#### 重新构建容器
```bash
# 停止所有容器
docker-compose down

# 清理Docker缓存
docker system prune -f

# 重新构建并启动
docker-compose build --no-cache
docker-compose up -d
```

### 7. 端口冲突问题

**问题描述**: 端口被占用

**解决方案**:

#### 检查端口占用
```bash
# Linux
netstat -tulpn | grep :80
netstat -tulpn | grep :8080

# 或使用ss命令
ss -tulpn | grep :80
```

#### 修改端口配置
编辑 `docker/.env` 文件：
```env
FRONTEND_PORT=8000
BACKEND_PORT=8081
```

### 8. 数据库连接问题

**问题描述**: 无法连接到MongoDB

**解决方案**:

#### 检查MongoDB容器
```bash
# 查看MongoDB容器状态
docker-compose ps mongodb

# 查看MongoDB日志
docker-compose logs mongodb

# 进入MongoDB容器
docker-compose exec mongodb mongosh
```

#### 检查网络连接
```bash
# 测试容器间网络
docker-compose exec backend ping mongodb
```

## 获取帮助

### 日志收集
在寻求帮助时，请提供以下信息：

1. **系统信息**
```bash
# Windows
systeminfo | findstr /B /C:"OS Name" /C:"OS Version"

# Linux
uname -a
cat /etc/os-release
```

2. **Docker信息**
```bash
docker --version
docker-compose --version
docker info
```

3. **错误日志**
```bash
# 应用日志
docker-compose logs

# 系统日志
sudo journalctl -u docker
```

### 联系支持
- 查看项目文档
- 提交GitHub Issue
- 联系系统管理员

---

**提示**: 大多数问题都可以通过仔细阅读错误信息和检查配置文件来解决。如果问题持续存在，请按照上述步骤逐一排查。
