using LogSimulator.Models;
using System.Text.Json;

namespace LogSimulator.Tests;

/// <summary>
/// ApiResponse 测试类
/// </summary>
public class ApiResponseTest
{
    /// <summary>
    /// 测试 ApiResponse 序列化和反序列化
    /// </summary>
    public static void TestApiResponseSerialization()
    {
        Console.WriteLine("=== ApiResponse 序列化测试 ===");

        // 创建成功响应
        var successResponse = new ApiResponse<string>
        {
            Code = 200,
            Message = "操作成功",
            Data = "测试数据",
            Timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
        };

        Console.WriteLine($"成功响应 IsSuccess: {successResponse.IsSuccess}");
        Console.WriteLine($"成功响应 Code: {successResponse.Code}");
        Console.WriteLine($"成功响应 Message: {successResponse.Message}");
        Console.WriteLine($"成功响应 Data: {successResponse.Data}");
        Console.WriteLine($"成功响应 Timestamp: {successResponse.Timestamp}");

        // 序列化
        var jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = true
        };

        var json = JsonSerializer.Serialize(successResponse, jsonOptions);
        Console.WriteLine($"序列化JSON:\n{json}");

        // 反序列化
        var deserializedResponse = JsonSerializer.Deserialize<ApiResponse<string>>(json, jsonOptions);
        Console.WriteLine($"反序列化后 IsSuccess: {deserializedResponse?.IsSuccess}");
        Console.WriteLine($"反序列化后 Code: {deserializedResponse?.Code}");
        Console.WriteLine($"反序列化后 Message: {deserializedResponse?.Message}");

        Console.WriteLine();

        // 创建失败响应
        var errorResponse = new ApiResponse<object>
        {
            Code = 400,
            Message = "参数错误",
            Data = null,
            Timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
        };

        Console.WriteLine($"错误响应 IsSuccess: {errorResponse.IsSuccess}");
        Console.WriteLine($"错误响应 Code: {errorResponse.Code}");
        Console.WriteLine($"错误响应 Message: {errorResponse.Message}");

        var errorJson = JsonSerializer.Serialize(errorResponse, jsonOptions);
        Console.WriteLine($"错误响应JSON:\n{errorJson}");

        Console.WriteLine("=== 测试完成 ===");
    }

    /// <summary>
    /// 测试与后端兼容的JSON格式
    /// </summary>
    public static void TestBackendCompatibility()
    {
        Console.WriteLine("=== 后端兼容性测试 ===");

        // 模拟后端返回的JSON
        var backendJson = """
        {
            "code": 200,
            "message": "操作成功",
            "data": {
                "id": "12345",
                "name": "测试数据"
            },
            "timestamp": 1640995200000
        }
        """;

        Console.WriteLine($"后端JSON:\n{backendJson}");

        var jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };

        try
        {
            // 反序列化为 ApiResponse<object>
            var response = JsonSerializer.Deserialize<ApiResponse<object>>(backendJson, jsonOptions);
            
            Console.WriteLine($"解析成功:");
            Console.WriteLine($"  Code: {response?.Code}");
            Console.WriteLine($"  Message: {response?.Message}");
            Console.WriteLine($"  IsSuccess: {response?.IsSuccess}");
            Console.WriteLine($"  Timestamp: {response?.Timestamp}");
            Console.WriteLine($"  Data: {response?.Data}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"解析失败: {ex.Message}");
        }

        Console.WriteLine("=== 兼容性测试完成 ===");
    }
}
