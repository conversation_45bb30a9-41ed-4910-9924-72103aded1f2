server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: logger-management-backend
  
  data:
    mongodb:
      host: ************
      port: 27017
      database: logger_management
      # 如果需要认证，取消注释以下配置
      username: mongo_5MySKx
      password: mongo_b2H8yD
      authentication-database: admin
  
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
    default-property-inclusion: non_null

# 日志配置
logging:
  level:
    com.logmanagement: DEBUG
    org.springframework.data.mongodb: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# 应用自定义配置
app:
  cors:
    allowed-origins: "http://localhost:5173,http://127.0.0.1:5173,http://***********:5173,http://localhost:3000,http://127.0.0.1:3000"
    allowed-methods: "GET,POST,PUT,DELETE,OPTIONS"
    allowed-headers: "*"
    allow-credentials: true

  jwt:
    secret: "LoggerManagementSystemSecretKeyForJWTTokenGeneration2024!@#$%^&*()_+{}|:<>?[]\\;'\",./"
    expiration: 86400

  pagination:
    default-page-size: 10
    max-page-size: 100

# Knife4j配置
knife4j:
  enable: true
  setting:
    language: zh_cn
    enable-version: true
    enable-reload-cache-parameter: true
    enable-after-script: true
    enable-filter-multipart-api-method-type: POST
    enable-filter-multipart-apis: false
    enable-request-cache: true
    enable-host: false
    enable-host-text: ""
    enable-home-custom: true
    home-custom-path: classpath:markdown/home.md
    enable-search: true
    enable-footer: true
    enable-footer-custom: true
    footer-custom-content: "Copyright © 2024 Logger Management System"
    enable-dynamic-parameter: true
    enable-debug: true
    enable-open-api: false
    enable-group: true
  cors: false
  production: false
  basic:
    enable: false
