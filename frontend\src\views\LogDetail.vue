<template>
  <div class="log-detail">
    <el-page-header @back="handleBack" title="返回日志列表">
      <template #content>
        <span class="page-title">日志详情</span>
      </template>
    </el-page-header>

    <el-card v-loading="loading" class="detail-card" shadow="never">
      <template v-if="log">
        <!-- 基本信息 -->
        <el-descriptions title="基本信息" :column="2" border>
          <el-descriptions-item label="日志ID">
            {{ log.id }}
          </el-descriptions-item>

          <el-descriptions-item label="时间">
            {{ formatTime(log.timestamp) }}
          </el-descriptions-item>

          <el-descriptions-item label="级别">
            <el-tag :type="getLevelTagType(log.level)">
              {{ log.level }}
            </el-tag>
          </el-descriptions-item>

          <el-descriptions-item label="来源">
            {{ log.source }}
          </el-descriptions-item>

          <el-descriptions-item label="应用ID" v-if="log.applicationId">
            {{ log.applicationId }}
          </el-descriptions-item>

          <el-descriptions-item label="应用名称" v-if="log.applicationName">
            {{ log.applicationName }}
          </el-descriptions-item>

          <el-descriptions-item label="环境" v-if="log.environment">
            <el-tag :type="getEnvTagType(log.environment)">
              {{ getEnvText(log.environment) }}
            </el-tag>
          </el-descriptions-item>

          <el-descriptions-item label="线程" v-if="log.thread">
            {{ log.thread }}
          </el-descriptions-item>

          <el-descriptions-item label="创建时间" v-if="log.createdAt">
            {{ formatTime(log.createdAt) }}
          </el-descriptions-item>

          <el-descriptions-item label="更新时间" v-if="log.updatedAt">
            {{ formatTime(log.updatedAt) }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 日志消息 -->
        <el-divider content-position="left">日志消息</el-divider>
        <el-card class="message-card" shadow="never">
          <pre class="log-message">{{ log.message }}</pre>
        </el-card>

        <!-- 异常信息 -->
        <template v-if="log.exception">
          <el-divider content-position="left">异常信息</el-divider>
          <el-card class="exception-card" shadow="never">
            <pre class="exception-text">{{ log.exception }}</pre>
          </el-card>
        </template>

        <!-- 环境属性 -->
        <template v-if="log.environmentProperties && Object.keys(log.environmentProperties).length > 0">
          <el-divider content-position="left">环境属性</el-divider>
          <el-card class="metadata-card" shadow="never">
            <el-table :data="environmentPropertiesList" border>
              <el-table-column prop="key" label="键" width="200" />
              <el-table-column prop="value" label="值" show-overflow-tooltip>
                <template #default="{ row }">
                  <span v-if="typeof row.value === 'object'">
                    {{ JSON.stringify(row.value, null, 2) }}
                  </span>
                  <span v-else>{{ row.value }}</span>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </template>

        <!-- 扩展属性 -->
        <template v-if="log.extendProperties && Object.keys(log.extendProperties).length > 0">
          <el-divider content-position="left">扩展属性</el-divider>
          <el-card class="metadata-card" shadow="never">
            <el-table :data="extendPropertiesList" border>
              <el-table-column prop="key" label="键" width="200" />
              <el-table-column prop="value" label="值" show-overflow-tooltip>
                <template #default="{ row }">
                  <span v-if="typeof row.value === 'object'">
                    {{ JSON.stringify(row.value, null, 2) }}
                  </span>
                  <span v-else>{{ row.value }}</span>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </template>

        <!-- 元数据 -->
        <template v-if="log.metadata && Object.keys(log.metadata).length > 0">
          <el-divider content-position="left">元数据</el-divider>
          <el-card class="metadata-card" shadow="never">
            <el-table :data="metadataList" border>
              <el-table-column prop="key" label="键" width="200" />
              <el-table-column prop="value" label="值" show-overflow-tooltip>
                <template #default="{ row }">
                  <span v-if="typeof row.value === 'object'">
                    {{ JSON.stringify(row.value, null, 2) }}
                  </span>
                  <span v-else>{{ row.value }}</span>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </template>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <el-button type="primary" @click="handleCopyLog">
            <el-icon><CopyDocument /></el-icon>
            复制日志
          </el-button>
          
          <el-button type="warning" @click="handleExportLog">
            <el-icon><Download /></el-icon>
            导出日志
          </el-button>
          
          <el-button type="danger" @click="handleDeleteLog">
            <el-icon><Delete /></el-icon>
            删除日志
          </el-button>
        </div>
      </template>
      
      <el-empty v-else-if="!loading" description="日志不存在" />
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { CopyDocument, Download, Delete } from '@element-plus/icons-vue'
import { logApi, LogLevel } from '../api/logs'
import { formatTime } from '../utils/dateTime'
import { Message, MessageBox } from '../utils/message'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const log = ref(null)

// 计算属性
const metadataList = computed(() => {
  if (!log.value?.metadata) return []
  return Object.entries(log.value.metadata).map(([key, value]) => ({
    key,
    value
  }))
})

const environmentPropertiesList = computed(() => {
  if (!log.value?.environmentProperties) return []
  return Object.entries(log.value.environmentProperties).map(([key, value]) => ({
    key,
    value
  }))
})

const extendPropertiesList = computed(() => {
  if (!log.value?.extendProperties) return []
  return Object.entries(log.value.extendProperties).map(([key, value]) => ({
    key,
    value
  }))
})

// 获取日志级别标签类型
const getLevelTagType = (level) => {
  const typeMap = {
    ERROR: 'danger',
    WARN: 'warning',
    INFO: 'info',
    DEBUG: 'success'
  }
  return typeMap[level] || 'info'
}

// 获取环境标签类型
const getEnvTagType = (env) => {
  const typeMap = {
    dev: 'warning',
    test: 'info',
    staging: 'primary',
    prod: 'success'
  }
  return typeMap[env] || 'info'
}

// 获取环境文本
const getEnvText = (env) => {
  const textMap = {
    dev: '开发',
    test: '测试',
    staging: '模拟',
    prod: '生产'
  }
  return textMap[env] || env
}



// 返回列表
const handleBack = () => {
  router.back()
}

// 复制日志
const handleCopyLog = async () => {
  if (!log.value) return

  try {
    const logText = `
日志ID: ${log.value.id}
时间: ${formatTime(log.value.timestamp)}
级别: ${log.value.level}
来源: ${log.value.source}
应用ID: ${log.value.applicationId || 'N/A'}
应用名称: ${log.value.applicationName || 'N/A'}
环境: ${log.value.environment || 'N/A'}
线程: ${log.value.thread || 'N/A'}
消息: ${log.value.message}
${log.value.exception ? `异常: ${log.value.exception}` : ''}
${log.value.environmentProperties ? `环境属性: ${JSON.stringify(log.value.environmentProperties, null, 2)}` : ''}
${log.value.extendProperties ? `扩展属性: ${JSON.stringify(log.value.extendProperties, null, 2)}` : ''}
${log.value.metadata ? `元数据: ${JSON.stringify(log.value.metadata, null, 2)}` : ''}
创建时间: ${log.value.createdAt ? formatTime(log.value.createdAt) : 'N/A'}
更新时间: ${log.value.updatedAt ? formatTime(log.value.updatedAt) : 'N/A'}
    `.trim()

    await navigator.clipboard.writeText(logText)
    Message.success('日志已复制到剪贴板')
  } catch (error) {
    Message.error('复制失败')
  }
}

// 导出日志
const handleExportLog = () => {
  if (!log.value) return
  
  const logData = {
    ...log.value,
    formattedTime: formatTime(log.value.timestamp)
  }
  
  const dataStr = JSON.stringify(logData, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })
  
  const link = document.createElement('a')
  link.href = URL.createObjectURL(dataBlob)
  link.download = `log_${log.value.id}_${new Date().getTime()}.json`
  link.click()
  
  URL.revokeObjectURL(link.href)
  Message.success('日志导出成功')
}

// 删除日志
const handleDeleteLog = async () => {
  if (!log.value) return

  // 检查日志ID是否有效
  if (!log.value.id || log.value.id.trim() === '') {
    Message.error('无法删除：日志ID无效')
    return
  }

  try {
    await MessageBox.delete('确定要删除这条日志吗？删除后将无法恢复。', '确认删除')

    await logApi.deleteLog(log.value.id)
    Message.success('删除成功')
    router.push('/logs')
  } catch (error) {
    if (error !== 'cancel') {
      Message.error('删除失败')
    }
  }
}

// 加载日志详情
const loadLogDetail = async () => {
  const logId = route.params.id
  if (!logId) {
    Message.error('日志ID不存在')
    router.push('/logs')
    return
  }

  try {
    loading.value = true
    const response = await logApi.getLogById(logId)

    // 处理API响应格式
    if (response.code === 200 && response.data) {
      log.value = response.data
    } else {
      throw new Error(response.message || '日志不存在')
    }
  } catch (error) {
    console.error('加载日志详情失败:', error)
    Message.error('加载日志详情失败')
    router.push('/logs')
  } finally {
    loading.value = false
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadLogDetail()
})
</script>

<style scoped>
.log-detail {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-title {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.detail-card {
  margin-top: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.message-card,
.exception-card,
.metadata-card {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  margin-top: 16px;
}

.log-message,
.exception-text {
  margin: 0;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  font-family: 'Fira Code', 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 400px;
  overflow-y: auto;
  color: #2c3e50;
  border: 1px solid #e1e8ed;
}

.action-buttons {
  margin-top: 32px;
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  min-width: 120px;
  height: 40px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.action-buttons .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.el-divider {
  margin: 32px 0 20px 0;
  font-weight: 600;
  color: #606266;
}

.el-descriptions {
  margin-bottom: 24px;
}

:deep(.el-descriptions__header) {
  margin-bottom: 20px;
}

:deep(.el-descriptions__title) {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

:deep(.el-descriptions__body) {
  background-color: white;
}

:deep(.el-descriptions-item__label) {
  font-weight: 600;
  color: #606266;
  background-color: #fafafa;
}

:deep(.el-descriptions-item__content) {
  color: #303133;
}

:deep(.el-page-header__content) {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

:deep(.el-page-header__left::after) {
  background-color: #dcdfe6;
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #fafafa;
  color: #606266;
  font-weight: bold;
  padding: 6px 0;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f0f0f0;
  padding: 6px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .log-detail {
    padding: 15px;
  }

  .action-buttons {
    flex-direction: column;
    align-items: center;
  }

  .action-buttons .el-button {
    width: 100%;
    max-width: 200px;
  }

  .log-message,
  .exception-text {
    font-size: 12px;
    padding: 15px;
  }
}
</style>
