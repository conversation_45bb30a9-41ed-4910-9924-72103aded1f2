package com.logmanagement.backend.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 日志条目实体类
 *
 * <AUTHOR> Management System
 * @version 1.0.0
 */
@Document(collection = "log_entries")
public class LogEntry {

    @Id
    private String id;

    /**
     * 日志时间戳
     */
    @NotNull(message = "日志时间戳不能为空")
    @Indexed
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private LocalDateTime timestamp;

    /**
     * 日志级别
     */
    @NotNull(message = "日志级别不能为空")
    @Indexed
    private LogLevel level;

    /**
     * 日志消息
     */
    @NotBlank(message = "日志消息不能为空")
    private String message;

    /**
     * 日志来源
     */
    @NotBlank(message = "日志来源不能为空")
    @Indexed
    private String source;

    /**
     * 应用ID
     */
    @NotBlank(message = "应用ID不能为空")
    @Indexed
    private String applicationId;



    /**
     * 环境信息
     */
    @Indexed
    private String environment;

    /**
     * 扩展属性
     */
    private Map<String, Object> extendProperties;

    /**
     * 环境属性
     */
    private Map<String, Object> environmentProperties;

    /**
     * 线程名称
     */
    private String thread;

    /**
     * 异常信息
     */
    private String exception;

    /**
     * 元数据
     */
    private Map<String, Object> metadata;

    /**
     * 创建时间
     */
    @CreatedDate
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @LastModifiedDate
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private LocalDateTime updatedAt;

    // 构造函数
    public LogEntry() {
    }

    public LogEntry(LocalDateTime timestamp, LogLevel level, String message, String source) {
        this.timestamp = timestamp;
        this.level = level;
        this.message = message;
        this.source = source;
    }

    // Getter 和 Setter 方法
    public String getId() {
        return id;
    }

    public void setId(String id) {
        // 只有当ID不为空字符串时才设置，确保MongoDB能够自动生成ID
        if (id != null && !id.trim().isEmpty()) {
            this.id = id;
        }
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }

    public LogLevel getLevel() {
        return level;
    }

    public void setLevel(LogLevel level) {
        this.level = level;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(String applicationId) {
        this.applicationId = applicationId;
    }



    public String getEnvironment() {
        return environment;
    }

    public void setEnvironment(String environment) {
        this.environment = environment;
    }

    public Map<String, Object> getExtendProperties() {
        return extendProperties;
    }

    public void setExtendProperties(Map<String, Object> extendProperties) {
        this.extendProperties = extendProperties;
    }

    public Map<String, Object> getEnvironmentProperties() {
        return environmentProperties;
    }

    public void setEnvironmentProperties(Map<String, Object> environmentProperties) {
        this.environmentProperties = environmentProperties;
    }

    public String getThread() {
        return thread;
    }

    public void setThread(String thread) {
        this.thread = thread;
    }

    public String getException() {
        return exception;
    }

    public void setException(String exception) {
        this.exception = exception;
    }

    public Map<String, Object> getMetadata() {
        return metadata;
    }

    public void setMetadata(Map<String, Object> metadata) {
        this.metadata = metadata;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    public String toString() {
        return "LogEntry{" +
                "id='" + id + '\'' +
                ", timestamp=" + timestamp +
                ", level=" + level +
                ", message='" + message + '\'' +
                ", source='" + source + '\'' +
                ", thread='" + thread + '\'' +
                '}';
    }
}
