package com.logmanagement.backend.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 分页配置类
 * 
 * <AUTHOR> Management System
 * @version 1.0.0
 */
@Configuration
@ConfigurationProperties(prefix = "app.pagination")
public class PaginationConfig {

    /**
     * 默认页面大小
     */
    private int defaultPageSize = 20;

    /**
     * 最大页面大小
     */
    private int maxPageSize = 100;

    /**
     * 获取默认页面大小
     */
    public int getDefaultPageSize() {
        return defaultPageSize;
    }

    /**
     * 设置默认页面大小
     */
    public void setDefaultPageSize(int defaultPageSize) {
        this.defaultPageSize = defaultPageSize;
    }

    /**
     * 获取最大页面大小
     */
    public int getMaxPageSize() {
        return maxPageSize;
    }

    /**
     * 设置最大页面大小
     */
    public void setMaxPageSize(int maxPageSize) {
        this.maxPageSize = maxPageSize;
    }

    /**
     * 验证并调整页面大小
     * 
     * @param size 请求的页面大小
     * @return 调整后的页面大小
     */
    public int validatePageSize(int size) {
        if (size <= 0) {
            return defaultPageSize;
        }
        if (size > maxPageSize) {
            return maxPageSize;
        }
        return size;
    }

    /**
     * 获取默认页面大小的字符串形式（用于@RequestParam的defaultValue）
     */
    public String getDefaultPageSizeString() {
        return String.valueOf(defaultPageSize);
    }
}
