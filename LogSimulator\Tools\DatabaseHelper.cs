using MongoDB.Driver;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System.Text.Json;

namespace LogSimulator.Tools;

/// <summary>
/// 应用状态枚举
/// </summary>
public enum ApplicationStatus
{
    ACTIVE,
    DISABLED,
    MAINTENANCE,
    DELETED
}

/// <summary>
/// 应用实体（与后端Application实体保持一致）
/// </summary>
public class ApplicationEntity
{
    /// <summary>
    /// 应用ID
    /// </summary>
    [BsonId]
    [BsonElement("_id")]
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// 应用名称
    /// </summary>
    [BsonElement("name")]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 应用描述
    /// </summary>
    [BsonElement("description")]
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 应用令牌（用于API认证，也作为ApiKey使用）
    /// </summary>
    [BsonElement("token")]
    public string Token { get; set; } = string.Empty;

    /// <summary>
    /// 应用状态
    /// </summary>
    [BsonElement("status")]
    public ApplicationStatus Status { get; set; }

    /// <summary>
    /// 应用创建者ID
    /// </summary>
    [BsonElement("creatorId")]
    public string CreatorId { get; set; } = string.Empty;

    /// <summary>
    /// 应用创建者用户名
    /// </summary>
    [BsonElement("creatorUsername")]
    public string CreatorUsername { get; set; } = string.Empty;

    /// <summary>
    /// 应用环境
    /// </summary>
    [BsonElement("environment")]
    public string Environment { get; set; } = string.Empty;

    /// <summary>
    /// 应用版本
    /// </summary>
    [BsonElement("version")]
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// 日志保留天数
    /// </summary>
    [BsonElement("logRetentionDays")]
    public int? LogRetentionDays { get; set; }

    /// <summary>
    /// 最后活跃时间
    /// </summary>
    [BsonElement("lastActiveTime")]
    public DateTime? LastActiveTime { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    [BsonElement("createdAt")]
    public DateTime? CreatedAt { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    [BsonElement("updatedAt")]
    public DateTime? UpdatedAt { get; set; }

    [BsonElement("_class")]
    public string? Class { get; set; } = string.Empty;

    /// <summary>
    /// 获取ApiKey（实际就是token）
    /// </summary>
    public string ApiKey => Token;
}

/// <summary>
/// 数据库帮助工具
/// </summary>
public class DatabaseHelper
{
    private readonly IMongoDatabase _database;

    public DatabaseHelper()
    {
        // 连接到MongoDB
        var connectionString = "*****************************************************************************************";
        var client = new MongoClient(connectionString);
        _database = client.GetDatabase("logger_management");
    }

    /// <summary>
    /// 获取所有应用信息
    /// </summary>
    public async Task<List<ApplicationEntity>> GetAllApplicationsAsync()
    {
        try
        {
            var collection = _database.GetCollection<ApplicationEntity>("applications");
            var applications = await collection.Find(_ => true).ToListAsync();
            return applications;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"获取应用信息失败: {ex.Message}");
            return new List<ApplicationEntity>();
        }
    }

    /// <summary>
    /// 显示所有应用信息
    /// </summary>
    public async Task ShowAllApplicationsAsync()
    {
        Console.WriteLine("=== 数据库中的应用信息 ===");
        
        var applications = await GetAllApplicationsAsync();
        
        if (applications.Count == 0)
        {
            Console.WriteLine("数据库中没有找到应用信息");
            return;
        }

        foreach (var app in applications)
        {
            Console.WriteLine($"应用ID: {app.Id}");
            Console.WriteLine($"应用名称: {app.Name}");
            Console.WriteLine($"描述: {app.Description}");
            Console.WriteLine($"Token (ApiKey): {app.Token}");
            Console.WriteLine($"状态: {app.Status}");
            Console.WriteLine($"环境: {app.Environment}");
            Console.WriteLine($"版本: {app.Version}");
            Console.WriteLine($"创建者: {app.CreatorId}");
            Console.WriteLine("---");
        }
        
        Console.WriteLine($"总计: {applications.Count} 个应用");
    }

    /// <summary>
    /// 创建测试应用
    /// </summary>
    public async Task<ApplicationEntity?> CreateTestApplicationAsync()
    {
        try
        {
            var collection = _database.GetCollection<ApplicationEntity>("applications");
            
            var testApp = new ApplicationEntity
            {
                Id = "app-7ddacc3f3cef4af894ac1dbf4e8dcadf",
                Name = "LogSimulator测试应用",
                Description = "用于LogSimulator测试的应用",
                Token = "a8bd9f1b91b14928a42e4186a6fe23df",
                Status = ApplicationStatus.ACTIVE,
                CreatorId = "admin-001",
                Environment = "development",
                Version = "1.0.0"
            };

            await collection.InsertOneAsync(testApp);
            Console.WriteLine("测试应用创建成功！");
            return testApp;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"创建测试应用失败: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// 检查指定应用是否存在
    /// </summary>
    public async Task<ApplicationEntity?> FindApplicationAsync(string appId)
    {
        try
        {
            var collection = _database.GetCollection<ApplicationEntity>("applications");
            var app = await collection.Find(x => x.Id == appId).FirstOrDefaultAsync();
            return app;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"查找应用失败: {ex.Message}");
            return null;
        }
    }
}
