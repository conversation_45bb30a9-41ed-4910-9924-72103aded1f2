package com.logmanagement.backend.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 应用实体类
 * 
 * <AUTHOR> Management System
 * @version 1.0.0
 */
@Document(collection = "applications")
public class Application {

    @Id
    private String id;

    /**
     * 应用名称
     */
    @NotBlank(message = "应用名称不能为空")
    @Indexed(unique = true)
    private String name;

    /**
     * 应用描述
     */
    private String description;

    /**
     * 应用令牌（用于API认证，也作为ApiKey使用）
     */
    @NotBlank(message = "应用令牌不能为空")
    @Indexed(unique = true)
    private String token;

    /**
     * 应用状态
     */
    @NotNull(message = "应用状态不能为空")
    private ApplicationStatus status;

    /**
     * 应用创建者ID
     */
    @NotBlank(message = "创建者ID不能为空")
    private String creatorId;

    /**
     * 应用创建者用户名
     */
    private String creatorUsername;

    /**
     * 应用环境
     */
    private String environment;

    /**
     * 应用版本
     */
    private String version;

    /**
     * 日志保留天数
     */
    private Integer logRetentionDays;

    /**
     * 最后活跃时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private LocalDateTime lastActiveTime;

    /**
     * 创建时间
     */
    @CreatedDate
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @LastModifiedDate
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private LocalDateTime updatedAt;

    // 构造函数
    public Application() {
    }

    public Application(String name, String description, String token, String creatorId) {
        this.name = name;
        this.description = description;
        this.token = token;
        this.creatorId = creatorId;
        this.status = ApplicationStatus.ACTIVE;
        this.logRetentionDays = 30; // 默认保留30天
    }

    // Getter 和 Setter 方法
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    /**
     * 获取ApiKey（实际就是token）
     */
    public String getApiKey() {
        return token;
    }

    public ApplicationStatus getStatus() {
        return status;
    }

    public void setStatus(ApplicationStatus status) {
        this.status = status;
    }

    public String getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(String creatorId) {
        this.creatorId = creatorId;
    }

    public String getCreatorUsername() {
        return creatorUsername;
    }

    public void setCreatorUsername(String creatorUsername) {
        this.creatorUsername = creatorUsername;
    }

    public String getEnvironment() {
        return environment;
    }

    public void setEnvironment(String environment) {
        this.environment = environment;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public Integer getLogRetentionDays() {
        return logRetentionDays;
    }

    public void setLogRetentionDays(Integer logRetentionDays) {
        this.logRetentionDays = logRetentionDays;
    }

    public LocalDateTime getLastActiveTime() {
        return lastActiveTime;
    }

    public void setLastActiveTime(LocalDateTime lastActiveTime) {
        this.lastActiveTime = lastActiveTime;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    public String toString() {
        return "Application{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", status=" + status +
                ", creatorId='" + creatorId + '\'' +
                ", environment='" + environment + '\'' +
                ", version='" + version + '\'' +
                '}';
    }
}
