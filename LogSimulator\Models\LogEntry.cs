using System.Text.Json.Serialization;

namespace LogSimulator.Models;

/// <summary>
/// 日志级别枚举 - 与后端LogLevel枚举保持一致
/// </summary>
public enum LogLevel
{
    ERROR,
    WARN,
    INFO,
    DEBUG
}

/// <summary>
/// 日志条目模型 - 与后端LogEntry实体保持一致
/// </summary>
public class LogEntry
{
    /// <summary>
    /// 日志ID
    /// </summary>
    [JsonPropertyName("id")]
    public string? Id { get; set; }

    /// <summary>
    /// 日志时间戳
    /// </summary>
    [JsonPropertyName("timestamp")]
    public DateTime Timestamp { get; set; }

    /// <summary>
    /// 日志级别
    /// </summary>
    [JsonPropertyName("level")]
    public LogLevel Level { get; set; }

    /// <summary>
    /// 日志消息
    /// </summary>
    [JsonPropertyName("message")]
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 日志来源
    /// </summary>
    [JsonPropertyName("source")]
    public string Source { get; set; } = string.Empty;

    /// <summary>
    /// 应用程序ID
    /// </summary>
    [JsonPropertyName("applicationId")]
    public string ApplicationId { get; set; } = string.Empty;

    /// <summary>
    /// 环境信息
    /// </summary>
    [JsonPropertyName("environment")]
    public string? Environment { get; set; }

    /// <summary>
    /// 扩展属性
    /// </summary>
    [JsonPropertyName("extendProperties")]
    public Dictionary<string, object>? ExtendProperties { get; set; }

    /// <summary>
    /// 环境属性
    /// </summary>
    [JsonPropertyName("environmentProperties")]
    public Dictionary<string, object>? EnvironmentProperties { get; set; }

    /// <summary>
    /// 线程名称
    /// </summary>
    [JsonPropertyName("thread")]
    public string? Thread { get; set; }

    /// <summary>
    /// 异常信息
    /// </summary>
    [JsonPropertyName("exception")]
    public string? Exception { get; set; }

    /// <summary>
    /// 元数据
    /// </summary>
    [JsonPropertyName("metadata")]
    public Dictionary<string, object>? Metadata { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    [JsonPropertyName("createdAt")]
    public DateTime? CreatedAt { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    [JsonPropertyName("updatedAt")]
    public DateTime? UpdatedAt { get; set; }
}

/// <summary>
/// 批量日志请求模型
/// </summary>
public class BatchLogRequest
{
    /// <summary>
    /// 日志条目列表
    /// </summary>
    [JsonPropertyName("logs")]
    public List<LogEntry> Logs { get; set; } = new();
}

/// <summary>
/// API响应模型 - 与后端ApiResponse保持一致
/// </summary>
public class ApiResponse<T>
{
    /// <summary>
    /// 响应状态码
    /// 200-成功, 400-参数错误, 401-未授权, 404-不存在, 500-服务器错误
    /// </summary>
    [JsonPropertyName("code")]
    public int Code { get; set; }

    /// <summary>
    /// 响应消息
    /// </summary>
    [JsonPropertyName("message")]
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 响应数据
    /// 具体的业务数据，类型根据接口而定
    /// </summary>
    [JsonPropertyName("data")]
    public T? Data { get; set; }

    /// <summary>
    /// 响应时间戳
    /// </summary>
    [JsonPropertyName("timestamp")]
    public long Timestamp { get; set; }

    /// <summary>
    /// 判断响应是否成功 (状态码为200)
    /// </summary>
    [JsonIgnore]
    public bool IsSuccess => Code == 200;
}