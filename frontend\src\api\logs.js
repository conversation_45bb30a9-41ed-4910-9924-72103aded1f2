import request from './request'

// 日志级别常量
export const LogLevel = {
  ERROR: 'ERROR',
  WARN: 'WARN',
  INFO: 'INFO',
  DEBUG: 'DEBUG'
}

// 日志 API
export const logApi = {
  // 获取日志列表
  getLogs(params) {
    return request({
      url: '/logs',
      method: 'get',
      params
    })
  },

  // 获取日志详情
  getLogById(id) {
    return request({
      url: `/logs/${id}`,
      method: 'get'
    })
  },

  // 搜索日志
  searchLogs(params) {
    return request({
      url: '/logs/search',
      method: 'post',
      data: params
    })
  },

  // 获取日志统计
  getLogStats() {
    return request({
      url: '/logs/stats',
      method: 'get'
    })
  },

  // 获取指定应用的日志统计
  getApplicationLogStats(appId) {
    return request({
      url: `/logs/stats/${appId}`,
      method: 'get'
    })
  },



  // 删除日志（如果需要）
  deleteLog(id) {
    return request({
      url: `/logs/${id}`,
      method: 'delete'
    })
  },

  // 批量删除日志
  deleteLogs(ids) {
    return request({
      url: '/logs/batch',
      method: 'delete',
      data: { ids }
    })
  }
}
