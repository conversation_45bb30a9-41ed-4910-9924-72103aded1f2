@echo off
chcp 65001 >nul

:: 通用日志管理系统 - 简化部署脚本
:: 作者: Logger Management System
:: 版本: 1.0.0

echo ==========================================
echo 通用日志管理系统 - 简化部署脚本
echo ==========================================

:: 检查当前目录是否为scripts目录
if not exist "package-source.bat" (
    echo [ERROR] 请在scripts目录下运行此脚本
    echo [INFO] 当前目录: %CD%
    echo [INFO] 请切换到scripts目录后重新运行
    pause
    exit
)

:: 检查配置文件
if not exist "deploy-config.txt" (
    echo [ERROR] 配置文件不存在: deploy-config.txt
    echo [INFO] 正在创建配置文件模板...
    
    echo # 通用日志管理系统 - 部署配置文件> deploy-config.txt
    echo # 请根据实际情况修改以下配置>> deploy-config.txt
    echo.>> deploy-config.txt
    echo # Linux服务器配置>> deploy-config.txt
    echo SERVER_HOST=your-linux-server-ip>> deploy-config.txt
    echo SERVER_USER=your-username>> deploy-config.txt
    echo SERVER_PORT=22>> deploy-config.txt
    echo.>> deploy-config.txt
    echo # 部署路径>> deploy-config.txt
    echo DEPLOY_PATH=/home/<USER>/logger-management>> deploy-config.txt
    echo.>> deploy-config.txt
    echo # SSH密钥路径 ^(可选^)>> deploy-config.txt
    echo # SSH_KEY_PATH=C:\Users\<USER>\.ssh\id_rsa>> deploy-config.txt
    
    echo [INFO] 配置文件已创建: deploy-config.txt
    echo [INFO] 请编辑此文件后重新运行脚本
    pause
    exit
)

echo [INFO] 开始部署流程...

:: 步骤1: 打包源码
echo.
echo [STEP 1/3] 打包源码...
if exist "package-source.bat" (
    call package-source.bat
    if errorlevel 1 (
        echo [ERROR] 源码打包失败
        pause
        exit
    )
) else (
    echo [ERROR] 找不到 package-source.bat 文件
    pause
    exit
)

:: 步骤2: 检查是否有源码包
echo.
echo [STEP 2/3] 检查源码包...
set PACKAGE_FILE=
for /f "delims=" %%i in ('dir /b /o-d "logger-management-*.zip" 2^>nul') do (
    set PACKAGE_FILE=%%i
    goto :found_package
)

:found_package
if not defined PACKAGE_FILE (
    echo [ERROR] 未找到源码包文件
    pause
    exit
)

echo [INFO] 找到源码包: %PACKAGE_FILE%

:: 步骤3: 显示传输说明
echo.
echo [STEP 3/3] 文件传输说明
echo ==========================================
echo 源码包已准备完成: %PACKAGE_FILE%
echo.
echo 请按以下步骤完成部署:
echo.
echo 1. 将源码包传输到Linux服务器
echo    方法一: 使用SCP命令
echo      scp %PACKAGE_FILE% username@server-ip:/home/<USER>/
echo.
echo    方法二: 使用SFTP工具
echo      - WinSCP: https://winscp.net/
echo      - FileZilla: https://filezilla-project.org/
echo.
echo 2. 登录到Linux服务器
echo    ssh username@server-ip
echo.
echo 3. 解压并部署
echo    unzip %PACKAGE_FILE%
echo    cd logger-management
echo    chmod +x scripts/deploy-on-linux.sh
echo    ./scripts/deploy-on-linux.sh
echo.
echo ==========================================

:: 读取配置文件显示服务器信息
for /f "tokens=1,2 delims==" %%a in (deploy-config.txt) do (
    if "%%a"=="SERVER_HOST" set SERVER_HOST=%%b
    if "%%a"=="SERVER_USER" set SERVER_USER=%%b
    if "%%a"=="DEPLOY_PATH" set DEPLOY_PATH=%%b
)

if defined SERVER_HOST (
    echo 目标服务器信息:
    echo   服务器: %SERVER_HOST%
    echo   用户: %SERVER_USER%
    echo   路径: %DEPLOY_PATH%
    echo.
    echo 部署完成后访问地址:
    echo   前端: http://%SERVER_HOST%
    echo   后端API: http://%SERVER_HOST%:8080/api
    echo ==========================================
)

echo.
echo [SUCCESS] 源码包准备完成！
echo [INFO] 请按照上述说明完成文件传输和部署

pause
