server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: logger-management-backend
  
  data:
    mongodb:
      host: mongodb
      port: 27017
      database: logger_management
      username: logger_user
      password: logger_password
      authentication-database: logger_management
  
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
    default-property-inclusion: non_null

# 日志配置
logging:
  level:
    com.logmanagement: INFO
    org.springframework.data.mongodb: WARN
    org.springframework.web: WARN
    org.springframework.security: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: /app/logs/application.log
    max-size: 100MB
    max-history: 30

# 应用自定义配置
app:
  cors:
    allowed-origins: "*"
    allowed-methods: "GET,POST,PUT,DELETE,OPTIONS"
    allowed-headers: "*"
    allow-credentials: true

  jwt:
    secret: "LoggerManagementSystemSecretKeyForJWTTokenGeneration2024!@#$%^&*()_+{}|:<>?[]\\;'\",./"
    expiration: 86400

  pagination:
    default-page-size: 10
    max-page-size: 100

# Knife4j配置 - 生产环境禁用
knife4j:
  enable: false
  production: true

# Spring Boot Actuator配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
  health:
    mongo:
      enabled: true
