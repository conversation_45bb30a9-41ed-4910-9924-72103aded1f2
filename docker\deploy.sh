#!/bin/bash

# 通用日志管理系统 Docker 部署脚本
# 作者: Logger Management System
# 版本: 1.0.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker和Docker Compose
check_requirements() {
    log_info "检查系统要求..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    log_success "系统要求检查通过"
}

# 构建镜像
build_images() {
    log_info "构建应用镜像..."
    
    # 构建后端镜像
    log_info "构建后端镜像..."
    docker-compose build backend
    
    # 构建前端镜像
    log_info "构建前端镜像..."
    docker-compose build frontend
    
    log_success "镜像构建完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 启动数据库
    log_info "启动 MongoDB..."
    docker-compose up -d mongodb
    
    # 等待数据库启动
    log_info "等待 MongoDB 启动..."
    sleep 30
    
    # 启动后端服务
    log_info "启动后端服务..."
    docker-compose up -d backend
    
    # 等待后端启动
    log_info "等待后端服务启动..."
    sleep 30
    
    # 启动前端服务
    log_info "启动前端服务..."
    docker-compose up -d frontend
    
    # 启动管理界面（可选）
    log_info "启动 MongoDB 管理界面..."
    docker-compose up -d mongo-express
    
    log_success "所有服务启动完成"
}

# 检查服务状态
check_status() {
    log_info "检查服务状态..."
    docker-compose ps
    
    log_info "检查服务健康状态..."
    docker-compose exec backend curl -f http://localhost:8080/api/actuator/health || log_warning "后端服务健康检查失败"
    docker-compose exec frontend wget --no-verbose --tries=1 --spider http://localhost/ || log_warning "前端服务健康检查失败"
}

# 显示访问信息
show_access_info() {
    log_success "部署完成！"
    echo ""
    echo "=========================================="
    echo "通用日志管理系统访问信息"
    echo "=========================================="
    echo "前端地址: http://localhost"
    echo "后端API: http://localhost:8080/api"
    echo "MongoDB管理: http://localhost:8081"
    echo "  用户名: admin"
    echo "  密码: admin123"
    echo "=========================================="
    echo ""
    echo "常用命令:"
    echo "  查看日志: docker-compose logs -f [service_name]"
    echo "  停止服务: docker-compose down"
    echo "  重启服务: docker-compose restart [service_name]"
    echo "  查看状态: docker-compose ps"
    echo "=========================================="
}

# 主函数
main() {
    echo "=========================================="
    echo "通用日志管理系统 Docker 部署脚本"
    echo "=========================================="
    
    check_requirements
    build_images
    start_services
    sleep 10
    check_status
    show_access_info
}

# 执行主函数
main "$@"
