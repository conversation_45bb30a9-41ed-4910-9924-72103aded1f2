package com.logmanagement.backend.controller;

import com.logmanagement.backend.config.PaginationConfig;
import com.logmanagement.backend.dto.ApiResponse;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 系统配置控制器
 * 
 * 提供系统相关的配置信息和状态查询，包括：
 * - 分页配置信息
 * - 系统状态信息
 * - API版本信息
 * 
 * 这些接口主要用于前端获取系统配置，以便正确地调用其他API
 * 
 * <AUTHOR> Management System
 * @version 1.0.0
 */
@Api(tags = "系统配置", description = "系统配置信息和状态查询接口")
@RestController
@RequestMapping("/system")
public class SystemController {

    @Autowired
    private PaginationConfig paginationConfig;

    /**
     * 获取分页配置信息
     * 
     * 返回系统的分页配置，包括默认页面大小和最大页面大小限制
     * 前端可以根据这些配置来设置分页参数
     * 
     * @return 分页配置信息
     */
    @ApiOperation(
        value = "获取分页配置", 
        notes = "获取系统的分页配置信息，包括默认页面大小和最大页面大小限制。" +
                "前端可以根据这些配置来正确设置分页参数。"
    )
    @GetMapping("/pagination-config")
    public ResponseEntity<ApiResponse<Map<String, Integer>>> getPaginationConfig() {
        Map<String, Integer> config = new HashMap<>();
        config.put("defaultPageSize", paginationConfig.getDefaultPageSize());
        config.put("maxPageSize", paginationConfig.getMaxPageSize());
        
        return ResponseEntity.ok(ApiResponse.success("获取分页配置成功", config));
    }

    /**
     * 获取系统配置信息
     * 
     * 返回系统的基本配置信息，包括版本、环境等
     * 
     * @return 系统配置信息
     */
    @ApiOperation(
        value = "获取系统配置", 
        notes = "获取系统的基本配置信息，包括API版本、系统名称等。"
    )
    @GetMapping("/config")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getSystemConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("systemName", "Logger Management System");
        config.put("version", "1.0.0");
        config.put("apiVersion", "v1");
        config.put("description", "通用日志管理系统后端API");
        
        // 分页配置
        Map<String, Integer> paginationInfo = new HashMap<>();
        paginationInfo.put("defaultPageSize", paginationConfig.getDefaultPageSize());
        paginationInfo.put("maxPageSize", paginationConfig.getMaxPageSize());
        config.put("pagination", paginationInfo);
        
        // 支持的日志级别
        config.put("supportedLogLevels", new String[]{"ERROR", "WARN", "INFO", "DEBUG"});
        
        // 支持的用户角色
        config.put("supportedUserRoles", new String[]{"SUPER_ADMIN", "ADMIN", "USER"});
        
        // 支持的用户状态
        config.put("supportedUserStatuses", new String[]{"ACTIVE", "DISABLED"});
        
        // 支持的应用状态
        config.put("supportedApplicationStatuses", new String[]{"ACTIVE", "INACTIVE"});
        
        return ResponseEntity.ok(ApiResponse.success("获取系统配置成功", config));
    }

    /**
     * 健康检查
     * 
     * 检查系统是否正常运行
     * 
     * @return 系统状态信息
     */
    @ApiOperation(
        value = "健康检查", 
        notes = "检查系统是否正常运行，返回系统状态信息。"
    )
    @GetMapping("/health")
    public ResponseEntity<ApiResponse<Map<String, Object>>> healthCheck() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("timestamp", System.currentTimeMillis());
        health.put("uptime", System.currentTimeMillis()); // 简化的运行时间
        
        return ResponseEntity.ok(ApiResponse.success("系统运行正常", health));
    }
}
