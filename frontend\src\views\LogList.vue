<template>
  <div class="log-list">
    <!-- 搜索表单 -->
    <div class="search-section">
      <h2>日志搜索</h2>
      <el-form :model="searchForm" class="search-form">
        <!-- 第一行：时间筛选条件 -->
        <div class="search-row">
          <el-form-item label="起始时间" class="search-item">
            <el-date-picker
              v-model="searchForm.startTime"
              type="datetime"
              placeholder="选择起始时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              clearable
            />
          </el-form-item>

          <el-form-item label="结束时间" class="search-item">
            <el-date-picker
              v-model="searchForm.endTime"
              type="datetime"
              placeholder="选择结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              clearable
            />
          </el-form-item>

          <el-form-item label="级别" class="search-item">
            <el-select v-model="searchForm.level" placeholder="日志级别" clearable>
              <el-option label="ERROR" value="ERROR" />
              <el-option label="WARN" value="WARN" />
              <el-option label="INFO" value="INFO" />
              <el-option label="DEBUG" value="DEBUG" />
            </el-select>
          </el-form-item>
        </div>

        <!-- 第二行：基础筛选条件 -->
        <div class="search-row">
          <el-form-item label="应用名称" class="search-item">
            <el-select v-model="searchForm.applicationId" placeholder="应用名称" clearable>
              <el-option
                v-for="app in applications"
                :key="app.id"
                :label="getAppName(app)"
                :value="app.id"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="日志来源" class="search-item">
            <el-input v-model="searchForm.source" placeholder="请输入日志来源" clearable />
          </el-form-item>

          <el-form-item label="环境" class="search-item">
            <el-select v-model="searchForm.environment" placeholder="环境" clearable>
              <el-option label="开发" value="dev" />
              <el-option label="测试" value="test" />
              <el-option label="模拟" value="staging" />
              <el-option label="生产" value="prod" />
            </el-select>
          </el-form-item>
        </div>

        <!-- 第三行：关键词和属性搜索 -->
        <div class="search-row">
          <el-form-item label="消息内容" class="search-item">
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索内容"
              clearable
              @keyup.enter="handleSearch"
            />
          </el-form-item>

          <el-form-item label="属性搜索" class="search-item search-item-wide">
            <el-input
              v-model="searchForm.propertySearch"
              placeholder="搜索属性键或值"
              clearable
              @keyup.enter="handleSearch"
            >
              <template #prepend>
                <el-select v-model="searchForm.propertyType" style="width: 100px">
                  <el-option label="全部" value="all" />
                  <el-option label="扩展属性" value="extend" />
                  <el-option label="环境属性" value="environment" />
                  <el-option label="元数据" value="metadata" />
                </el-select>
              </template>
            </el-input>
          </el-form-item>
        </div>

        <!-- 操作按钮 -->
        <div class="search-buttons">
          <el-button type="primary" @click="handleSearch" :loading="loading">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </div>
      </el-form>
    </div>

    <!-- 日志表格 -->
    <div class="table-section">
      <div class="table-header">
        <h2>日志列表</h2>
        <div class="header-actions">
          <el-button
            type="danger"
            size="small"
            :disabled="!selectedLogs.length"
            @click="handleBatchDelete"
          >
            <el-icon><Delete /></el-icon>
            批量删除
          </el-button>
          <el-button size="small" @click="handleRefresh">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>
      
      <el-table
        :data="logs"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        stripe
        border
        style="width: 100%"
        :empty-text="loading ? '加载中...' : '暂无数据'"
      >
        <el-table-column type="selection" width="55" align="center" />

        <el-table-column prop="timestamp" label="时间" width="200" sortable class-name="time-column">
          <template #default="{ row }">
            <span style="color: #606266; font-size: 12px;">
              {{ formatTime(row.timestamp) }}
            </span>
          </template>
        </el-table-column>

        <el-table-column prop="level" label="级别" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getLevelTagType(row.level)" size="small">
              {{ row.level }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="applicationId" label="应用名称" width="150" show-overflow-tooltip>
          <template #default="{ row }">
            <span v-if="row.applicationId" style="color: #303133; font-weight: 500;">
              {{ getApplicationNameById(row.applicationId) }}
            </span>
            <span v-else style="color: #c0c4cc;">-</span>
          </template>
        </el-table-column>

        <el-table-column prop="source" label="来源" width="150" show-overflow-tooltip class-name="hidden-xs-only" />

        <el-table-column prop="environment" label="环境" width="100" align="center" class-name="hidden-xs-only">
          <template #default="{ row }">
            <el-tag v-if="row.environment" :type="getEnvTagType(row.environment)" size="small">
              {{ getEnvText(row.environment) }}
            </el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column prop="message" label="消息" min-width="300" show-overflow-tooltip>
          <template #default="{ row }">
            <span style="color: #303133;">{{ row.message }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="thread" label="线程" width="120" show-overflow-tooltip class-name="hidden-xs-only" />

        <el-table-column label="操作" width="280" fixed="right" align="center">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button type="primary" size="small" @click="handleViewDetail(row)">
                <el-icon><View /></el-icon>
                详情
              </el-button>
              <el-dropdown trigger="click" size="small">
                <el-button type="info" size="small">
                  <el-icon><InfoFilled /></el-icon>
                  属性
                  <el-icon><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item
                      @click="handleViewExtendProperties(row)"
                      :disabled="!row.extendProperties || Object.keys(row.extendProperties).length === 0"
                    >
                      <el-icon><InfoFilled /></el-icon>
                      扩展属性 ({{ row.extendProperties ? Object.keys(row.extendProperties).length : 0 }})
                      <el-icon v-if="hasPropertyMatch(row.extendProperties, 'extend')" style="margin-left: 4px; color: #409eff;"><Search /></el-icon>
                    </el-dropdown-item>
                    <el-dropdown-item
                      @click="handleViewEnvironmentProperties(row)"
                      :disabled="!row.environmentProperties || Object.keys(row.environmentProperties).length === 0"
                    >
                      <el-icon><Setting /></el-icon>
                      环境属性 ({{ row.environmentProperties ? Object.keys(row.environmentProperties).length : 0 }})
                      <el-icon v-if="hasPropertyMatch(row.environmentProperties, 'environment')" style="margin-left: 4px; color: #409eff;"><Search /></el-icon>
                    </el-dropdown-item>
                    <el-dropdown-item
                      @click="handleViewMetadata(row)"
                      :disabled="!row.metadata || Object.keys(row.metadata).length === 0"
                    >
                      <el-icon><InfoFilled /></el-icon>
                      元数据 ({{ row.metadata ? Object.keys(row.metadata).length : 0 }})
                      <el-icon v-if="hasPropertyMatch(row.metadata, 'metadata')" style="margin-left: 4px; color: #409eff;"><Search /></el-icon>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
              <el-button type="danger" size="small" @click="handleDelete(row)">
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <div class="pagination-info">
          <span class="query-time-info">
            查询用时 {{ queryTime }} 毫秒，共 {{ pagination.total }} 条
          </span>
        </div>
        <div class="pagination-controls">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.size"
            :page-sizes="getPageSizeOptions()"
            :total="pagination.total"
            layout="sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>

    <!-- 扩展属性对话框 -->
    <el-dialog
      v-model="showExtendPropsDialog"
      title="扩展属性"
      width="600px"
      :close-on-click-modal="false"
    >
      <div class="extend-props-content">
        <el-descriptions
          v-if="Object.keys(currentExtendProps).length > 0"
          :column="1"
          border
        >
          <el-descriptions-item
            v-for="(value, key) in currentExtendProps"
            :key="key"
            :label="key"
            :class="{ 'highlight-property': isPropertyHighlighted(key, value, 'extend') }"
          >
            <span style="word-break: break-all;" v-html="highlightSearchTerm(formatPropertyValue(value))"></span>
          </el-descriptions-item>
        </el-descriptions>

        <el-empty
          v-else
          description="暂无扩展属性"
          :image-size="100"
        />
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="showExtendPropsDialog = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 环境属性对话框 -->
    <el-dialog
      v-model="showEnvPropsDialog"
      title="环境属性"
      width="600px"
      :close-on-click-modal="false"
    >
      <div class="env-props-content">
        <el-descriptions
          v-if="Object.keys(currentEnvProps).length > 0"
          :column="1"
          border
        >
          <el-descriptions-item
            v-for="(value, key) in currentEnvProps"
            :key="key"
            :label="key"
            :class="{ 'highlight-property': isPropertyHighlighted(key, value, 'environment') }"
          >
            <span style="word-break: break-all;" v-html="highlightSearchTerm(formatPropertyValue(value))"></span>
          </el-descriptions-item>
        </el-descriptions>

        <el-empty
          v-else
          description="暂无环境属性"
          :image-size="100"
        />
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="showEnvPropsDialog = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 元数据对话框 -->
    <el-dialog
      v-model="showMetadataDialog"
      title="元数据"
      width="600px"
      :close-on-click-modal="false"
    >
      <div class="metadata-content">
        <el-descriptions
          v-if="Object.keys(currentMetadata).length > 0"
          :column="1"
          border
        >
          <el-descriptions-item
            v-for="(value, key) in currentMetadata"
            :key="key"
            :label="key"
            :class="{ 'highlight-property': isPropertyHighlighted(key, value, 'metadata') }"
          >
            <span style="word-break: break-all;" v-html="highlightSearchTerm(formatPropertyValue(value))"></span>
          </el-descriptions-item>
        </el-descriptions>

        <el-empty
          v-else
          description="暂无元数据"
          :image-size="100"
        />
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="showMetadataDialog = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 日志详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="日志详情"
      width="800px"
      :modal="false"
      :close-on-click-modal="false"
      :append-to-body="true"
      :lock-scroll="false"
      destroy-on-close
    >
      <div class="log-detail-content">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="日志ID">
            <span class="log-id">{{ currentLogDetail.id }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="应用名称">
            <el-tag type="primary">{{ getApplicationNameById(currentLogDetail.applicationId) }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="应用ID" v-if="currentLogDetail.applicationId">
            <span class="app-id">{{ currentLogDetail.applicationId }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="日志来源">
            <span>{{ currentLogDetail.source || '未知' }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="日志级别">
            <el-tag
              :type="getLevelTagType(currentLogDetail.level)"
              effect="dark"
            >
              {{ currentLogDetail.level }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="环境信息" v-if="currentLogDetail.environment">
            <el-tag type="success" effect="plain">{{ currentLogDetail.environment }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="线程名称" v-if="currentLogDetail.thread">
            <span class="thread-name">{{ currentLogDetail.thread }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="日志时间">
            <span class="time-text">{{ formatTime(currentLogDetail.timestamp) }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间" v-if="currentLogDetail.createdAt">
            <span class="time-text">{{ formatTime(currentLogDetail.createdAt) }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="更新时间" v-if="currentLogDetail.updatedAt">
            <span class="time-text">{{ formatTime(currentLogDetail.updatedAt) }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="日志内容">
            <div class="log-message-detail">
              {{ currentLogDetail.message }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="异常信息" v-if="currentLogDetail.exception">
            <div class="exception-detail">
              {{ currentLogDetail.exception }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item
            v-if="currentLogDetail.extendProperties && Object.keys(currentLogDetail.extendProperties).length > 0"
            label="扩展属性"
          >
            <div class="properties-summary">
              <el-button
                type="primary"
                size="small"
                @click="handleViewExtendProperties(currentLogDetail)"
              >
                查看扩展属性 ({{ Object.keys(currentLogDetail.extendProperties).length }})
              </el-button>
            </div>
          </el-descriptions-item>
          <el-descriptions-item
            v-if="currentLogDetail.environmentProperties && Object.keys(currentLogDetail.environmentProperties).length > 0"
            label="环境属性"
          >
            <div class="properties-summary">
              <el-button
                type="success"
                size="small"
                @click="handleViewEnvironmentProperties(currentLogDetail)"
              >
                查看环境属性 ({{ Object.keys(currentLogDetail.environmentProperties).length }})
              </el-button>
            </div>
          </el-descriptions-item>
          <el-descriptions-item
            v-if="currentLogDetail.metadata && Object.keys(currentLogDetail.metadata).length > 0"
            label="元数据"
          >
            <div class="properties-summary">
              <el-button
                type="info"
                size="small"
                @click="handleViewMetadata(currentLogDetail)"
              >
                查看元数据 ({{ Object.keys(currentLogDetail.metadata).length }})
              </el-button>
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="showDetailDialog = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Search, Refresh, Delete, View, InfoFilled, Setting, ArrowDown } from '@element-plus/icons-vue'
import { logApi, LogLevel } from '../api/logs'
import { useAuthStore } from '../stores/auth'
import { useConfigStore } from '../stores/config'
import { formatTime } from '../utils/dateTime'
import { Message, MessageBox } from '../utils/message'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()
const configStore = useConfigStore()

// 响应式数据
const loading = ref(false)
const logs = ref([])

const applications = ref([])
const selectedLogs = ref([])
const showExtendPropsDialog = ref(false)
const currentExtendProps = ref({})
const showEnvPropsDialog = ref(false)
const currentEnvProps = ref({})
const showMetadataDialog = ref(false)
const currentMetadata = ref({})
const showDetailDialog = ref(false)
const currentLogDetail = ref({})

// 搜索表单
const searchForm = reactive({
  page: 1,
  size: configStore.getDefaultPageSize(),
  level: undefined,
  applicationId: undefined,
  source: undefined,
  environment: undefined,
  startTime: undefined,
  endTime: undefined,
  keyword: undefined,
  propertySearch: undefined,
  propertyType: 'all'
})

// 分页信息
const pagination = reactive({
  page: 1,
  size: configStore.getDefaultPageSize(),
  total: 0
})

// 查询用时
const queryTime = ref(0)

// 获取页面大小选项
const getPageSizeOptions = () => {
  const maxSize = configStore.getMaxPageSize()
  const options = [10, 20, 50]
  if (maxSize >= 100) {
    options.push(100)
  }
  if (maxSize > 100) {
    options.push(maxSize)
  }
  return options
}

// 获取日志级别标签类型
const getLevelTagType = (level) => {
  const typeMap = {
    ERROR: 'danger',
    WARN: 'warning',
    INFO: 'info',
    DEBUG: 'success'
  }
  return typeMap[level] || 'info'
}

// 获取环境标签类型
const getEnvTagType = (env) => {
  const typeMap = {
    dev: 'warning',
    test: 'info',
    staging: 'primary',
    prod: 'success'
  }
  return typeMap[env] || 'info'
}

// 获取环境文本
const getEnvText = (env) => {
  const textMap = {
    dev: '开发',
    test: '测试',
    staging: '模拟',
    prod: '生产'
  }
  return textMap[env] || env
}



// 获取应用名称（防御性编程）
const getAppName = (app) => {
  if (!app) return ''

  // 如果name是数组，取第一个元素
  if (Array.isArray(app.name)) {
    return app.name[0] || ''
  }

  // 确保返回字符串
  return String(app.name || '')
}

// 根据应用ID获取应用名称
const getApplicationNameById = (applicationId) => {
  if (!applicationId) return '-'
  const app = applications.value.find(app => app.id === applicationId)
  return app ? getAppName(app) : applicationId
}



// 搜索
const handleSearch = async () => {
  searchForm.page = 1
  pagination.page = 1
  await loadLogs()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    page: 1,
    size: configStore.getDefaultPageSize(),
    level: undefined,
    applicationId: undefined,
    source: undefined,
    environment: undefined,
    startTime: undefined,
    endTime: undefined,
    keyword: undefined,
    propertySearch: undefined,
    propertyType: 'all'
  })
  pagination.page = 1
  loadLogs()
}

// 刷新
const handleRefresh = () => {
  loadLogs()
}

// 选择变化
const handleSelectionChange = (selection) => {
  selectedLogs.value = selection
}

// 查看详情
const handleViewDetail = (log) => {
  currentLogDetail.value = log
  showDetailDialog.value = true
}

// 查看扩展属性
const handleViewExtendProperties = (log) => {
  currentExtendProps.value = log.extendProperties || {}
  showExtendPropsDialog.value = true
}

// 查看环境属性
const handleViewEnvironmentProperties = (log) => {
  currentEnvProps.value = log.environmentProperties || {}
  showEnvPropsDialog.value = true
}

// 查看元数据
const handleViewMetadata = (log) => {
  currentMetadata.value = log.metadata || {}
  showMetadataDialog.value = true
}

// 格式化属性值
const formatPropertyValue = (value) => {
  if (typeof value === 'object' && value !== null) {
    return JSON.stringify(value, null, 2)
  }
  return String(value)
}

// 检查属性是否匹配搜索条件
const hasPropertyMatch = (properties, type) => {
  if (!searchForm.propertySearch || !properties) return false

  // 检查属性类型是否匹配
  if (searchForm.propertyType !== 'all' && searchForm.propertyType !== type) return false

  const searchTerm = searchForm.propertySearch.toLowerCase()

  for (const [key, value] of Object.entries(properties)) {
    if (key.toLowerCase().includes(searchTerm) ||
        String(value).toLowerCase().includes(searchTerm)) {
      return true
    }
  }

  return false
}

// 检查单个属性是否高亮
const isPropertyHighlighted = (key, value, type) => {
  if (!searchForm.propertySearch) return false

  // 检查属性类型是否匹配
  if (searchForm.propertyType !== 'all' && searchForm.propertyType !== type) return false

  const searchTerm = searchForm.propertySearch.toLowerCase()
  return key.toLowerCase().includes(searchTerm) ||
         String(value).toLowerCase().includes(searchTerm)
}

// 高亮搜索词
const highlightSearchTerm = (text) => {
  if (!searchForm.propertySearch || !text) return text

  const searchTerm = searchForm.propertySearch
  const regex = new RegExp(`(${searchTerm})`, 'gi')
  return String(text).replace(regex, '<mark style="background-color: #fffacd; padding: 1px 2px; border-radius: 2px;">$1</mark>')
}

// 删除单个日志
const handleDelete = async (log) => {
  try {
    // 检查日志ID是否有效
    if (!log.id || log.id.trim() === '') {
      Message.error('无法删除：日志ID无效')
      return
    }

    await MessageBox.delete('确定要删除这条日志吗？', '确认删除')

    await logApi.deleteLog(log.id)
    Message.success('删除成功')
    await loadLogs()
  } catch (error) {
    if (error !== 'cancel') {
      Message.error('删除失败')
    }
  }
}

// 批量删除
const handleBatchDelete = async () => {
  try {
    // 过滤掉无效ID的日志
    const validLogs = selectedLogs.value.filter(log => log.id && log.id.trim() !== '')

    if (validLogs.length === 0) {
      Message.error('没有可删除的有效日志')
      return
    }

    if (validLogs.length < selectedLogs.value.length) {
      Message.warning(`已过滤掉 ${selectedLogs.value.length - validLogs.length} 条无效ID的日志`)
    }

    await MessageBox.delete(`确定要删除选中的 ${validLogs.length} 条日志吗？`, '确认删除')

    const ids = validLogs.map(log => log.id)
    await logApi.deleteLogs(ids)
    Message.success('批量删除成功')
    await loadLogs()
  } catch (error) {
    if (error !== 'cancel') {
      Message.error('批量删除失败')
    }
  }
}

// 分页大小变化
const handleSizeChange = (size) => {
  searchForm.size = size
  pagination.size = size
  loadLogs()
}

// 当前页变化
const handleCurrentChange = (page) => {
  searchForm.page = page
  pagination.page = page
  loadLogs()
}

// 加载日志数据
const loadLogs = async () => {
  try {
    loading.value = true

    // 准备搜索参数
    let searchParams = { ...searchForm }

    // 保留必要的分页参数，只清理搜索条件中的空值
    const requiredParams = ['page', 'size', 'sort', 'order']
    Object.keys(searchParams).forEach(key => {
      if (!requiredParams.includes(key) &&
          (searchParams[key] === undefined || searchParams[key] === null || searchParams[key] === '')) {
        searchParams[key] = null // 设置为null而不是删除，保持参数结构完整
      }
    })

    let response
    let filteredLogs = []

    try {
      // 尝试调用真实API
      response = await logApi.getLogs(searchParams)

      if (response.code === 200 && response.data) {
        // 处理后端分页响应格式
        if (response.data.content) {
          filteredLogs = response.data.content
          pagination.total = response.data.totalElements || 0
        } else if (Array.isArray(response.data)) {
          filteredLogs = response.data
          pagination.total = response.data.length
        } else {
          filteredLogs = []
          pagination.total = 0
        }

        // 更新查询用时
        queryTime.value = response.queryTime || 0
      } else {
        throw new Error(response.message || '获取日志失败')
      }
    } catch (error) {
      console.error('获取日志失败:', error)
      logs.value = []
      pagination.total = 0
      ElMessage.error('获取日志失败，请检查后端服务是否正常运行')
      return
    }

    // 后端已支持属性搜索，不需要客户端过滤
    logs.value = filteredLogs

  } catch (error) {
    console.error('加载日志失败:', error)
    logs.value = []
    pagination.total = 0
    ElMessage.error('加载日志失败，请检查后端服务是否正常运行')
  } finally {
    loading.value = false
  }
}



// 加载应用列表
const loadApplications = async () => {
  try {
    // 导入应用API
    const { applicationApi } = await import('../api/applications')

    let response
    try {
      // 尝试使用真实API
      response = await applicationApi.getApplications({ page: 1, size: 100 })

      if (response.code === 200 && response.data) {
        let appList = response.data.content || []

        // 如果不是管理员，只显示用户有权限的应用
        if (!authStore.isAdmin && authStore.user) {
          const authorizedAppIds = authStore.user.authorizedAppIds || []
          appList = appList.filter(app =>
            authorizedAppIds.includes(app.id) || app.creatorId === authStore.user.id
          )
        }

        applications.value = appList


      } else {
        throw new Error(response.message || '获取应用列表失败')
      }
    } catch (error) {
      console.error('加载应用列表失败:', error)
      applications.value = []
      ElMessage.error('获取应用列表失败，请检查后端服务是否正常运行')
    }
  } catch (error) {
    console.error('加载应用列表失败:', error)
    applications.value = []
    ElMessage.error('获取应用列表失败，请检查后端服务是否正常运行')
  }
}

// 初始化URL参数
const initializeFromUrlParams = () => {
  // 从URL参数设置搜索表单
  if (route.query.level) {
    searchForm.level = route.query.level
  }

  if (route.query.applicationId) {
    searchForm.applicationId = route.query.applicationId
  }

  if (route.query.appId) {
    // 兼容旧的appId参数名
    searchForm.applicationId = route.query.appId
  }

  if (route.query.applicationName) {
    // 如果有applicationName参数，需要根据应用名称查找应用ID
    // 这里先设置一个标记，等应用列表加载完成后再处理
    searchForm.tempApplicationName = route.query.applicationName
  }

  if (route.query.source) {
    searchForm.source = route.query.source
  }

  if (route.query.environment) {
    searchForm.environment = route.query.environment
  }

  if (route.query.startTime) {
    searchForm.startTime = route.query.startTime
  }

  if (route.query.endTime) {
    searchForm.endTime = route.query.endTime
  }

  if (route.query.keyword) {
    searchForm.keyword = route.query.keyword
  }
}



// 组件挂载时加载数据
onMounted(async () => {
  // 先初始化URL参数
  initializeFromUrlParams()

  // 加载基础数据
  await loadApplications()

  // 如果有tempApplicationName参数，需要转换为应用ID
  if (searchForm.tempApplicationName && applications.value.length > 0) {
    const app = applications.value.find(app => getAppName(app) === searchForm.tempApplicationName)
    if (app) {
      searchForm.applicationId = app.id
    }
    // 清除临时的应用名称参数
    delete searchForm.tempApplicationName
  }

  // 最后加载日志数据（会应用URL参数中的过滤条件）
  loadLogs()
})
</script>

<style scoped>
.log-list {
  background-color: var(--bg-color);
  min-height: calc(100vh - var(--header-height));
  max-width: var(--content-max-width);
  margin: 0 auto;
}

.search-section {
  background: white;
  padding: 16px;
  border-radius: 8px;
  margin: 0 var(--container-padding) 16px var(--container-padding);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-section h2 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

/* 搜索表单行布局 */
.search-row {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  margin-bottom: 16px;
  align-items: center;
}

.search-row:last-of-type {
  margin-bottom: 20px;
}

.search-item {
  margin: 0 !important;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 10px;
  height: 36px;
  min-width: 0;
}

.search-item .el-form-item__label {
  margin-bottom: 0 !important;
  font-weight: 500;
  color: #606266;
  font-size: 14px;
  line-height: 36px;
  white-space: nowrap;
  width: 90px;
  text-align: right;
  flex-shrink: 0;
}

.search-item .el-form-item__content {
  margin-left: 0 !important;
  flex: 1;
  height: 30px;
  display: flex;
  align-items: center;
  min-width: 0;
  width: 200px;
}

.search-item .el-select,
.search-item .el-input,
.search-item .el-date-editor {
  width: 200px !important;
  height: 30px !important;
  min-width: 200px;
}

.search-item .el-select .el-input__inner,
.search-item .el-input__inner,
.search-item .el-date-editor .el-input__inner {
  height: 30px !important;
  line-height: 30px !important;
  font-size: 13px !important;
}

/* 属性搜索项占用更多空间 */
.search-item-wide {
  grid-column: span 2;
}

.search-item-wide .el-form-item__label {
  width: 90px;
  text-align: right;
  flex-shrink: 0;
}

.search-item-wide .el-form-item__content {
  min-width: 0;
  width: auto;
  flex: 1;
}

.search-item-wide .el-select,
.search-item-wide .el-input,
.search-item-wide .el-date-editor {
  width: 100% !important;
  min-width: 300px;
}

.search-form {
  margin: 0;
}

/* 时间选择器样式 */
.search-form .el-date-editor {
  width: 200px;
}

.search-form .el-date-editor .el-input__inner {
  font-size: 13px;
}

.search-buttons {
  display: flex;
  gap: 16px;
  align-items: center;
  justify-content: center;
  margin-top: 8px;
}

.search-buttons .el-button {
  height: 32px;
  padding: 6px 20px;
  font-size: 14px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  gap: 6px;
  min-width: 100px;
  justify-content: center;
  font-weight: 500;
}

.table-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin: 0 var(--container-padding);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #ebeef5;
  background-color: #fafafa;
}

.table-header h2 {
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.header-actions .el-button {
  height: 32px;
  padding: 8px 16px;
  font-size: 13px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.pagination-wrapper {
  padding: 20px;
  background-color: #fafafa;
  border-top: 1px solid #ebeef5;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 20px;
}

.pagination-info {
  display: flex;
  align-items: center;
}

.query-time-info {
  color: #606266;
  font-size: 13px;
  font-weight: 500;
  white-space: nowrap;
}

.pagination-controls {
  display: flex;
  align-items: center;
}

/* 表格样式优化 */
:deep(.el-table) {
  border: none;
}

:deep(.el-table th) {
  background-color: #f8f9fa;
  color: #606266;
  font-weight: bold;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f0f0f0;
  padding: 6px 0;
}

:deep(.el-table th) {
  padding: 6px 0;
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background-color: #fafafa;
}

:deep(.el-table__body tr:hover td) {
  background-color: #f5f7fa !important;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
  align-items: center;
  flex-wrap: nowrap;
}

.action-buttons .el-button {
  min-width: 65px;
  height: 32px;
  padding: 6px 10px;
  font-size: 12px;
  border-radius: 6px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
}

.action-buttons .el-button .el-icon {
  margin-right: 4px;
  font-size: 12px;
}

.action-buttons .el-dropdown {
  display: inline-block;
}

.action-buttons .el-dropdown .el-button {
  min-width: 70px;
}

/* 扩展属性对话框样式 */
.extend-props-content {
  max-height: 400px;
  overflow-y: auto;
}

.extend-props-content .el-descriptions {
  margin-top: 0;
}

.extend-props-content .el-descriptions-item__label {
  font-weight: 600;
  color: #303133;
  background-color: #f8f9fa;
}

.extend-props-content .el-descriptions-item__content {
  word-break: break-all;
  white-space: pre-wrap;
  font-family: 'Courier New', monospace;
  font-size: 13px;
}

/* 日志详情对话框样式 */
.log-detail-content {
  max-height: 600px;
  overflow-y: auto;
}

.log-detail-content .el-descriptions {
  margin-top: 0;
}

.log-detail-content .el-descriptions-item__label {
  font-weight: 600;
  color: #303133;
  background-color: #f8f9fa;
  width: 120px;
}

.log-detail-content .el-descriptions-item__content {
  word-break: break-all;
}

.log-id {
  font-family: 'Courier New', monospace;
  font-size: 13px;
  color: #409eff;
  font-weight: 600;
}

.app-id {
  font-family: 'Courier New', monospace;
  font-size: 13px;
  color: #67c23a;
  font-weight: 500;
}

.thread-name {
  font-family: 'Courier New', monospace;
  font-size: 13px;
  color: #e6a23c;
  font-weight: 500;
}

.log-message-detail {
  max-height: 200px;
  overflow-y: auto;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-all;
}

.exception-detail {
  max-height: 300px;
  overflow-y: auto;
  padding: 12px;
  background-color: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-all;
  color: #f56c6c;
}

.properties-summary {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 环境属性对话框样式 */
.env-props-content {
  max-height: 400px;
  overflow-y: auto;
}

.env-props-content .el-descriptions {
  margin-top: 0;
}

.env-props-content .el-descriptions-item__label {
  font-weight: 600;
  color: #303133;
  background-color: #e8f4fd;
}

.env-props-content .el-descriptions-item__content {
  word-break: break-all;
  white-space: pre-wrap;
  font-family: 'Courier New', monospace;
  font-size: 13px;
}

/* 属性搜索高亮样式 */
.highlight-property {
  background-color: #f0f9ff !important;
  border-left: 3px solid #409eff !important;
}

.highlight-property .el-descriptions-item__label {
  background-color: #e1f5fe !important;
  color: #1976d2 !important;
  font-weight: 700 !important;
}

/* 搜索词高亮 */
:deep(mark) {
  background-color: #fffacd !important;
  padding: 1px 2px !important;
  border-radius: 2px !important;
  font-weight: 600 !important;
}

/* 属性下拉菜单样式 */
:deep(.el-dropdown-menu__item) {
  padding: 8px 16px;
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 6px;
}

:deep(.el-dropdown-menu__item .el-icon) {
  font-size: 14px;
}

:deep(.el-dropdown-menu__item:not(.is-disabled):hover) {
  background-color: #f5f7fa;
  color: #409eff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .log-list {
    min-height: calc(100vh - 120px);
  }

  .page-header {
    margin-bottom: 16px;
  }

  .page-header h1 {
    font-size: 20px;
    text-align: center;
    margin-bottom: 12px;
  }

  .search-section {
    margin-bottom: 12px;
    padding: 12px;
  }

  /* 移动端搜索行调整为单列 */
  .search-row {
    grid-template-columns: 1fr;
    gap: 12px;
    margin-bottom: 16px;
  }

  .search-item {
    flex-direction: column;
    align-items: stretch;
    gap: 6px;
    height: auto;
  }

  .search-item .el-form-item__label {
    font-size: 13px;
    margin-bottom: 0 !important;
    text-align: left;
    width: auto;
    line-height: 1.5;
  }

  .search-item .el-form-item__content {
    width: 100% !important;
  }

  .search-item .el-select,
  .search-item .el-input,
  .search-item .el-date-editor {
    width: 100% !important;
    min-width: auto !important;
  }

  .search-item-wide {
    grid-column: span 1;
  }

  .search-item-wide .el-select,
  .search-item-wide .el-input,
  .search-item-wide .el-date-editor {
    width: 100% !important;
    min-width: auto !important;
  }

  .search-buttons {
    justify-content: center;
    gap: 10px;
    margin-top: 6px;
  }

  .search-buttons .el-button {
    flex: 1;
    max-width: 120px;
    height: 32px;
    padding: 6px 14px;
    font-size: 13px;
  }

  .table-section {
    margin-bottom: 16px;
  }

  .table-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
    padding: 16px;
  }

  .table-header h2 {
    font-size: 16px;
    text-align: center;
  }

  .header-actions {
    justify-content: center;
    gap: 10px;
  }

  .header-actions .el-button {
    height: 32px;
    padding: 8px 16px;
    font-size: 12px;
    flex: 1;
    max-width: 120px;
  }

  /* 移动端表格优化 */
  :deep(.el-table) {
    font-size: 12px;
  }

  :deep(.el-table .el-table__header th) {
    padding: 4px 4px;
    font-size: 12px;
    font-weight: bold;
  }

  :deep(.el-table .el-table__body td) {
    padding: 4px 4px;
    font-size: 12px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 6px;
  }

  .action-buttons .el-button {
    width: 100%;
    height: 28px;
    padding: 4px 8px;
    font-size: 11px;
  }

  .action-buttons .el-dropdown .el-button {
    width: 100%;
    min-width: auto;
  }

  .pagination-wrapper {
    padding: 16px;
  }

  :deep(.el-pagination) {
    justify-content: center;
  }

  :deep(.el-pagination .el-pager li) {
    min-width: 28px;
    height: 28px;
    line-height: 28px;
    font-size: 12px;
  }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {

  .page-header h1 {
    font-size: 18px;
  }

  .search-section {
    padding: 10px;
  }

  .search-buttons .el-button {
    height: 30px;
    font-size: 12px;
    padding: 5px 12px;
  }

  .table-header {
    padding: 12px;
  }

  .header-actions .el-button {
    height: 28px;
    padding: 6px 12px;
    font-size: 11px;
  }

  /* 隐藏部分表格列以适应小屏幕 */
  :deep(.el-table .hidden-xs-only) {
    display: none !important;
  }
}
</style>
