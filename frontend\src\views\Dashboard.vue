<template>
  <div class="dashboard">
    <div class="dashboard-header">
      <h1>仪表板</h1>
      <p>通用日志管理系统概览</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card total-card">
        <div class="stat-icon">
          <el-icon size="32"><Document /></el-icon>
        </div>
        <div class="stat-info">
          <div class="stat-number">{{ stats.total.toLocaleString() }}</div>
          <div class="stat-label">总日志数</div>
        </div>
      </div>

      <div class="stat-card error-card">
        <div class="stat-icon">
          <el-icon size="32"><Warning /></el-icon>
        </div>
        <div class="stat-info">
          <div class="stat-number">{{ stats.errorCount.toLocaleString() }}</div>
          <div class="stat-label">错误日志</div>
        </div>
      </div>

      <div class="stat-card warn-card">
        <div class="stat-icon">
          <el-icon size="32"><InfoFilled /></el-icon>
        </div>
        <div class="stat-info">
          <div class="stat-number">{{ stats.warnCount.toLocaleString() }}</div>
          <div class="stat-label">警告日志</div>
        </div>
      </div>

      <div class="stat-card today-card">
        <div class="stat-icon">
          <el-icon size="32"><Calendar /></el-icon>
        </div>
        <div class="stat-info">
          <div class="stat-number">{{ stats.todayCount.toLocaleString() }}</div>
          <div class="stat-label">今日日志</div>
        </div>
      </div>
    </div>

    <!-- 快速操作 -->
    <div class="quick-actions-section">
      <h2>快速操作</h2>
      <div class="action-grid">
        <div class="action-card" @click="goToLogs">
          <div class="action-icon primary">
            <el-icon><List /></el-icon>
          </div>
          <div class="action-content">
            <h3>查看日志</h3>
            <p>浏览和搜索系统日志</p>
          </div>
        </div>

        <div class="action-card" @click="goToErrorLogs">
          <div class="action-icon danger">
            <el-icon><Warning /></el-icon>
          </div>
          <div class="action-content">
            <h3>错误日志</h3>
            <p>查看系统错误信息</p>
          </div>
        </div>

        <div class="action-card" @click="refreshStats">
          <div class="action-icon info">
            <el-icon><Refresh /></el-icon>
          </div>
          <div class="action-content">
            <h3>刷新数据</h3>
            <p>更新仪表板统计</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 最近日志 -->
    <div class="recent-logs-section">
      <div class="section-header">
        <h2>最近日志</h2>
        <el-button size="small" @click="goToLogs">
          查看更多
          <el-icon><ArrowRight /></el-icon>
        </el-button>
      </div>

      <div class="recent-logs-table">
        <el-table :data="recentLogs" v-loading="logsLoading" stripe>
          <el-table-column prop="timestamp" label="时间" width="200" class-name="time-column">
            <template #default="{ row }">
              {{ formatTime(row.timestamp) }}
            </template>
          </el-table-column>

          <el-table-column prop="level" label="级别" width="100">
            <template #default="{ row }">
              <el-tag :type="getLevelTagType(row.level)" size="small">
                {{ row.level }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="source" label="来源" width="150" />

          <el-table-column prop="applicationName" label="应用名称" width="150" show-overflow-tooltip />

          <el-table-column prop="message" label="消息" show-overflow-tooltip />

          <el-table-column label="操作" width="100">
            <template #default="{ row }">
              <el-button type="primary" size="small" @click="viewLogDetail(row)">
                详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onActivated, onDeactivated } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  Document,
  Warning,
  InfoFilled,
  Calendar,
  Refresh,
  List,
  Clock,
  ArrowRight
} from '@element-plus/icons-vue'
import { logApi, LogLevel } from '../api/logs'
import { useAuthStore } from '../stores/auth'
import { useConfigStore } from '../stores/config'
import { formatTime } from '../utils/dateTime'
import { Message } from '../utils/message'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()
const configStore = useConfigStore()

// 响应式数据
const statsLoading = ref(false)
const logsLoading = ref(false)
const recentLogs = ref([])
const isFirstLoad = ref(true) // 标记是否是首次加载

// 统计数据
const stats = reactive({
  total: 0,
  errorCount: 0,
  warnCount: 0,
  infoCount: 0,
  debugCount: 0,
  todayCount: 0,
  recentHourCount: 0
})

// 获取百分比
const getPercentage = (count) => {
  if (stats.total === 0) return 0
  return Math.round((count / stats.total) * 100)
}

// 获取日志级别标签类型
const getLevelTagType = (level) => {
  const typeMap = {
    ERROR: 'danger',
    WARN: 'warning',
    INFO: 'info',
    DEBUG: 'success'
  }
  return typeMap[level] || 'info'
}



// 跳转到日志列表
const goToLogs = () => {
  router.push('/logs')
}

// 跳转到错误日志
const goToErrorLogs = () => {
  router.push('/logs?level=ERROR')
}

// 跳转到最近日志
const goToRecentLogs = () => {
  const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000).toISOString()
  router.push(`/logs?startTime=${oneHourAgo}`)
}

// 查看日志详情
const viewLogDetail = (log) => {
  router.push(`/logs/${log.id}`)
}

// 刷新统计数据
const refreshStats = async () => {
  await Promise.all([loadStats(), loadRecentLogs()])
  Message.success('统计数据已刷新')
}

// 重置页面状态到初始状态
const resetPageState = () => {
  // 重置统计数据
  stats.total = 0
  stats.errorCount = 0
  stats.warnCount = 0
  stats.infoCount = 0
  stats.debugCount = 0
  stats.todayCount = 0
  stats.recentHourCount = 0

  // 清空最近日志
  recentLogs.value = []

  // 重置加载状态
  statsLoading.value = false
  logsLoading.value = false

  console.log('仪表板页面状态已重置')
}

// 加载统计数据
const loadStats = async () => {
  try {
    statsLoading.value = true
    const response = await logApi.getLogStats()

    if (response.code === 200 && response.data) {
      Object.assign(stats, response.data)
    } else {
      throw new Error(response.message || '获取统计数据失败')
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
    Message.error('获取统计数据失败，请检查后端服务是否正常运行')
  } finally {
    statsLoading.value = false
  }
}

// 加载最近日志
const loadRecentLogs = async () => {
  try {
    logsLoading.value = true

    // 构建查询参数
    const params = {
      page: 1,
      size: Math.min(10, configStore.getDefaultPageSize()), // 最近日志显示较少条数
      sort: 'timestamp',
      order: 'desc'
    }

    // 如果不是管理员，需要根据用户权限过滤应用
    if (!authStore.isAdmin && authStore.user) {
      // 获取用户有权限的应用ID列表
      const authorizedAppIds = authStore.user.authorizedAppIds || []

      // 如果用户没有任何应用权限，直接返回空结果
      if (authorizedAppIds.length === 0) {
        recentLogs.value = []
        return
      }

      // 将应用ID列表添加到查询参数中
      params.applicationIds = authorizedAppIds.join(',')
    }

    const response = await logApi.getLogs(params)

    if (response.code === 200 && response.data) {
      // 处理后端分页响应格式
      if (response.data.content) {
        recentLogs.value = response.data.content
      } else if (Array.isArray(response.data)) {
        recentLogs.value = response.data
      } else {
        recentLogs.value = []
      }
    } else {
      throw new Error(response.message || '获取最近日志失败')
    }
  } catch (error) {
    console.error('加载最近日志失败:', error)
    recentLogs.value = []
    Message.error('获取最近日志失败，请检查后端服务是否正常运行')
  } finally {
    logsLoading.value = false
  }
}

// 组件挂载时加载数据
onMounted(async () => {
  console.log('Dashboard组件挂载')
  isFirstLoad.value = true

  // 检查是否有权限错误提示
  if (route.query.error === 'admin_required') {
    Message.warning('您没有权限访问该页面，已重定向到仪表板')
    // 清除URL中的错误参数
    router.replace({ path: '/dashboard' })
  }

  // 加载数据
  await loadStats()
  await loadRecentLogs()

  isFirstLoad.value = false
})

// 组件激活时（从其他tab切换回来时）
onActivated(async () => {
  console.log('Dashboard组件被激活，isFirstLoad:', isFirstLoad.value)

  // 如果是首次加载（从导航栏重新进入），重置状态
  if (isFirstLoad.value) {
    console.log('检测到首次加载，重置页面状态')
    resetPageState()
    await loadStats()
    await loadRecentLogs()
    isFirstLoad.value = false
  } else {
    console.log('从其他tab切换回来，刷新数据')
    // 仪表板数据需要实时更新，所以总是重新加载
    await loadStats()
    await loadRecentLogs()
  }
})

// 组件停用时（切换到其他tab时）
onDeactivated(() => {
  console.log('Dashboard组件被停用')
})
</script>

<style scoped>
.dashboard {
  padding: var(--container-padding);
  background-color: var(--bg-color);
  min-height: calc(100vh - var(--header-height));
  max-width: var(--content-max-width);
  margin: 0 auto;
}

.dashboard-header {
  text-align: center;
  margin-bottom: 30px;
}

.dashboard-header h1 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
  color: #303133;
}

.dashboard-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  margin-right: 20px;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 8px;
  color: #303133;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.total-card {
  border-left: 4px solid #409eff;
}

.total-card .stat-icon {
  background: #409eff;
  color: white;
}

.error-card {
  border-left: 4px solid #f56c6c;
}

.error-card .stat-icon {
  background: #f56c6c;
  color: white;
}

.warn-card {
  border-left: 4px solid #e6a23c;
}

.warn-card .stat-icon {
  background: #e6a23c;
  color: white;
}

.today-card {
  border-left: 4px solid #67c23a;
}

.today-card .stat-icon {
  background: #67c23a;
  color: white;
}

.quick-actions-section {
  background: white;
  border-radius: 12px;
  padding: 32px;
  margin-bottom: 30px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.quick-actions-section h2 {
  margin: 0 0 32px 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 32px;
}

.action-card {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 16px;
  padding: 28px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 24px;
  min-height: 100px;
}

.action-card:hover {
  background: white;
  border-color: #409eff;
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(64, 158, 255, 0.15);
}

.action-icon {
  width: 56px;
  height: 56px;
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  font-size: 24px;
  color: white;
}

.action-icon.primary {
  background: linear-gradient(135deg, #409eff, #66b3ff);
}

.action-icon.danger {
  background: linear-gradient(135deg, #f56c6c, #ff8a8a);
}

.action-icon.info {
  background: linear-gradient(135deg, #909399, #b3b6bb);
}

.action-content {
  flex: 1;
}

.action-content h3 {
  margin: 0 0 8px 0;
  font-size: 17px;
  font-weight: 600;
  color: #303133;
  line-height: 1.3;
}

.action-content p {
  margin: 0;
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
}

.recent-logs-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #ebeef5;
  background-color: #fafafa;
}

.section-header h2 {
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.recent-logs-table {
  padding: 0;
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #fafafa;
  color: #606266;
  font-weight: bold;
  padding: 6px 0;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f0f0f0;
  padding: 6px 0;
}

/* 中等屏幕响应式设计 */
@media (max-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  }

  .action-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 24px;
  }
}

@media (max-width: 992px) {
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
  }

  .action-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
  }

  .action-card {
    padding: 24px;
  }
}

/* 小屏幕响应式设计 */
@media (max-width: 768px) {
  .dashboard {
    padding: var(--container-padding);
    min-height: calc(100vh - 120px); /* 调整高度以适应新的导航栏 */
  }

  .dashboard-header {
    margin-bottom: 20px;
  }

  .dashboard-header h1 {
    font-size: 22px;
    text-align: center;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    margin-bottom: 20px;
  }

  .stat-card {
    padding: 16px;
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }

  .stat-icon {
    width: 36px;
    height: 36px;
    font-size: 18px;
  }

  .stat-content {
    align-items: center;
  }

  .stat-number {
    font-size: 20px;
    margin-bottom: 4px;
  }

  .stat-label {
    font-size: 12px;
  }

  .quick-actions-section {
    padding: 20px 16px;
  }

  .quick-actions-section h2 {
    margin: 0 0 20px 0;
    font-size: 18px;
    text-align: center;
  }

  .action-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .action-card {
    padding: 18px;
    gap: 16px;
    min-height: 70px;
  }

  .action-icon {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }

  .action-content h3 {
    font-size: 15px;
    margin: 0 0 4px 0;
  }

  .action-content p {
    font-size: 12px;
    line-height: 1.4;
  }

  .recent-logs-section {
    margin-top: 20px;
  }

  .section-header {
    padding: 16px;
  }

  .section-header h2 {
    font-size: 16px;
  }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {
  .dashboard {
    padding: var(--container-padding);
  }

  .dashboard-header h1 {
    font-size: 20px;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }

  .stat-card {
    padding: 12px;
  }

  .stat-number {
    font-size: 18px;
  }

  .stat-label {
    font-size: 11px;
  }

  .quick-actions-section {
    padding: 16px 12px;
  }

  .action-card {
    padding: 16px;
    gap: 12px;
    min-height: 60px;
  }

  .action-icon {
    width: 36px;
    height: 36px;
    font-size: 16px;
  }

  .action-content h3 {
    font-size: 14px;
  }

  .action-content p {
    font-size: 11px;
  }
}
</style>
