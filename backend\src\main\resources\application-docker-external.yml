server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: logger-management-backend
  
  data:
    mongodb:
      # 使用环境变量配置外部MongoDB连接
      host: ${MONGODB_HOST:localhost}
      port: ${MONGODB_PORT:27017}
      database: ${MONGODB_DATABASE:logger_management}
      username: ${MONGODB_USERNAME:logger_user}
      password: ${MONGODB_PASSWORD:logger_password}
      authentication-database: ${MONGODB_AUTH_DATABASE:logger_management}
      # 连接池配置
      options:
        max-connection-pool-size: 20
        min-connection-pool-size: 5
        max-connection-idle-time: 30000
        max-connection-life-time: 60000
        connect-timeout: 10000
        socket-timeout: 30000
        server-selection-timeout: 30000
  
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
    default-property-inclusion: non_null

# 日志配置
logging:
  level:
    com.logmanagement: ${LOG_LEVEL_APP:INFO}
    org.springframework.data.mongodb: ${LOG_LEVEL_MONGO:WARN}
    org.springframework.web: WARN
    org.springframework.security: WARN
    root: ${LOG_LEVEL_ROOT:INFO}
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: /app/logs/application.log
    max-size: ${LOG_FILE_MAX_SIZE:100MB}
    max-history: ${LOG_FILE_MAX_HISTORY:30}

# 应用自定义配置
app:
  cors:
    allowed-origins: ${CORS_ALLOWED_ORIGINS:*}
    allowed-methods: ${CORS_ALLOWED_METHODS:GET,POST,PUT,DELETE,OPTIONS}
    allowed-headers: ${CORS_ALLOWED_HEADERS:*}
    allow-credentials: ${CORS_ALLOW_CREDENTIALS:true}

  jwt:
    secret: ${JWT_SECRET:LoggerManagementSystemSecretKeyForJWTTokenGeneration2024!@#$%^&*()_+{}|:<>?[]\\;'\",./"} 
    expiration: ${JWT_EXPIRATION:86400}

  pagination:
    default-page-size: 10
    max-page-size: 100

# Knife4j配置 - 外部MongoDB环境通常为生产环境，禁用API文档
knife4j:
  enable: ${ENABLE_API_DOCS:false}
  production: true

# Spring Boot Actuator配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
  health:
    mongo:
      enabled: true
