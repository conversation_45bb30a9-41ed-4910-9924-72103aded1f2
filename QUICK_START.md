# 通用日志管理系统 - 快速开始指南

## 概述

本指南提供最简单的方式将Windows开发的源码部署到Linux Docker环境。

## 前置要求

### Windows端
- Windows 10/11 或 Windows Server
- 网络连接到Linux服务器

### Linux端
- Ubuntu 18.04+, CentOS 7+ 或其他主流Linux发行版
- 4GB+ 内存，20GB+ 磁盘空间
- sudo权限

## 快速部署（3步完成）

### 步骤1: 准备Linux服务器

在Linux服务器上执行以下命令：

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y  # Ubuntu/Debian
# 或
sudo yum update -y  # CentOS/RHEL

# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 启动Docker服务
sudo systemctl start docker
sudo systemctl enable docker

# 将当前用户添加到docker组
sudo usermod -aG docker $USER

# 创建部署目录
mkdir -p ~/logger-management
cd ~/logger-management

# 重新登录以使docker组权限生效
logout
```

### 步骤2: 打包和传输源码（Windows端）

```cmd
# 进入项目的scripts目录
cd scripts

# 运行简化部署脚本
simple-deploy.bat
```

按照脚本提示：
1. 编辑生成的 `deploy-config.txt` 配置文件
2. 使用推荐的文件传输工具将源码包传输到Linux服务器

**推荐传输工具**:
- **WinSCP** (图形界面): https://winscp.net/
- **命令行SCP**: `scp logger-management-*.zip username@server-ip:/home/<USER>/`

### 步骤3: 部署应用（Linux端）

```bash
# SSH登录到Linux服务器
ssh username@server-ip

# 进入部署目录
cd ~/logger-management

# 解压源码包
unzip logger-management-*.zip

# 运行部署脚本
chmod +x scripts/deploy-on-linux.sh
./scripts/deploy-on-linux.sh
```

部署脚本会：
- 检查系统环境
- 选择部署模式（完整部署或外部MongoDB）
- 自动构建和启动所有服务
- 进行健康检查

## 访问应用

部署完成后，可以通过以下地址访问：

- **前端应用**: http://your-server-ip
- **后端API**: http://your-server-ip:8080/api
- **MongoDB管理** (如果启用): http://your-server-ip:8081

## 常用管理命令

```bash
# 查看服务状态
cd ~/logger-management/docker
docker-compose ps

# 查看服务日志
docker-compose logs -f backend
docker-compose logs -f frontend

# 重启服务
docker-compose restart

# 停止服务
docker-compose down

# 更新应用
docker-compose down
# 上传新的源码包并解压
unzip -o new-package.zip
docker-compose build
docker-compose up -d
```

## 故障排除

### 常见问题

1. **"不是内部命令"错误**
   - 确保在 `scripts` 目录下运行脚本
   - 使用 `simple-deploy.bat` 而不是 `deploy-remote.bat`

2. **文件传输失败**
   - 检查网络连接：`ping server-ip`
   - 使用WinSCP等图形工具
   - 确认SSH服务运行：`ssh username@server-ip`

3. **Docker权限错误**
   - 确保用户在docker组中：`groups $USER`
   - 重新登录：`logout` 然后重新SSH连接

4. **端口被占用**
   - 检查端口：`netstat -tulpn | grep :80`
   - 修改端口配置：编辑 `docker/.env` 文件

5. **内存不足**
   - 检查内存：`free -h`
   - 关闭不必要的服务
   - 增加服务器内存

### 获取详细帮助

- 查看 `scripts/TROUBLESHOOTING.md` 详细故障排除指南
- 查看 `CROSS_PLATFORM_DEPLOYMENT.md` 完整部署文档

## 安全建议

### 生产环境配置

1. **配置防火墙**
```bash
# Ubuntu/Debian
sudo ufw enable
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 8080/tcp

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=22/tcp
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=8080/tcp
sudo firewall-cmd --reload
```

2. **使用SSH密钥认证**
```cmd
# Windows端生成密钥
ssh-keygen -t rsa -b 4096

# 复制公钥到服务器
scp %USERPROFILE%\.ssh\id_rsa.pub username@server-ip:~/.ssh/authorized_keys
```

3. **修改默认密码**
   - 编辑 `docker/.env` 文件
   - 修改数据库密码和JWT密钥

4. **配置HTTPS**
   - 使用Nginx反向代理
   - 申请SSL证书

## 性能优化

### 系统级优化

```bash
# 增加文件描述符限制
echo "* soft nofile 65536" | sudo tee -a /etc/security/limits.conf
echo "* hard nofile 65536" | sudo tee -a /etc/security/limits.conf

# 优化内核参数
echo "vm.max_map_count=262144" | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

### 应用级优化

编辑 `docker/docker-compose.yml` 添加资源限制：

```yaml
services:
  backend:
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
```

## 备份和恢复

### 数据备份

```bash
# 备份MongoDB数据
docker-compose exec mongodb mongodump --out /backup
docker cp logger-management-mongodb:/backup ./backup-$(date +%Y%m%d)

# 备份配置文件
tar -czf config-backup-$(date +%Y%m%d).tar.gz docker/.env docker/docker-compose.yml
```

### 数据恢复

```bash
# 恢复MongoDB数据
docker cp ./backup-20241220 logger-management-mongodb:/backup
docker-compose exec mongodb mongorestore /backup
```

## 监控和维护

### 健康检查

```bash
# 检查服务健康状态
curl http://localhost:8080/api/actuator/health
curl http://localhost/

# 查看资源使用
docker stats

# 查看磁盘使用
df -h
```

### 日志管理

```bash
# 查看应用日志
docker-compose logs --tail=100 backend

# 清理Docker日志
docker system prune -f

# 设置日志轮转
sudo nano /etc/docker/daemon.json
```

---

**恭喜！** 您已成功部署通用日志管理系统。如有问题，请参考故障排除指南或联系技术支持。
