#!/bin/bash

# 通用日志管理系统 - Linux服务器环境设置脚本
# 作者: Logger Management System
# 版本: 1.0.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检测操作系统
detect_os() {
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        OS=$NAME
        VER=$VERSION_ID
    elif type lsb_release >/dev/null 2>&1; then
        OS=$(lsb_release -si)
        VER=$(lsb_release -sr)
    elif [ -f /etc/redhat-release ]; then
        OS="Red Hat Enterprise Linux"
        VER=$(cat /etc/redhat-release | sed s/.*release\ // | sed s/\ .*//)
    else
        OS=$(uname -s)
        VER=$(uname -r)
    fi
    
    log_info "检测到操作系统: $OS $VER"
}

# 更新系统包
update_system() {
    log_info "更新系统包..."
    
    if command -v apt-get &> /dev/null; then
        sudo apt-get update
        sudo apt-get upgrade -y
    elif command -v yum &> /dev/null; then
        sudo yum update -y
    elif command -v dnf &> /dev/null; then
        sudo dnf update -y
    else
        log_warning "未识别的包管理器，请手动更新系统"
    fi
    
    log_success "系统更新完成"
}

# 安装基础工具
install_basic_tools() {
    log_info "安装基础工具..."
    
    if command -v apt-get &> /dev/null; then
        sudo apt-get install -y curl wget unzip git nano vim
    elif command -v yum &> /dev/null; then
        sudo yum install -y curl wget unzip git nano vim
    elif command -v dnf &> /dev/null; then
        sudo dnf install -y curl wget unzip git nano vim
    fi
    
    log_success "基础工具安装完成"
}

# 安装Docker
install_docker() {
    if command -v docker &> /dev/null; then
        log_info "Docker已安装: $(docker --version)"
        return 0
    fi
    
    log_info "安装Docker..."
    
    if command -v apt-get &> /dev/null; then
        # Ubuntu/Debian
        curl -fsSL https://get.docker.com -o get-docker.sh
        sudo sh get-docker.sh
        rm get-docker.sh
    elif command -v yum &> /dev/null; then
        # CentOS/RHEL
        sudo yum install -y yum-utils
        sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
        sudo yum install -y docker-ce docker-ce-cli containerd.io
    elif command -v dnf &> /dev/null; then
        # Fedora
        sudo dnf -y install dnf-plugins-core
        sudo dnf config-manager --add-repo https://download.docker.com/linux/fedora/docker-ce.repo
        sudo dnf install -y docker-ce docker-ce-cli containerd.io
    fi
    
    # 启动Docker服务
    sudo systemctl start docker
    sudo systemctl enable docker
    
    # 将当前用户添加到docker组
    sudo usermod -aG docker $USER
    
    log_success "Docker安装完成"
    log_warning "请重新登录以使docker组权限生效"
}

# 安装Docker Compose
install_docker_compose() {
    if command -v docker-compose &> /dev/null; then
        log_info "Docker Compose已安装: $(docker-compose --version)"
        return 0
    fi
    
    log_info "安装Docker Compose..."
    
    # 获取最新版本号
    COMPOSE_VERSION=$(curl -s https://api.github.com/repos/docker/compose/releases/latest | grep 'tag_name' | cut -d\" -f4)
    
    # 下载并安装
    sudo curl -L "https://github.com/docker/compose/releases/download/${COMPOSE_VERSION}/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    sudo chmod +x /usr/local/bin/docker-compose
    
    # 创建符号链接
    sudo ln -sf /usr/local/bin/docker-compose /usr/bin/docker-compose
    
    log_success "Docker Compose安装完成: $(docker-compose --version)"
}

# 配置防火墙
configure_firewall() {
    log_info "配置防火墙..."
    
    if command -v ufw &> /dev/null; then
        # Ubuntu/Debian UFW
        sudo ufw allow 22/tcp
        sudo ufw allow 80/tcp
        sudo ufw allow 8080/tcp
        sudo ufw allow 8081/tcp
        log_info "UFW防火墙规则已配置"
    elif command -v firewall-cmd &> /dev/null; then
        # CentOS/RHEL firewalld
        sudo firewall-cmd --permanent --add-port=22/tcp
        sudo firewall-cmd --permanent --add-port=80/tcp
        sudo firewall-cmd --permanent --add-port=8080/tcp
        sudo firewall-cmd --permanent --add-port=8081/tcp
        sudo firewall-cmd --reload
        log_info "firewalld防火墙规则已配置"
    else
        log_warning "未检测到防火墙管理工具，请手动配置防火墙规则"
        log_info "需要开放的端口: 22(SSH), 80(前端), 8080(后端), 8081(MongoDB管理)"
    fi
}

# 创建部署目录
create_deploy_directory() {
    local deploy_path="/home/<USER>/logger-management"
    
    log_info "创建部署目录: $deploy_path"
    
    mkdir -p "$deploy_path"
    cd "$deploy_path"
    
    log_success "部署目录创建完成: $(pwd)"
}

# 优化系统设置
optimize_system() {
    log_info "优化系统设置..."
    
    # 增加文件描述符限制
    echo "* soft nofile 65536" | sudo tee -a /etc/security/limits.conf
    echo "* hard nofile 65536" | sudo tee -a /etc/security/limits.conf
    
    # 优化内核参数
    echo "vm.max_map_count=262144" | sudo tee -a /etc/sysctl.conf
    echo "net.core.somaxconn=65535" | sudo tee -a /etc/sysctl.conf
    
    # 应用设置
    sudo sysctl -p
    
    log_success "系统优化完成"
}

# 显示安装结果
show_installation_result() {
    log_success "Linux服务器环境设置完成！"
    echo ""
    echo "=========================================="
    echo "安装信息摘要"
    echo "=========================================="
    echo "操作系统: $OS $VER"
    echo "Docker版本: $(docker --version 2>/dev/null || echo '未安装')"
    echo "Docker Compose版本: $(docker-compose --version 2>/dev/null || echo '未安装')"
    echo "部署目录: /home/<USER>/logger-management"
    echo "当前用户: $USER"
    echo ""
    echo "开放端口:"
    echo "  22   - SSH"
    echo "  80   - 前端应用"
    echo "  8080 - 后端API"
    echo "  8081 - MongoDB管理界面"
    echo ""
    echo "下一步操作:"
    echo "1. 重新登录以使docker组权限生效"
    echo "2. 从Windows传输源码包到此服务器"
    echo "3. 解压源码包并运行部署脚本"
    echo "=========================================="
}

# 主函数
main() {
    echo "=========================================="
    echo "通用日志管理系统 - Linux服务器环境设置"
    echo "=========================================="
    
    detect_os
    
    log_info "开始设置Linux服务器环境..."
    
    update_system
    install_basic_tools
    install_docker
    install_docker_compose
    configure_firewall
    create_deploy_directory
    optimize_system
    
    show_installation_result
    
    log_info "如果这是首次安装Docker，请执行以下命令重新登录:"
    log_info "logout 或 exit"
}

# 检查是否以root用户运行
if [ "$EUID" -eq 0 ]; then
    log_error "请不要以root用户运行此脚本"
    log_info "使用普通用户运行: ./setup-linux-server.sh"
    exit 1
fi

# 执行主函数
main "$@"
