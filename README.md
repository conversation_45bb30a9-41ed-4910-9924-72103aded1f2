# 通用日志管理系统

一个基于 Vue3 + Element Plus + Java 8 + MongoDB 的现代化通用日志管理系统。

## 项目结构

```
LoggerManagement/
├── frontend/          # Vue3 + Element Plus 前端 (JavaScript)
├── backend/           # Java 8 + Spring Boot 后端
├── docker/            # Docker 配置文件
└── README.md
```

## 技术栈

### 前端
- Vue 3 (Composition API) - JavaScript 版本
- Element Plus UI 组件库
- Vite 构建工具
- Vue Router 路由管理
- Axios HTTP 客户端

### 后端
- Java 8
- Spring Boot 2.7.x
- Spring Data MongoDB
- Maven 构建工具

### 数据库
- MongoDB 5.0+

## 功能特性

- 📋 **日志列表展示** - 支持分页、排序、筛选
- 🔍 **智能搜索** - 按级别、来源、时间范围、关键词搜索
- 📖 **日志详情** - 完整的日志信息展示，支持复制和导出
- 🏷️ **日志级别分类** - ERROR、WARN、INFO、DEBUG 四个级别
- 📅 **时间范围查询** - 灵活的时间筛选功能
- 📊 **统计仪表板** - 实时日志统计和可视化图表
- 🎨 **现代化UI** - 响应式设计，支持移动端
- ⚡ **高性能** - 优化的查询和分页机制

## 界面预览

### 仪表板
- 实时统计卡片（总数、错误、警告、今日日志）
- 日志级别分布图表
- 快速操作按钮
- 最近日志列表

### 日志管理
- 高级搜索表单
- 响应式表格展示
- 批量操作功能
- 详情页面展示

## 快速开始

### 1. 启动 MongoDB
```bash
# 使用 Docker Compose（推荐）
cd docker
docker-compose up -d

# 或者直接运行 MongoDB
docker run -d -p 27017:27017 --name mongodb mongo:5.0
```

### 2. 启动后端服务
```bash
cd backend
mvn spring-boot:run
```

### 3. 启动前端服务
```bash
cd frontend
npm install
npm run dev
```

### 4. 访问应用
- 前端地址：http://localhost:5173
- 后端API：http://localhost:8080/api
- MongoDB管理：http://localhost:8081 (用户名/密码: admin/admin123)

## API 接口文档

### 日志管理接口
- `GET /api/logs` - 获取日志列表（支持分页和搜索）
- `GET /api/logs/{id}` - 获取日志详情
- `POST /api/logs/search` - 高级搜索日志
- `POST /api/logs` - 创建日志条目
- `DELETE /api/logs/{id}` - 删除日志条目
- `DELETE /api/logs/batch` - 批量删除日志

### 统计接口
- `GET /api/logs/stats` - 获取日志统计信息
- `GET /api/logs/sources` - 获取所有日志来源

### 管理接口
- `DELETE /api/logs/cleanup?days=30` - 清理过期日志

## 开发说明

### 环境要求
- Node.js 16+
- Java 8+
- Maven 3.6+
- MongoDB 5.0+

### 配置说明
1. **前端配置**：`frontend/vite.config.js` - 代理配置
2. **后端配置**：`backend/src/main/resources/application.yml` - 数据库连接等
3. **数据库配置**：`docker/docker-compose.yml` - MongoDB 容器配置

### 开发模式
- 前端支持热重载，修改代码后自动刷新
- 后端支持 Spring Boot DevTools，自动重启
- 数据库初始化时会自动创建测试数据

### 项目特点
- ✅ 使用 JavaScript 而非 TypeScript，降低学习成本
- ✅ 现代化的 UI 设计，用户体验优秀
- ✅ 完整的错误处理和用户反馈
- ✅ 响应式设计，支持移动端访问
- ✅ 代码结构清晰，易于维护和扩展

## 部署说明

### Docker 部署
```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 生产环境配置
1. 修改数据库连接配置
2. 配置生产环境的 CORS 设置
3. 启用 HTTPS
4. 配置日志轮转和清理策略

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License
