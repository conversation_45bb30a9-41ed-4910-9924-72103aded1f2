package com.logmanagement.backend.util;

import java.security.SecureRandom;
import java.util.Base64;

/**
 * 令牌生成工具类
 * 
 * <AUTHOR> Management System
 * @version 1.0.0
 */
public class TokenGenerator {

    private static final SecureRandom secureRandom = new SecureRandom();
    private static final Base64.Encoder base64Encoder = Base64.getUrlEncoder();

    /**
     * 生成应用令牌
     * 
     * @return 应用令牌
     */
    public static String generateAppToken() {
        byte[] randomBytes = new byte[32];
        secureRandom.nextBytes(randomBytes);
        return base64Encoder.encodeToString(randomBytes).replaceAll("=", "");
    }

    /**
     * 生成API密钥
     * 
     * @return API密钥
     */
    public static String generateApiKey() {
        byte[] randomBytes = new byte[24];
        secureRandom.nextBytes(randomBytes);
        return base64Encoder.encodeToString(randomBytes).replaceAll("=", "");
    }

    /**
     * 生成随机字符串
     * 
     * @param length 长度
     * @return 随机字符串
     */
    public static String generateRandomString(int length) {
        byte[] randomBytes = new byte[length];
        secureRandom.nextBytes(randomBytes);
        return base64Encoder.encodeToString(randomBytes)
                .replaceAll("[^a-zA-Z0-9]", "")
                .substring(0, Math.min(length, randomBytes.length));
    }
}
