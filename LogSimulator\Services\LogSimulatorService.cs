using LogSimulator.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace LogSimulator.Services;

/// <summary>
/// 日志模拟器服务
/// </summary>
public class LogSimulatorService
{
    private readonly LogGeneratorService _logGenerator;
    private readonly LogApiService _logApiService;
    private readonly IConfiguration _configuration;
    private readonly ILogger<LogSimulatorService> _logger;

    public LogSimulatorService(
        LogGeneratorService logGenerator,
        LogApiService logApiService,
        IConfiguration configuration,
        ILogger<LogSimulatorService> logger)
    {
        _logGenerator = logGenerator;
        _logApiService = logApiService;
        _configuration = configuration;
        _logger = logger;
    }

    /// <summary>
    /// 运行单次模拟
    /// </summary>
    public async Task<bool> RunSingleSimulationAsync()
    {
        try
        {
            _logger.LogInformation("开始单次日志模拟");

            // 生成单个日志条目
            var logEntry = _logGenerator.GenerateLogEntry();
            
            _logger.LogInformation("生成日志: {Level} - {Message}", logEntry.Level, logEntry.Message);

            // 发送日志
            var response = await _logApiService.SendLogAsync(logEntry);

            if (response.IsSuccess)
            {
                _logger.LogInformation("日志发送成功: {Message}", response.Message);
                return true;
            }
            else
            {
                _logger.LogError("日志发送失败: {Message} (状态码: {Code})",
                    response.Message, response.Code);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "单次模拟执行失败: {Message}", ex.Message);
            return false;
        }
    }

    /// <summary>
    /// 运行批量模拟
    /// </summary>
    public async Task<SimulationResult> RunBatchSimulationAsync(int? logCount = null, int? batchSize = null)
    {
        var result = new SimulationResult();
        
        try
        {
            var totalLogs = logCount ?? _configuration.GetValue<int>("Simulation:LogCount");
            var batchSizeValue = batchSize ?? _configuration.GetValue<int>("Simulation:BatchSize");
            
            _logger.LogInformation("开始批量日志模拟: 总数={TotalLogs}, 批次大小={BatchSize}", totalLogs, batchSizeValue);

            result.TotalLogs = totalLogs;
            result.StartTime = DateTime.UtcNow;

            var batches = (int)Math.Ceiling((double)totalLogs / batchSizeValue);
            
            for (int batchIndex = 0; batchIndex < batches; batchIndex++)
            {
                var currentBatchSize = Math.Min(batchSizeValue, totalLogs - (batchIndex * batchSizeValue));
                
                _logger.LogInformation("处理批次 {BatchIndex}/{TotalBatches}, 大小: {CurrentBatchSize}", 
                    batchIndex + 1, batches, currentBatchSize);

                // 生成日志条目
                var logEntries = _logGenerator.GenerateLogEntries(currentBatchSize);
                
                // 发送批量日志
                var response = await _logApiService.SendLogsBatchAsync(logEntries);

                if (response.IsSuccess)
                {
                    result.SuccessfulLogs += currentBatchSize;
                    _logger.LogInformation("批次 {BatchIndex} 发送成功: {Message}", batchIndex + 1, response.Message);
                }
                else
                {
                    result.FailedLogs += currentBatchSize;
                    _logger.LogError("批次 {BatchIndex} 发送失败: {Message} (状态码: {Code})",
                        batchIndex + 1, response.Message, response.Code);
                }

                // 批次间延迟
                var intervalSeconds = _configuration.GetValue<int>("Simulation:IntervalSeconds");
                if (intervalSeconds > 0 && batchIndex < batches - 1)
                {
                    _logger.LogDebug("等待 {IntervalSeconds} 秒后处理下一批次", intervalSeconds);
                    await Task.Delay(TimeSpan.FromSeconds(intervalSeconds));
                }
            }

            result.EndTime = DateTime.UtcNow;
            result.Duration = result.EndTime - result.StartTime;

            _logger.LogInformation("批量模拟完成: 成功={SuccessfulLogs}, 失败={FailedLogs}, 耗时={Duration}", 
                result.SuccessfulLogs, result.FailedLogs, result.Duration);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量模拟执行失败: {Message}", ex.Message);
            result.EndTime = DateTime.UtcNow;
            result.Duration = result.EndTime - result.StartTime;
            result.Error = ex.Message;
            return result;
        }
    }

    /// <summary>
    /// 运行连续模拟
    /// </summary>
    public async Task RunContinuousSimulationAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("开始连续日志模拟，按 Ctrl+C 停止");

            var intervalSeconds = _configuration.GetValue<int>("Simulation:IntervalSeconds");
            var batchSize = _configuration.GetValue<int>("Simulation:BatchSize");

            var totalSent = 0;
            var totalFailed = 0;
            var startTime = DateTime.UtcNow;

            while (!cancellationToken.IsCancellationRequested)
            {
                try
                {
                    // 生成日志条目
                    var logEntries = _logGenerator.GenerateLogEntries(batchSize);
                    
                    // 发送批量日志
                    var response = await _logApiService.SendLogsBatchAsync(logEntries);

                    if (response.IsSuccess)
                    {
                        totalSent += batchSize;
                        _logger.LogInformation("发送成功: {BatchSize} 条日志 (总计: {TotalSent}) - {Message}",
                            batchSize, totalSent, response.Message);
                    }
                    else
                    {
                        totalFailed += batchSize;
                        _logger.LogError("发送失败: {BatchSize} 条日志 (总计失败: {TotalFailed}) - {Message} (状态码: {Code})",
                            batchSize, totalFailed, response.Message, response.Code);
                    }

                    // 等待下一次发送
                    await Task.Delay(TimeSpan.FromSeconds(intervalSeconds), cancellationToken);
                }
                catch (OperationCanceledException)
                {
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "连续模拟过程中发生异常: {Message}", ex.Message);
                    await Task.Delay(TimeSpan.FromSeconds(5), cancellationToken); // 错误后等待5秒
                }
            }

            var duration = DateTime.UtcNow - startTime;
            _logger.LogInformation("连续模拟结束: 成功={TotalSent}, 失败={TotalFailed}, 运行时间={Duration}", 
                totalSent, totalFailed, duration);
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("连续模拟被用户取消");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "连续模拟执行失败: {Message}", ex.Message);
        }
    }

    /// <summary>
    /// 测试API连接
    /// </summary>
    public async Task<bool> TestApiConnectionAsync()
    {
        try
        {
            _logger.LogInformation("测试API连接...");
            
            var success = await _logApiService.TestConnectionAsync();
            
            if (success)
            {
                _logger.LogInformation("API连接测试成功");
                
                // 获取统计信息
                var stats = await _logApiService.GetApplicationStatsAsync();
                if (!string.IsNullOrEmpty(stats))
                {
                    _logger.LogInformation("应用程序统计信息: {Stats}", stats);
                }
            }
            else
            {
                _logger.LogError("API连接测试失败");
            }

            return success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "API连接测试异常: {Message}", ex.Message);
            return false;
        }
    }

    /// <summary>
    /// 显示配置信息
    /// </summary>
    public void ShowConfiguration()
    {
        _logger.LogInformation("=== 日志模拟器配置 ===");
        _logger.LogInformation("API地址: {ApiBaseUrl}", _configuration["LogManagement:ApiBaseUrl"]);
        _logger.LogInformation("应用程序ID: {AppId}", _configuration["LogManagement:AppId"]);
        _logger.LogInformation("API密钥: {ApiKey}", _configuration["LogManagement:ApiKey"]);
        _logger.LogInformation("日志数量: {LogCount}", _configuration["Simulation:LogCount"]);
        _logger.LogInformation("批次大小: {BatchSize}", _configuration["Simulation:BatchSize"]);
        _logger.LogInformation("间隔时间: {IntervalSeconds} 秒", _configuration["Simulation:IntervalSeconds"]);
        _logger.LogInformation("随机延迟: {EnableRandomDelay}", _configuration["Simulation:EnableRandomDelay"]);
        _logger.LogInformation("========================");
    }
}

/// <summary>
/// 模拟结果
/// </summary>
public class SimulationResult
{
    public int TotalLogs { get; set; }
    public int SuccessfulLogs { get; set; }
    public int FailedLogs { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public TimeSpan Duration { get; set; }
    public string? Error { get; set; }

    public double SuccessRate => TotalLogs > 0 ? (double)SuccessfulLogs / TotalLogs * 100 : 0;
}
