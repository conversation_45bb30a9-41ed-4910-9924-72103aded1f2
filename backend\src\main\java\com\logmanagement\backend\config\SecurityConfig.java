package com.logmanagement.backend.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfigurationSource;

/**
 * Spring Security配置类
 *
 * <AUTHOR> Management System
 * @version 1.0.0
 */
@Configuration
@EnableWebSecurity
public class SecurityConfig extends WebSecurityConfigurerAdapter {

    private final CorsConfigurationSource corsConfigurationSource;
    private final JwtAuthenticationFilter jwtAuthenticationFilter;

    public SecurityConfig(CorsConfigurationSource corsConfigurationSource,
                         JwtAuthenticationFilter jwtAuthenticationFilter) {
        this.corsConfigurationSource = corsConfigurationSource;
        this.jwtAuthenticationFilter = jwtAuthenticationFilter;
    }

    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http
            // 启用CORS并使用自定义配置
            .cors().configurationSource(corsConfigurationSource)
            .and()

            // 禁用CSRF（因为我们使用JWT）
            .csrf().disable()

            // 配置会话管理为无状态
            .sessionManagement()
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
            .and()

            // 配置授权规则
            .authorizeRequests()
                // 允许所有OPTIONS请求（CORS预检请求）
                .antMatchers("OPTIONS", "/**").permitAll()

                // 允许认证相关的端点
                .antMatchers("/auth/**").permitAll()

                // 允许外部应用调用的日志API（使用AppID+ApiKey认证）
                .antMatchers("/api/logs/**").permitAll()

                // 允许健康检查端点
                .antMatchers("/actuator/**").permitAll()
                .antMatchers("/system/health").permitAll()

                // 允许错误页面
                .antMatchers("/error").permitAll()

                // 允许Knife4j API文档相关端点
                .antMatchers("/doc.html").permitAll()
                .antMatchers("/webjars/**").permitAll()
                .antMatchers("/swagger-resources/**").permitAll()
                .antMatchers("/v2/api-docs/**").permitAll()
                .antMatchers("/favicon.ico").permitAll()

                // 允许系统配置接口（无敏感信息）
                .antMatchers("/system/config").permitAll()
                .antMatchers("/system/pagination-config").permitAll()
                .antMatchers("/config/**").permitAll()

                // 其他所有请求都需要认证
                .anyRequest().authenticated()
            .and()

            // 添加JWT认证过滤器
            .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class)

            // 配置X-Frame-Options，允许同源iframe嵌入
            .headers()
                .frameOptions().sameOrigin()
            .and()

            // 禁用默认的登录页面
            .formLogin().disable()

            // 禁用HTTP Basic认证
            .httpBasic().disable();
    }

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
}
