package com.logmanagement.backend.controller;

import com.logmanagement.backend.dto.ApiResponse;
import com.logmanagement.backend.dto.LoginRequest;
import com.logmanagement.backend.dto.LoginResponse;
import com.logmanagement.backend.entity.User;
import com.logmanagement.backend.entity.UserStatus;
import com.logmanagement.backend.repository.UserRepository;
import com.logmanagement.backend.util.JwtUtil;
import io.swagger.annotations.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 认证控制器
 *
 * 提供用户认证相关的API接口，包括：
 * - 用户登录认证
 * - 用户登出
 * - JWT令牌验证和刷新
 * - 获取当前用户信息
 *
 * 所有接口都返回统一的ApiResponse格式，包含状态码、消息和数据
 *
 * <AUTHOR> Management System
 * @version 1.0.0
 */
@Api(tags = "认证管理", description = "用户认证相关接口，包括登录、登出、令牌管理等功能")
@RestController
@RequestMapping("/auth")
public class AuthController {

    private static final Logger logger = LoggerFactory.getLogger(AuthController.class);

    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private UserRepository userRepository;

    /**
     * 用户登录
     *
     * 用户通过用户名和密码进行登录认证，成功后返回JWT令牌和用户信息
     *
     * @param request 登录请求，包含用户名和密码
     * @return 登录响应，包含JWT令牌、用户信息等
     */
    @ApiOperation(
        value = "用户登录",
        notes = "用户通过用户名和密码进行登录认证，成功后返回JWT令牌和用户基本信息。" +
                "令牌有效期为24小时，需要在后续请求的Authorization头中携带。"
    )
    @PostMapping("/login")
    public ResponseEntity<ApiResponse<LoginResponse>> login(
            @ApiParam(value = "登录请求信息，包含用户名和密码", required = true)
            @Valid @RequestBody LoginRequest request) {
        logger.info("用户登录请求: {}", request.getUsername());
        
        try {
            // 从数据库查找用户
            Optional<User> userOptional = userRepository.findByUsername(request.getUsername());
            if (!userOptional.isPresent()) {
                logger.warn("用户不存在: {}", request.getUsername());
                return ResponseEntity.ok(ApiResponse.error("用户名或密码错误"));
            }

            User user = userOptional.get();

            // 验证密码
            if (!passwordEncoder.matches(request.getPassword(), user.getPassword())) {
                logger.warn("密码错误: {}", request.getUsername());
                return ResponseEntity.ok(ApiResponse.error("用户名或密码错误"));
            }

            // 检查用户状态
            if (user.getStatus() != UserStatus.ACTIVE) {
                logger.warn("用户账户已被禁用: {}", request.getUsername());
                return ResponseEntity.ok(ApiResponse.error("用户账户已被禁用"));
            }

            // 更新最后登录时间
            user.setLastLoginTime(LocalDateTime.now());
            userRepository.save(user);

            // 生成JWT令牌
            String token = jwtUtil.generateToken(user.getId(), user.getUsername(), user.getRole().name());

            // 构建响应
            LoginResponse response = new LoginResponse(token, 86400, user);

            logger.info("用户登录成功: {}", user.getUsername());
            return ResponseEntity.ok(ApiResponse.success("登录成功", response));

        } catch (Exception e) {
            logger.error("登录处理失败", e);
            return ResponseEntity.ok(ApiResponse.error("登录失败，请稍后重试"));
        }
    }

    /**
     * 用户登出
     *
     * 用户登出系统，清除客户端的认证状态
     * 注意：由于使用JWT无状态认证，服务端不维护会话状态，
     * 实际的令牌失效需要客户端主动清除
     *
     * @return 登出结果响应
     */
    @ApiOperation(
        value = "用户登出",
        notes = "用户登出系统。由于使用JWT无状态认证，服务端不维护会话，" +
                "客户端需要主动清除本地存储的令牌。"
    )
    @PostMapping("/logout")
    public ResponseEntity<ApiResponse<String>> logout() {
        logger.info("用户登出");
        // 在实际项目中，这里可以将token加入黑名单
        return ResponseEntity.ok(ApiResponse.success("登出成功"));
    }

    /**
     * 获取当前用户信息
     *
     * 根据JWT令牌获取当前登录用户的详细信息，包括用户ID、用户名、
     * 真实姓名、邮箱、角色、状态等信息
     *
     * @param authHeader JWT认证令牌，格式：Bearer <token>
     * @return 当前用户的详细信息
     */
    @ApiOperation(
        value = "获取当前用户信息",
        notes = "根据JWT令牌获取当前登录用户的详细信息，包括用户基本信息、角色权限等。" +
                "需要在请求头中携带有效的JWT令牌。"
    )
    @GetMapping("/me")
    public ResponseEntity<ApiResponse<LoginResponse.UserInfo>> getCurrentUser(
            @ApiParam(value = "JWT认证令牌，格式：Bearer <token>", required = true)
            @RequestHeader(value = "Authorization", required = false) String authHeader) {
        
        try {
            if (authHeader == null || !authHeader.startsWith("Bearer ")) {
                return ResponseEntity.ok(ApiResponse.error("未提供有效的认证令牌"));
            }

            String token = authHeader.substring(7);
            if (!jwtUtil.validateToken(token)) {
                return ResponseEntity.ok(ApiResponse.error("认证令牌无效"));
            }

            String username = jwtUtil.getUsernameFromToken(token);
            Optional<User> userOptional = userRepository.findByUsername(username);

            if (!userOptional.isPresent()) {
                return ResponseEntity.ok(ApiResponse.error("用户不存在"));
            }

            User user = userOptional.get();

            LoginResponse.UserInfo userInfo = new LoginResponse.UserInfo(user);

            return ResponseEntity.ok(ApiResponse.success("获取用户信息成功", userInfo));

        } catch (Exception e) {
            logger.error("获取用户信息失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取用户信息失败"));
        }
    }

    /**
     * 验证token
     */
    @ApiOperation(value = "验证JWT令牌", notes = "验证JWT令牌的有效性")
    @GetMapping("/validate")
    public ResponseEntity<ApiResponse<Map<String, Boolean>>> validateToken(
            @ApiParam(value = "JWT认证令牌", required = true) @RequestHeader(value = "Authorization", required = false) String authHeader) {

        logger.info("Token验证请求, Authorization头: {}", authHeader != null ? "存在" : "不存在");

        try {
            Map<String, Boolean> result = new HashMap<>();

            if (authHeader == null || !authHeader.startsWith("Bearer ")) {
                logger.info("Token验证失败: 未提供有效的Authorization头");
                result.put("valid", false);
                return ResponseEntity.ok(ApiResponse.success("Token验证结果", result));
            }

            String token = authHeader.substring(7);
            logger.info("提取的Token: {}", token.length() > 50 ? token.substring(0, 50) + "..." : token);
            logger.info("Token格式检查 - 包含点号数量: {}", token.split("\\.").length - 1);

            boolean isValid = jwtUtil.validateToken(token);
            result.put("valid", isValid);

            logger.info("Token验证结果: {}", isValid);
            return ResponseEntity.ok(ApiResponse.success("Token验证结果", result));

        } catch (Exception e) {
            logger.error("Token验证失败", e);
            Map<String, Boolean> result = new HashMap<>();
            result.put("valid", false);
            return ResponseEntity.ok(ApiResponse.success("Token验证结果", result));
        }
    }

    /**
     * 刷新token
     */
    @ApiOperation(value = "刷新JWT令牌", notes = "使用当前有效的JWT令牌获取新的令牌")
    @PostMapping("/refresh")
    public ResponseEntity<ApiResponse<Map<String, String>>> refreshToken(
            @ApiParam(value = "JWT认证令牌", required = true) @RequestHeader(value = "Authorization", required = false) String authHeader) {
        
        try {
            if (authHeader == null || !authHeader.startsWith("Bearer ")) {
                return ResponseEntity.ok(ApiResponse.error("未提供有效的认证令牌"));
            }

            String token = authHeader.substring(7);
            if (!jwtUtil.validateToken(token)) {
                return ResponseEntity.ok(ApiResponse.error("认证令牌无效"));
            }

            String username = jwtUtil.getUsernameFromToken(token);
            String userId = jwtUtil.getUserIdFromToken(token);
            String role = jwtUtil.getRoleFromToken(token);

            // 生成新的token
            String newToken = jwtUtil.generateToken(userId, username, role);
            String newRefreshToken = jwtUtil.generateToken(userId, username, role);

            Map<String, String> result = new HashMap<>();
            result.put("token", newToken);
            result.put("refreshToken", newRefreshToken);

            return ResponseEntity.ok(ApiResponse.success("Token刷新成功", result));

        } catch (Exception e) {
            logger.error("Token刷新失败", e);
            return ResponseEntity.ok(ApiResponse.error("Token刷新失败"));
        }
    }
}
