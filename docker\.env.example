# 通用日志管理系统 Docker 环境变量配置文件
# 复制此文件为 .env 并根据需要修改配置

# ===========================================
# 端口配置
# ===========================================
FRONTEND_PORT=80
BACKEND_PORT=8080
MONGODB_PORT=27017
MONGO_EXPRESS_PORT=8081

# ===========================================
# MongoDB 配置
# ===========================================
MONGO_INITDB_ROOT_USERNAME=admin
MONGO_INITDB_ROOT_PASSWORD=password123
MONGO_INITDB_DATABASE=logger_management

# 应用数据库用户
MONGO_APP_USERNAME=logger_user
MONGO_APP_PASSWORD=logger_password

# MongoDB Express 配置
MONGO_EXPRESS_USERNAME=admin
MONGO_EXPRESS_PASSWORD=admin123

# ===========================================
# 应用配置
# ===========================================
# Spring Boot 配置文件
SPRING_PROFILES_ACTIVE=docker

# JWT 密钥（生产环境请修改）
JWT_SECRET=LoggerManagementSystemSecretKeyForJWTTokenGeneration2024!@#$%^&*()_+{}|:<>?[]\\;'\",./"

# JWT 过期时间（秒）
JWT_EXPIRATION=86400

# ===========================================
# 日志配置
# ===========================================
# 日志级别
LOG_LEVEL_ROOT=INFO
LOG_LEVEL_APP=INFO

# 日志文件配置
LOG_FILE_MAX_SIZE=100MB
LOG_FILE_MAX_HISTORY=30

# ===========================================
# 性能配置
# ===========================================
# JVM 内存配置
JAVA_OPTS=-Xms512m -Xmx1024m -Djava.security.egd=file:/dev/./urandom

# ===========================================
# 安全配置
# ===========================================
# CORS 配置
CORS_ALLOWED_ORIGINS=*
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=*
CORS_ALLOW_CREDENTIALS=true

# ===========================================
# 开发配置
# ===========================================
# 是否启用 API 文档（生产环境建议设为 false）
ENABLE_API_DOCS=false

# 是否启用开发工具
ENABLE_DEV_TOOLS=false
