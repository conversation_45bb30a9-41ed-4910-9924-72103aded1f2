package com.logmanagement.backend.controller;

import com.logmanagement.backend.config.PaginationConfig;
import com.logmanagement.backend.dto.ApiResponse;
import com.logmanagement.backend.dto.LogSearchRequest;
import com.logmanagement.backend.dto.LogStatsResponse;
import com.logmanagement.backend.entity.Application;
import com.logmanagement.backend.entity.ApplicationStatus;
import com.logmanagement.backend.entity.LogEntry;
import com.logmanagement.backend.entity.LogLevel;
import com.logmanagement.backend.entity.User;
import com.logmanagement.backend.repository.ApplicationRepository;
import com.logmanagement.backend.repository.UserRepository;
import com.logmanagement.backend.service.LogEntryService;
import com.logmanagement.backend.util.JwtUtil;
import io.swagger.annotations.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.time.LocalDateTime;
import java.util.*;
import org.springframework.data.domain.Page;

/**
 * 日志条目控制器
 *
 * 提供日志条目的完整管理功能，包括：
 * - 日志的增删改查操作
 * - 多条件搜索和分页查询
 * - 日志统计和分析
 * - 批量操作支持
 * - 日志清理和维护
 *
 * 权限控制：
 * - 管理员可以查看所有日志
 * - 普通用户只能查看有权限的应用的日志
 *
 * <AUTHOR> Management System
 * @version 1.0.0
 */
@Api(tags = "日志管理", description = "日志条目的增删改查、搜索过滤、统计分析等功能")
@RestController
@RequestMapping("/logs")
@Validated
public class LogEntryController {

    private static final Logger logger = LoggerFactory.getLogger(LogEntryController.class);

    @Autowired
    private LogEntryService logEntryService;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private ApplicationRepository applicationRepository;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private PaginationConfig paginationConfig;

    /**
     * 获取日志列表（分页）
     *
     * 支持多种过滤条件的分页查询，包括：
     * - 日志级别过滤（ERROR、WARN、INFO、DEBUG）
     * - 时间范围过滤
     * - 应用和环境过滤
     * - 关键词搜索（支持消息内容搜索）
     * - 线程名称过滤
     *
     * 权限控制：
     * - 管理员可以查看所有应用的日志
     * - 普通用户只能查看有权限的应用的日志
     *
     * @param page 页码，从1开始，默认为1
     * @param size 每页大小，默认为配置的默认值，最大不超过配置的最大值
     * @param sort 排序字段，默认为timestamp（时间戳）
     * @param order 排序方向，asc或desc，默认为desc
     * @param level 日志级别过滤，可选值：ERROR、WARN、INFO、DEBUG
     * @param source 日志来源过滤
     * @param applicationName 应用名称过滤
     * @param environment 环境过滤，如：dev、test、prod
     * @param startTime 开始时间，格式：yyyy-MM-dd HH:mm:ss
     * @param endTime 结束时间，格式：yyyy-MM-dd HH:mm:ss
     * @param keyword 关键词搜索，支持在消息内容中搜索
     * @param thread 线程名称过滤
     * @param applicationIds 应用ID列表，逗号分隔（管理员专用）
     * @param authHeader JWT认证令牌
     * @return 日志分页结果，包含日志列表和分页信息
     */
    @ApiOperation(
        value = "获取日志列表",
        notes = "分页查询日志条目，支持多种过滤条件。管理员可以查看所有日志，" +
                "普通用户只能查看有权限的应用的日志。支持按时间、级别、应用等条件过滤。"
    )
    @GetMapping
    public ResponseEntity<ApiResponse<Page<LogEntry>>> getLogs(
            @ApiParam(value = "页码，从1开始", defaultValue = "1", example = "1")
            @RequestParam(defaultValue = "1") Integer page,

            @ApiParam(value = "每页大小，不超过系统配置的最大值", example = "10")
            @RequestParam(required = false) Integer size,

            @ApiParam(value = "排序字段", defaultValue = "timestamp", allowableValues = "timestamp,level,source,applicationName")
            @RequestParam(defaultValue = "timestamp") String sort,

            @ApiParam(value = "排序方向", defaultValue = "desc", allowableValues = "asc,desc")
            @RequestParam(defaultValue = "desc") String order,

            @ApiParam(value = "日志级别过滤", allowableValues = "ERROR,WARN,INFO,DEBUG", example = "ERROR")
            @RequestParam(required = false) String level,

            @ApiParam(value = "日志来源过滤", example = "com.example.service.UserService")
            @RequestParam(required = false) String source,

            @ApiParam(value = "应用ID过滤", example = "app123")
            @RequestParam(required = false) String applicationId,

            @ApiParam(value = "环境过滤", allowableValues = "dev,test,prod", example = "prod")
            @RequestParam(required = false) String environment,

            @ApiParam(value = "开始时间，格式：yyyy-MM-dd HH:mm:ss", example = "2024-01-01 00:00:00")
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,

            @ApiParam(value = "结束时间，格式：yyyy-MM-dd HH:mm:ss", example = "2024-12-31 23:59:59")
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime,

            @ApiParam(value = "关键词搜索，在消息内容中搜索", example = "用户登录")
            @RequestParam(required = false) String keyword,

            @ApiParam(value = "线程名称过滤", example = "http-nio-8080-exec-1")
            @RequestParam(required = false) String thread,

            @ApiParam(value = "应用ID列表，逗号分隔（管理员专用）", example = "app1,app2,app3")
            @RequestParam(required = false) String applicationIds,

            @ApiParam(value = "JWT认证令牌，格式：Bearer <token>", required = true)
            @RequestHeader(value = "Authorization", required = false) String authHeader) {

        // 权限检查
        String currentUserId = getCurrentUserId(authHeader);
        if (currentUserId == null) {
            return ResponseEntity.ok(ApiResponse.error("未授权访问"));
        }

        // 构建搜索请求对象
        LogSearchRequest request = new LogSearchRequest();
        request.setPage(page);
        // 使用配置的默认页面大小，并验证大小限制
        int validatedSize = size != null ? paginationConfig.validatePageSize(size) : paginationConfig.getDefaultPageSize();
        request.setSize(validatedSize);
        request.setSort(sort);
        request.setOrder(order);

        if (level != null && !level.trim().isEmpty()) {
            try {
                request.setLevel(LogLevel.valueOf(level.toUpperCase()));
            } catch (IllegalArgumentException e) {
                logger.warn("无效的日志级别: {}", level);
            }
        }

        request.setSource(source);
        request.setApplicationId(applicationId);
        request.setEnvironment(environment);
        request.setStartTime(startTime);
        request.setEndTime(endTime);
        request.setKeyword(keyword);
        request.setThread(thread);

        // 处理权限过滤
        boolean isAdminUser = isAdmin(authHeader);
        if (!isAdminUser) {
            // 非管理员用户，需要根据权限过滤应用
            User user = userRepository.findById(currentUserId).orElse(null);
            if (user == null) {
                return ResponseEntity.ok(ApiResponse.error("用户不存在"));
            }

            List<String> authorizedAppIds = user.getAuthorizedAppIds();
            if (authorizedAppIds == null || authorizedAppIds.isEmpty()) {
                // 如果没有授权应用，返回空结果
                Page<LogEntry> emptyPage = Page.empty();
                return ResponseEntity.ok(ApiResponse.success("获取日志列表成功", emptyPage));
            }

            // 设置应用ID过滤条件
            request.setApplicationIds(authorizedAppIds);
        } else if (applicationIds != null && !applicationIds.trim().isEmpty()) {
            // 管理员用户，如果指定了applicationIds参数，则使用该参数
            List<String> appIdList = Arrays.asList(applicationIds.split(","));
            request.setApplicationIds(appIdList);
        }

        logger.info("获取日志列表: {}", request);
        try {
            Page<LogEntry> logs = logEntryService.findAll(request);
            return ResponseEntity.ok(ApiResponse.success("获取日志列表成功", logs));
        } catch (Exception e) {
            logger.error("获取日志列表失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取日志列表失败"));
        }
    }

    /**
     * 根据ID获取日志详情
     *
     * @param id 日志ID
     * @return 日志详情
     */
    @ApiOperation(value = "获取日志详情", notes = "根据日志ID获取单条日志的详细信息")
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<LogEntry>> getLogById(
            @ApiParam(value = "日志ID", required = true) @PathVariable String id,
            @ApiParam(value = "JWT认证令牌，格式：Bearer <token>", required = true)
            @RequestHeader(value = "Authorization", required = false) String authHeader) {
        logger.info("获取日志详情: {}", id);

        // 权限检查
        String currentUserId = getCurrentUserId(authHeader);
        if (currentUserId == null) {
            return ResponseEntity.ok(ApiResponse.error("未授权访问"));
        }

        try {
            Optional<LogEntry> logEntry = logEntryService.findById(id);
            if (logEntry.isPresent()) {
                LogEntry entry = logEntry.get();

                // 检查用户是否有权限查看该日志
                if (!hasPermissionToViewLog(currentUserId, entry)) {
                    return ResponseEntity.ok(ApiResponse.error("无权限访问该日志"));
                }

                return ResponseEntity.ok(ApiResponse.success(entry));
            } else {
                return ResponseEntity.ok(ApiResponse.notFound("日志不存在"));
            }
        } catch (Exception e) {
            logger.error("获取日志详情失败: {}", id, e);
            return ResponseEntity.ok(ApiResponse.error("获取日志详情失败"));
        }
    }

    /**
     * 搜索日志
     *
     * 高级搜索功能，支持复杂的搜索条件组合
     *
     * @param request 搜索请求，包含各种过滤条件
     * @return 日志分页结果
     */
    @ApiOperation(
        value = "搜索日志",
        notes = "高级搜索功能，支持复杂的搜索条件组合，包括时间范围、级别、关键词等多种条件。"
    )
    @PostMapping("/search")
    public ResponseEntity<ApiResponse<Page<LogEntry>>> searchLogs(
            @ApiParam(value = "搜索请求信息", required = true)
            @Valid @RequestBody LogSearchRequest request,
            @ApiParam(value = "JWT认证令牌，格式：Bearer <token>", required = true)
            @RequestHeader(value = "Authorization", required = false) String authHeader) {
        logger.info("搜索日志: {}", request);

        // 权限检查
        String currentUserId = getCurrentUserId(authHeader);
        if (currentUserId == null) {
            return ResponseEntity.ok(ApiResponse.error("未授权访问"));
        }

        try {
            // 检查是否是管理员
            boolean isAdminUser = isAdmin(authHeader);

            Page<LogEntry> logs;
            if (isAdminUser) {
                // 管理员可以搜索所有日志
                logs = logEntryService.search(request);
            } else {
                // 普通用户只能搜索有权限的应用日志
                User currentUser = userRepository.findById(currentUserId).orElse(null);
                if (currentUser == null) {
                    return ResponseEntity.ok(ApiResponse.error("用户不存在"));
                }

                List<String> authorizedAppIds = currentUser.getAuthorizedAppIds();
                if (authorizedAppIds == null || authorizedAppIds.isEmpty()) {
                    // 用户没有任何应用权限，返回空结果
                    logs = Page.empty();
                } else {
                    // 设置用户有权限的应用ID列表
                    request.setApplicationIds(authorizedAppIds);
                    logs = logEntryService.search(request);
                }
            }

            return ResponseEntity.ok(ApiResponse.success("搜索日志成功", logs));
        } catch (Exception e) {
            logger.error("搜索日志失败", e);
            return ResponseEntity.ok(ApiResponse.error("搜索日志失败"));
        }
    }

    /**
     * 获取日志统计信息
     *
     * @param authHeader 认证头
     * @return 日志统计
     */
    @ApiOperation(value = "获取日志统计", notes = "获取日志的统计信息，包括总数、各级别数量等")
    @GetMapping("/stats")
    public ResponseEntity<ApiResponse<LogStatsResponse>> getLogStats(
            @ApiParam(value = "JWT认证令牌", required = true) @RequestHeader(value = "Authorization", required = false) String authHeader) {
        logger.info("获取日志统计信息");
        try {
            String currentUserId = getCurrentUserId(authHeader);
            if (currentUserId == null) {
                return ResponseEntity.ok(ApiResponse.error("未授权访问"));
            }

            // 检查是否是管理员
            boolean isAdminUser = isAdmin(authHeader);

            LogStatsResponse stats;
            if (isAdminUser) {
                // 管理员可以查看所有日志统计
                stats = logEntryService.getStats();
            } else {
                // 非管理员只能查看有权限的应用的日志统计
                User user = userRepository.findById(currentUserId).orElse(null);
                if (user == null) {
                    return ResponseEntity.ok(ApiResponse.error("用户不存在"));
                }

                List<String> authorizedAppIds = user.getAuthorizedAppIds();
                if (authorizedAppIds == null || authorizedAppIds.isEmpty()) {
                    // 如果没有授权应用，返回空统计
                    stats = new LogStatsResponse();
                    stats.setTotal(0L);
                    stats.setErrorCount(0L);
                    stats.setWarnCount(0L);
                    stats.setInfoCount(0L);
                    stats.setDebugCount(0L);
                    stats.setTodayCount(0L);
                    stats.setRecentHourCount(0L);
                } else {
                    // 根据授权应用获取统计
                    stats = logEntryService.getStatsByApplicationIds(authorizedAppIds);
                }
            }

            return ResponseEntity.ok(ApiResponse.success(stats));
        } catch (Exception e) {
            logger.error("获取日志统计信息失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取日志统计信息失败"));
        }
    }

    /**
     * 获取指定应用的日志统计信息
     *
     * @param appId 应用ID
     * @param authHeader 认证头
     * @return 应用日志统计
     */
    @ApiOperation(value = "获取应用日志统计", notes = "获取指定应用的日志统计信息，包括总数、各级别数量等")
    @GetMapping("/stats/{appId}")
    public ResponseEntity<ApiResponse<LogStatsResponse>> getApplicationLogStats(
            @ApiParam(value = "应用ID", required = true) @PathVariable String appId,
            @ApiParam(value = "JWT认证令牌", required = true) @RequestHeader(value = "Authorization", required = false) String authHeader) {
        logger.info("获取应用日志统计信息: {}", appId);
        try {
            String currentUserId = getCurrentUserId(authHeader);
            if (currentUserId == null) {
                return ResponseEntity.ok(ApiResponse.error("未授权访问"));
            }

            // 检查应用是否存在
            Optional<Application> applicationOpt = applicationRepository.findById(appId);
            if (!applicationOpt.isPresent()) {
                return ResponseEntity.ok(ApiResponse.error("应用不存在"));
            }

            Application application = applicationOpt.get();

            // 检查权限
            if (!hasPermission(currentUserId, application)) {
                return ResponseEntity.ok(ApiResponse.error("无权限访问该应用"));
            }

            // 获取该应用的日志统计
            LogStatsResponse stats = logEntryService.getStatsByApplicationIds(Arrays.asList(appId));

            return ResponseEntity.ok(ApiResponse.success("获取应用日志统计成功", stats));
        } catch (Exception e) {
            logger.error("获取应用日志统计信息失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取应用日志统计信息失败"));
        }
    }

    /**
     * 获取所有日志来源
     *
     * 获取系统中所有日志的来源列表，用于过滤条件选择
     *
     * @return 日志来源列表
     */
    @ApiOperation(
        value = "获取日志来源列表",
        notes = "获取系统中所有日志的来源列表，通常是类名或模块名，用于日志过滤。"
    )
    @GetMapping("/sources")
    public ResponseEntity<ApiResponse<List<String>>> getLogSources(
            @ApiParam(value = "JWT认证令牌，格式：Bearer <token>", required = true)
            @RequestHeader(value = "Authorization", required = false) String authHeader) {
        logger.info("获取所有日志来源");

        // 权限检查
        String currentUserId = getCurrentUserId(authHeader);
        if (currentUserId == null) {
            return ResponseEntity.ok(ApiResponse.error("未授权访问"));
        }

        try {
            // 检查是否是管理员
            boolean isAdminUser = isAdmin(authHeader);

            List<String> sources;
            if (isAdminUser) {
                // 管理员可以获取所有日志来源
                sources = logEntryService.getAllSources();
            } else {
                // 普通用户只能获取有权限的应用的日志来源
                User currentUser = userRepository.findById(currentUserId).orElse(null);
                if (currentUser == null) {
                    return ResponseEntity.ok(ApiResponse.error("用户不存在"));
                }

                List<String> authorizedAppIds = currentUser.getAuthorizedAppIds();
                if (authorizedAppIds == null || authorizedAppIds.isEmpty()) {
                    sources = new ArrayList<>();
                } else {
                    // 暂时使用所有来源，后续可以优化为按应用ID过滤
                    sources = logEntryService.getAllSources();
                }
            }

            return ResponseEntity.ok(ApiResponse.success("获取日志来源成功", sources));
        } catch (Exception e) {
            logger.error("获取日志来源失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取日志来源失败"));
        }
    }

    /**
     * 创建日志条目
     *
     * @param logEntry 日志条目
     * @param appToken 应用Token（用于验证应用身份）
     * @return 创建结果
     */
    @ApiOperation(value = "创建日志条目", notes = "创建单条日志记录，需要提供有效的应用Token")
    @PostMapping
    public ResponseEntity<ApiResponse<LogEntry>> createLog(
            @ApiParam(value = "日志条目信息", required = true) @Valid @RequestBody LogEntry logEntry,
            @ApiParam(value = "应用Token，用于验证应用身份", required = true)
            @RequestHeader(value = "X-App-Token", required = false) String appToken) {
        logger.info("创建日志条目: {}", logEntry);

        // 验证应用Token
        if (!isValidAppToken(appToken, logEntry.getApplicationId())) {
            return ResponseEntity.ok(ApiResponse.error("无效的应用Token"));
        }

        try {
            // 设置创建时间
            logEntry.setCreatedAt(LocalDateTime.now());
            logEntry.setUpdatedAt(LocalDateTime.now());

            LogEntry savedLog = logEntryService.save(logEntry);
            return ResponseEntity.ok(ApiResponse.success("日志创建成功", savedLog));
        } catch (Exception e) {
            logger.error("创建日志条目失败", e);
            return ResponseEntity.ok(ApiResponse.error("创建日志条目失败"));
        }
    }

    /**
     * 批量创建日志条目
     *
     * 一次性创建多条日志记录，提高批量导入效率
     *
     * @param logEntries 日志条目列表
     * @return 创建结果，包含所有成功创建的日志
     */
    @ApiOperation(
        value = "批量创建日志条目",
        notes = "一次性创建多条日志记录，适用于批量导入场景。" +
                "所有日志条目都会进行验证，如有错误会返回详细信息。"
    )
    @PostMapping("/batch")
    public ResponseEntity<ApiResponse<List<LogEntry>>> createLogs(
            @ApiParam(value = "日志条目列表", required = true)
            @Valid @RequestBody List<LogEntry> logEntries,
            @ApiParam(value = "应用Token，用于验证应用身份", required = true)
            @RequestHeader(value = "X-App-Token", required = false) String appToken) {
        logger.info("批量创建日志条目，数量: {}", logEntries.size());

        // 验证应用Token（检查第一个日志条目的应用ID）
        if (logEntries.isEmpty()) {
            return ResponseEntity.ok(ApiResponse.error("日志条目列表不能为空"));
        }

        String applicationId = logEntries.get(0).getApplicationId();
        if (!isValidAppToken(appToken, applicationId)) {
            return ResponseEntity.ok(ApiResponse.error("无效的应用Token"));
        }

        // 验证所有日志条目都属于同一个应用
        boolean allSameApp = logEntries.stream()
                .allMatch(entry -> applicationId.equals(entry.getApplicationId()));
        if (!allSameApp) {
            return ResponseEntity.ok(ApiResponse.error("批量创建的日志条目必须属于同一个应用"));
        }

        try {
            // 设置创建时间
            LocalDateTime now = LocalDateTime.now();
            logEntries.forEach(entry -> {
                entry.setCreatedAt(now);
                entry.setUpdatedAt(now);
            });

            List<LogEntry> savedLogs = logEntryService.saveAll(logEntries);
            return ResponseEntity.ok(ApiResponse.success("批量创建日志成功", savedLogs));
        } catch (Exception e) {
            logger.error("批量创建日志条目失败", e);
            return ResponseEntity.ok(ApiResponse.error("批量创建日志条目失败"));
        }
    }

    /**
     * 删除日志条目
     *
     * 根据日志ID删除单条日志记录
     *
     * @param id 日志ID
     * @return 删除结果
     */
    @ApiOperation(
        value = "删除日志条目",
        notes = "根据日志ID删除单条日志记录。删除操作不可逆，请谨慎操作。只有管理员可以执行此操作。"
    )
    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<Void>> deleteLog(
            @ApiParam(value = "日志ID", required = true, example = "log123456")
            @PathVariable String id,
            @ApiParam(value = "JWT认证令牌，格式：Bearer <token>", required = true)
            @RequestHeader(value = "Authorization", required = false) String authHeader) {
        logger.info("删除日志条目: {}", id);

        // 检查ID是否为空
        if (id == null || id.trim().isEmpty()) {
            logger.warn("尝试删除空ID的日志");
            return ResponseEntity.ok(new ApiResponse<>(400, "日志ID不能为空"));
        }

        // 权限检查 - 只有管理员可以删除日志
        if (!isAdmin(authHeader)) {
            return ResponseEntity.ok(new ApiResponse<>(403, "无权限操作，只有管理员可以删除日志"));
        }

        try {
            boolean deleted = logEntryService.deleteById(id);
            if (deleted) {
                ApiResponse<Void> response = new ApiResponse<>(200, "日志删除成功");
                return ResponseEntity.ok(response);
            } else {
                ApiResponse<Void> response = new ApiResponse<>(404, "日志不存在");
                return ResponseEntity.ok(response);
            }
        } catch (Exception e) {
            logger.error("删除日志条目失败: {}", id, e);
            ApiResponse<Void> response = new ApiResponse<>(500, "删除日志条目失败");
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 批量删除日志条目
     *
     * 根据日志ID列表批量删除多条日志记录
     *
     * @param request 包含ID列表的请求，格式：{"ids": ["id1", "id2", ...]}
     * @return 删除结果，包含实际删除的数量
     */
    @ApiOperation(
        value = "批量删除日志条目",
        notes = "根据日志ID列表批量删除多条日志记录。" +
                "请求体格式：{\"ids\": [\"id1\", \"id2\", ...]}。" +
                "删除操作不可逆，请谨慎操作。只有管理员可以执行此操作。"
    )
    @DeleteMapping("/batch")
    public ResponseEntity<ApiResponse<Long>> deleteLogs(
            @ApiParam(value = "包含ID列表的请求", required = true,
                     example = "{\"ids\": [\"log123\", \"log456\"]}")
            @RequestBody Map<String, List<String>> request,
            @ApiParam(value = "JWT认证令牌，格式：Bearer <token>", required = true)
            @RequestHeader(value = "Authorization", required = false) String authHeader) {

        // 权限检查 - 只有管理员可以批量删除日志
        if (!isAdmin(authHeader)) {
            return ResponseEntity.ok(ApiResponse.error("无权限操作，只有管理员可以删除日志"));
        }

        List<String> ids = request.get("ids");
        if (ids == null || ids.isEmpty()) {
            return ResponseEntity.ok(ApiResponse.error("ID列表不能为空"));
        }

        logger.info("批量删除日志条目，ID数量: {}", ids.size());
        try {
            long deletedCount = logEntryService.deleteByIds(ids);
            return ResponseEntity.ok(ApiResponse.success("批量删除成功，删除数量: " + deletedCount, deletedCount));
        } catch (Exception e) {
            logger.error("批量删除日志条目失败", e);
            return ResponseEntity.ok(ApiResponse.error("批量删除日志条目失败"));
        }
    }

    /**
     * 清理过期日志
     *
     * 删除指定天数之前的所有日志记录，用于定期清理历史数据
     *
     * @param days 保留天数，默认30天，即删除30天前的所有日志
     * @return 清理结果，包含实际删除的数量
     */
    @ApiOperation(
        value = "清理过期日志",
        notes = "删除指定天数之前的所有日志记录，用于定期清理历史数据。" +
                "默认保留30天的日志，可以通过days参数自定义保留天数。" +
                "清理操作不可逆，请谨慎操作。只有管理员可以执行此操作。"
    )
    @DeleteMapping("/cleanup")
    public ResponseEntity<ApiResponse<Long>> cleanupOldLogs(
            @ApiParam(value = "保留天数", defaultValue = "30", example = "30")
            @RequestParam(defaultValue = "30") int days,
            @ApiParam(value = "JWT认证令牌，格式：Bearer <token>", required = true)
            @RequestHeader(value = "Authorization", required = false) String authHeader) {

        // 权限检查 - 只有管理员可以清理日志
        if (!isAdmin(authHeader)) {
            return ResponseEntity.ok(ApiResponse.error("无权限操作，只有管理员可以清理日志"));
        }

        if (days < 1) {
            return ResponseEntity.ok(ApiResponse.error("保留天数必须大于0"));
        }

        logger.info("清理{}天前的日志", days);
        try {
            long deletedCount = logEntryService.cleanupOldLogs(days);
            return ResponseEntity.ok(ApiResponse.success("清理完成，删除数量: " + deletedCount, deletedCount));
        } catch (Exception e) {
            logger.error("清理过期日志失败", e);
            return ResponseEntity.ok(ApiResponse.error("清理过期日志失败"));
        }
    }

    /**
     * 修复空ID的日志
     *
     * @param authHeader JWT认证令牌
     * @return 修复结果
     */
    @ApiOperation(value = "修复空ID日志", notes = "修复数据库中ID为空字符串的日志记录，只有管理员可以执行此操作")
    @PostMapping("/fix-empty-ids")
    public ResponseEntity<ApiResponse<Map<String, Object>>> fixEmptyIdLogs(
            @ApiParam(value = "JWT认证令牌，格式：Bearer <token>", required = true)
            @RequestHeader(value = "Authorization", required = false) String authHeader) {
        logger.info("手动修复空ID日志");

        // 权限检查 - 只有管理员可以执行修复操作
        if (!isAdmin(authHeader)) {
            return ResponseEntity.ok(new ApiResponse<>(403, "无权限操作，只有管理员可以执行修复操作"));
        }

        try {
            // 查找所有ID为空字符串的日志
            Query query = new Query(Criteria.where("_id").is(""));
            List<LogEntry> emptyIdLogs = mongoTemplate.find(query, LogEntry.class);

            if (emptyIdLogs.isEmpty()) {
                Map<String, Object> result = new HashMap<>();
                result.put("fixedCount", 0);
                result.put("message", "没有发现空ID的日志数据");
                return ResponseEntity.ok(new ApiResponse<>(200, "修复完成", result));
            }

            logger.info("发现 {} 条空ID的日志数据，开始修复...", emptyIdLogs.size());

            // 删除空ID的日志
            mongoTemplate.remove(query, LogEntry.class);

            // 重新保存这些日志，让MongoDB自动生成新的ID
            for (LogEntry log : emptyIdLogs) {
                log.setId(null); // 确保ID为null，让MongoDB自动生成
                mongoTemplate.save(log);
            }

            Map<String, Object> result = new HashMap<>();
            result.put("fixedCount", emptyIdLogs.size());
            result.put("message", "空ID日志修复完成");

            logger.info("空ID日志修复完成，共修复 {} 条日志", emptyIdLogs.size());
            return ResponseEntity.ok(new ApiResponse<>(200, "修复完成", result));

        } catch (Exception e) {
            logger.error("修复空ID日志失败", e);
            return ResponseEntity.ok(new ApiResponse<>(500, "修复空ID日志失败: " + e.getMessage()));
        }
    }

    /**
     * 获取当前用户ID
     */
    private String getCurrentUserId(String authHeader) {
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            return null;
        }

        try {
            String token = authHeader.substring(7);
            return jwtUtil.getUserIdFromToken(token);
        } catch (Exception e) {
            logger.error("获取用户ID失败", e);
            return null;
        }
    }

    /**
     * 检查是否是管理员
     */
    private boolean isAdmin(String authHeader) {
        String currentUserId = getCurrentUserId(authHeader);
        if (currentUserId == null) {
            return false;
        }

        try {
            User user = userRepository.findById(currentUserId).orElse(null);
            return user != null && "SUPER_ADMIN".equals(user.getRole().name());
        } catch (Exception e) {
            logger.error("检查管理员权限失败", e);
            return false;
        }
    }

    /**
     * 检查用户是否有权限查看指定日志
     */
    private boolean hasPermissionToViewLog(String userId, LogEntry logEntry) {
        try {
            User user = userRepository.findById(userId).orElse(null);
            if (user == null) {
                return false;
            }

            // 超级管理员有所有权限
            if ("SUPER_ADMIN".equals(user.getRole().name())) {
                return true;
            }

            // 检查用户是否有权限访问该应用的日志
            List<String> authorizedAppIds = user.getAuthorizedAppIds();
            if (authorizedAppIds == null || authorizedAppIds.isEmpty()) {
                return false;
            }

            return authorizedAppIds.contains(logEntry.getApplicationId());
        } catch (Exception e) {
            logger.error("检查日志访问权限失败", e);
            return false;
        }
    }

    /**
     * 验证应用Token是否有效
     */
    private boolean isValidAppToken(String appToken, String applicationId) {
        if (appToken == null || appToken.isEmpty() || applicationId == null || applicationId.isEmpty()) {
            return false;
        }

        try {
            // 从数据库查找应用
            Optional<Application> applicationOpt = applicationRepository.findById(applicationId);
            if (!applicationOpt.isPresent()) {
                logger.warn("应用不存在: {}", applicationId);
                return false;
            }

            Application application = applicationOpt.get();

            // 检查应用状态
            if (application.getStatus() != ApplicationStatus.ACTIVE) {
                logger.warn("应用状态不是激活状态: {}", applicationId);
                return false;
            }

            // 验证Token
            boolean isValid = appToken.equals(application.getToken());
            if (!isValid) {
                logger.warn("应用Token验证失败: {}", applicationId);
            }

            return isValid;
        } catch (Exception e) {
            logger.error("验证应用Token失败", e);
            return false;
        }
    }

    /**
     * 检查用户是否有权限访问应用
     */
    private boolean hasPermission(String userId, Application application) {
        try {
            User user = userRepository.findById(userId).orElse(null);
            if (user == null) {
                return false;
            }

            // 超级管理员有所有权限
            if ("SUPER_ADMIN".equals(user.getRole().name())) {
                return true;
            }

            // 应用创建者有权限
            if (application.getCreatorId().equals(userId)) {
                return true;
            }

            // 检查用户是否被授权访问该应用
            if (user.getAuthorizedAppIds() != null &&
                user.getAuthorizedAppIds().contains(application.getId())) {
                return true;
            }

            return false;
        } catch (Exception e) {
            logger.error("检查权限失败", e);
            return false;
        }
    }
}
