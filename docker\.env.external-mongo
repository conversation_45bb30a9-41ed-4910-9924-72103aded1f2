# 通用日志管理系统 - 外部MongoDB配置
# 当您已有MongoDB服务器时使用此配置

# ===========================================
# 应用端口配置
# ===========================================
FRONTEND_PORT=80
BACKEND_PORT=8080

# ===========================================
# 外部MongoDB配置
# ===========================================
# MongoDB服务器地址（必填）
MONGODB_HOST=your-mongodb-host

# MongoDB端口
MONGODB_PORT=27017

# 数据库名称
MONGODB_DATABASE=logger_management

# 应用数据库用户凭据（必填）
MONGODB_USERNAME=logger_user
MONGODB_PASSWORD=logger_password

# 认证数据库（通常与应用数据库相同）
MONGODB_AUTH_DATABASE=logger_management

# MongoDB管理员凭据（仅在启用mongo-express时需要）
MONGODB_ADMIN_USERNAME=admin
MONGODB_ADMIN_PASSWORD=your-admin-password

# ===========================================
# MongoDB Express配置（可选）
# ===========================================
# 如需启用MongoDB管理界面，请取消注释docker-compose文件中的mongo-express部分
MONGO_EXPRESS_PORT=8081
MONGO_EXPRESS_USERNAME=admin
MONGO_EXPRESS_PASSWORD=admin123

# ===========================================
# 应用安全配置
# ===========================================
# JWT密钥（生产环境请使用强密钥）
JWT_SECRET=YourSecureJWTSecretKeyHere2024!@#$%^&*()

# JWT过期时间（秒）
JWT_EXPIRATION=86400

# ===========================================
# CORS配置
# ===========================================
# 允许的来源（生产环境请指定具体域名）
CORS_ALLOWED_ORIGINS=http://localhost,http://your-domain.com

# 允许的HTTP方法
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS

# 允许的请求头
CORS_ALLOWED_HEADERS=Content-Type,Authorization,X-App-ID,X-Api-Key

# 是否允许携带凭据
CORS_ALLOW_CREDENTIALS=true

# ===========================================
# 日志配置
# ===========================================
# 应用日志级别
LOG_LEVEL_ROOT=INFO
LOG_LEVEL_APP=INFO
LOG_LEVEL_MONGO=WARN

# 日志文件配置
LOG_FILE_MAX_SIZE=100MB
LOG_FILE_MAX_HISTORY=30

# ===========================================
# 功能开关
# ===========================================
# 是否启用API文档（生产环境建议关闭）
ENABLE_API_DOCS=false
