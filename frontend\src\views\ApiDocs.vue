<script setup>
import { ref, onMounted } from 'vue'
import { Files, Link, Refresh } from '@element-plus/icons-vue'

const loading = ref(true)
const iframeRef = ref(null)

// API文档URL
const apiDocsUrl = ref('')

onMounted(() => {
  // 构建API文档URL
  // 获取当前页面的基础URL，然后构建API文档路径
  const currentOrigin = window.location.origin
  const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || '/api'

  // 如果是开发环境，使用代理路径
  if (import.meta.env.DEV) {
    apiDocsUrl.value = `${apiBaseUrl}/doc.html`
  } else {
    // 生产环境，需要根据实际部署情况调整
    apiDocsUrl.value = `${currentOrigin}${apiBaseUrl}/doc.html`
  }

  // 监听iframe加载完成
  if (iframeRef.value) {
    iframeRef.value.onload = () => {
      loading.value = false
    }

    // 设置错误处理
    iframeRef.value.onerror = () => {
      loading.value = false
      console.error('API文档加载失败')
    }
  }
})

// 在新窗口打开API文档
const openInNewWindow = () => {
  window.open(apiDocsUrl.value, '_blank')
}

// 刷新API文档
const refreshDocs = () => {
  loading.value = true
  if (iframeRef.value) {
    iframeRef.value.src = iframeRef.value.src
  }
}
</script>

<template>
  <div class="content-container">
    <div class="api-docs-container">
      <!-- 页面标题和操作按钮 -->
      <div class="page-header">
        <div class="header-left">
          <h1 class="page-title">
            <el-icon><Files /></el-icon>
            API 接口文档
          </h1>
          <p class="page-description">
            查看系统所有API接口的详细文档，包括请求参数、响应格式等信息
          </p>
        </div>
        <div class="header-actions">
          <el-button
            type="primary"
            @click="openInNewWindow"
            :icon="Link"
          >
            新窗口打开
          </el-button>
          <el-button 
            @click="refreshDocs"
            :icon="Refresh"
            :loading="loading"
          >
            刷新文档
          </el-button>
        </div>
      </div>

      <!-- API文档内容区域 -->
      <div class="docs-content">
        <div v-if="loading" class="loading-container">
          <el-skeleton animated>
            <template #template>
              <el-skeleton-item variant="h1" style="width: 40%" />
              <el-skeleton-item variant="text" style="width: 60%; margin-top: 20px;" />
              <el-skeleton-item variant="text" style="width: 80%; margin-top: 10px;" />
              <el-skeleton-item variant="rect" style="width: 100%; height: 400px; margin-top: 20px;" />
            </template>
          </el-skeleton>
        </div>
        
        <iframe
          ref="iframeRef"
          :src="apiDocsUrl"
          class="docs-iframe"
          :class="{ 'iframe-hidden': loading }"
          frameborder="0"
          scrolling="auto"
        ></iframe>
      </div>
    </div>
  </div>
</template>

<style scoped>
.api-docs-container {
  background: white;
  border-radius: var(--border-radius-large);
  box-shadow: var(--box-shadow-light);
  overflow: hidden;
  height: calc(100vh - var(--header-height) - 40px);
  display: flex;
  flex-direction: column;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: var(--spacing-xxl);
  border-bottom: 1px solid var(--border-lighter);
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-title .el-icon {
  color: var(--primary-color);
}

.page-description {
  color: var(--text-regular);
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.docs-content {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.loading-container {
  padding: var(--spacing-xxl);
  height: 100%;
}

.docs-iframe {
  width: 100%;
  height: 100%;
  border: none;
  transition: opacity 0.3s ease;
}

.iframe-hidden {
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    padding: var(--spacing-xl);
  }
  
  .header-actions {
    width: 100%;
    justify-content: flex-start;
  }
  
  .page-title {
    font-size: 20px;
  }
  
  .api-docs-container {
    height: calc(100vh - var(--header-height) - 30px);
  }
}

@media (max-width: 480px) {
  .header-actions {
    flex-direction: column;
    width: 100%;
  }
  
  .header-actions .el-button {
    width: 100%;
  }
}
</style>
