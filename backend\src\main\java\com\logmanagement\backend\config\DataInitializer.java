package com.logmanagement.backend.config;

import com.logmanagement.backend.entity.*;
import com.logmanagement.backend.service.LogEntryService;
import com.logmanagement.backend.repository.UserRepository;
import com.logmanagement.backend.repository.ApplicationRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.UUID;

/**
 * 数据初始化器 - 用于创建测试数据
 * 
 * <AUTHOR> Management System
 * @version 1.0.0
 */
@Component
public class DataInitializer implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(DataInitializer.class);

    @Autowired
    private LogEntryService logEntryService;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private ApplicationRepository applicationRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    private final Random random = new Random();

    private final String[] sources = {
        "UserService", "OrderService", "PaymentService", "NotificationService",
        "AuthService", "ProductService", "InventoryService", "ReportService"
    };

    private final String[] threads = {
        "http-nio-8080-exec-1", "http-nio-8080-exec-2", "http-nio-8080-exec-3",
        "scheduler-1", "scheduler-2", "async-task-1", "async-task-2"
    };





    private final String[] environments = {
        "dev", "test", "staging", "prod"
    };

    private final String[] errorMessages = {
        "数据库连接超时",
        "用户认证失败",
        "订单处理异常",
        "支付接口调用失败",
        "文件上传失败",
        "缓存服务不可用",
        "第三方API调用超时",
        "数据验证失败"
    };

    private final String[] warnMessages = {
        "数据库连接池使用率过高",
        "内存使用率超过80%",
        "响应时间较慢",
        "重试次数过多",
        "配置文件缺少某些参数",
        "临时文件清理失败",
        "缓存命中率较低"
    };

    private final String[] infoMessages = {
        "用户登录成功",
        "订单创建成功",
        "支付完成",
        "邮件发送成功",
        "文件上传完成",
        "定时任务执行完成",
        "数据同步完成",
        "系统启动完成"
    };

    private final String[] debugMessages = {
        "开始处理用户请求",
        "查询数据库",
        "调用第三方接口",
        "执行业务逻辑",
        "返回处理结果",
        "清理临时数据",
        "更新缓存",
        "记录操作日志"
    };

    @Override
    public void run(String... args) throws Exception {
        logger.info("开始检查和初始化数据...");

        // 初始化用户数据
        initUsers();

        // 初始化应用数据
        List<String> createdAppIds = initApplications();

        // 更新用户的授权应用ID
        updateUserAuthorizations(createdAppIds);

        // 修复空ID的日志数据
        fixEmptyIdLogs();

        // 初始化日志数据
        initLogData(createdAppIds);

        logger.info("数据初始化完成");
    }

    private void initUsers() {
        // 检查是否已有用户数据
        if (userRepository.count() > 0) {
            logger.info("数据库中已存在用户数据，跳过用户初始化");
            return;
        }

        logger.info("开始初始化用户数据...");

        List<User> users = new ArrayList<>();

        // 管理员用户
        User admin = new User();
        admin.setId("admin-001");
        admin.setUsername("admin");
        admin.setPassword(passwordEncoder.encode("123456"));
        admin.setEmail("<EMAIL>");
        admin.setRealName("系统管理员");
        admin.setRole(UserRole.SUPER_ADMIN);
        admin.setStatus(UserStatus.ACTIVE);
        LocalDateTime userCreateTime = LocalDateTime.now().minusDays(random.nextInt(90));
        admin.setCreatedAt(userCreateTime.plusNanos(random.nextInt(1000) * 1000000));
        users.add(admin);

        // 开发者用户
        User developer = new User();
        developer.setId("dev-001");
        developer.setUsername("developer");
        developer.setPassword(passwordEncoder.encode("123456"));
        developer.setEmail("<EMAIL>");
        developer.setRealName("开发者");
        developer.setRole(UserRole.DEVELOPER);
        developer.setStatus(UserStatus.ACTIVE);
        // 授权应用ID将在应用创建后设置
        developer.setCreatedAt(userCreateTime.plusDays(1).plusNanos(random.nextInt(1000) * 1000000));
        users.add(developer);

        // 普通用户
        User user = new User();
        user.setId("user-001");
        user.setUsername("user");
        user.setPassword(passwordEncoder.encode("123456"));
        user.setEmail("<EMAIL>");
        user.setRealName("普通用户");
        user.setRole(UserRole.VIEWER);
        user.setStatus(UserStatus.ACTIVE);
        // 授权应用ID将在应用创建后设置
        user.setCreatedAt(userCreateTime.plusDays(2).plusNanos(random.nextInt(1000) * 1000000));
        users.add(user);

        // 批量保存用户
        userRepository.saveAll(users);
        logger.info("用户数据初始化完成，共创建 {} 个用户", users.size());
    }

    private List<String> initApplications() {
        // 检查是否已有应用数据
        if (applicationRepository.count() > 0) {
            logger.info("数据库中已存在应用数据，跳过应用初始化");
            // 返回现有应用的ID列表
            return applicationRepository.findAll().stream()
                    .map(Application::getId)
                    .collect(java.util.stream.Collectors.toList());
        }

        logger.info("开始初始化应用数据...");

        List<Application> applications = new ArrayList<>();
        List<String> appIds = new ArrayList<>();

        // 创建测试应用
        String[] appNames = {"用户管理系统", "订单管理系统", "支付系统", "通知系统"};
        String[] environments = {"dev", "test", "staging", "prod"};

        for (int i = 0; i < appNames.length; i++) {
            Application app = new Application();
            // 按照新增应用时的方式生成ID
            String appId = "app-" + UUID.randomUUID().toString().replace("-", "");
            app.setId(appId);
            app.setName(appNames[i]);
            app.setDescription(appNames[i] + "的日志管理");
            app.setToken(UUID.randomUUID().toString().replace("-", ""));
            app.setStatus(ApplicationStatus.ACTIVE);
            app.setCreatorId("admin-001");
            app.setCreatorUsername("admin");
            app.setEnvironment(environments[i % environments.length]);
            app.setVersion("1.0.0");
            app.setLogRetentionDays(30);
            // 设置随机的最后活跃时间和创建时间（包含毫秒）
            LocalDateTime now = LocalDateTime.now();
            app.setLastActiveTime(now.minusHours(random.nextInt(24)).plusNanos(random.nextInt(1000) * 1000000));
            app.setCreatedAt(now.minusDays(random.nextInt(30)).plusNanos(random.nextInt(1000) * 1000000));
            applications.add(app);
            appIds.add(appId);
        }

        // 批量保存应用
        applicationRepository.saveAll(applications);
        logger.info("应用数据初始化完成，共创建 {} 个应用", applications.size());

        return appIds;
    }

    private void updateUserAuthorizations(List<String> appIds) {
        if (appIds.isEmpty()) {
            logger.info("没有应用ID，跳过用户授权更新");
            return;
        }

        logger.info("开始更新用户授权应用ID...");

        // 更新开发者用户的授权应用（前两个应用）
        User developer = userRepository.findById("dev-001").orElse(null);
        if (developer != null && appIds.size() >= 2) {
            developer.setAuthorizedAppIds(appIds.subList(0, 2));
            userRepository.save(developer);
            logger.info("开发者用户授权应用更新完成: {}", appIds.subList(0, 2));
        }

        // 更新普通用户的授权应用（第一个应用）
        User user = userRepository.findById("user-001").orElse(null);
        if (user != null && !appIds.isEmpty()) {
            user.setAuthorizedAppIds(java.util.Arrays.asList(appIds.get(0)));
            userRepository.save(user);
            logger.info("普通用户授权应用更新完成: {}", appIds.get(0));
        }

        logger.info("用户授权应用ID更新完成");
    }

    private void fixEmptyIdLogs() {
        logger.info("开始修复空ID的日志数据...");

        try {
            // 查找所有ID为空字符串的日志
            Query query = new Query(Criteria.where("_id").is(""));
            List<LogEntry> emptyIdLogs = mongoTemplate.find(query, LogEntry.class);

            if (emptyIdLogs.isEmpty()) {
                logger.info("没有发现空ID的日志数据");
                return;
            }

            logger.info("发现 {} 条空ID的日志数据，开始修复...", emptyIdLogs.size());

            // 删除空ID的日志
            mongoTemplate.remove(query, LogEntry.class);

            // 重新保存这些日志，让MongoDB自动生成新的ID
            for (LogEntry log : emptyIdLogs) {
                log.setId(null); // 确保ID为null，让MongoDB自动生成
                mongoTemplate.save(log);
            }

            logger.info("空ID日志修复完成，共修复 {} 条日志", emptyIdLogs.size());

        } catch (Exception e) {
            logger.error("修复空ID日志时发生错误", e);
        }
    }

    private void initLogData(List<String> appIds) {
        // 检查是否已有日志数据
        long logCount = logEntryService.count();
        if (logCount > 0) {
            logger.info("数据库中已存在{}条日志数据，跳过日志初始化", logCount);
            return;
        }

        if (appIds.isEmpty()) {
            logger.info("没有应用ID，跳过日志初始化");
            return;
        }

        logger.info("开始初始化日志数据...");

        List<LogEntry> logEntries = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();

        // 生成最近7天的测试数据
        for (int day = 6; day >= 0; day--) {
            LocalDateTime dayStart = now.minusDays(day).withHour(0).withMinute(0).withSecond(0);

            // 每天生成50-200条日志
            int dailyLogCount = 50 + random.nextInt(151);

            for (int i = 0; i < dailyLogCount; i++) {
                LogEntry logEntry = createRandomLogEntry(dayStart, appIds);
                logEntries.add(logEntry);
            }
        }

        // 批量保存
        logEntryService.saveAll(logEntries);

        logger.info("日志数据初始化完成，共生成 {} 条日志记录", logEntries.size());
    }

    private LogEntry createRandomLogEntry(LocalDateTime baseTime, List<String> appIds) {
        LogEntry logEntry = new LogEntry();

        // 随机时间（在当天内，包含秒和毫秒）
        LocalDateTime timestamp = baseTime
            .plusMinutes(random.nextInt(1440)) // 1440分钟 = 24小时
            .plusSeconds(random.nextInt(60))   // 随机秒数
            .plusNanos(random.nextInt(1000) * 1000000); // 随机毫秒数（转换为纳秒）
        logEntry.setTimestamp(timestamp);

        // 随机日志级别（按真实比例分布）
        LogLevel level = getRandomLogLevel();
        logEntry.setLevel(level);

        // 随机来源
        logEntry.setSource(sources[random.nextInt(sources.length)]);

        // 随机线程
        logEntry.setThread(threads[random.nextInt(threads.length)]);

        // 设置应用ID（从传入的应用ID列表中随机选择）
        String appId = appIds.get(random.nextInt(appIds.size()));
        logEntry.setApplicationId(appId);

        // 设置环境
        logEntry.setEnvironment(environments[random.nextInt(environments.length)]);

        // 根据级别设置消息
        String message = getRandomMessage(level);
        logEntry.setMessage(message);

        // 如果是错误级别，添加异常信息
        if (level == LogLevel.ERROR && random.nextBoolean()) {
            logEntry.setException(generateRandomException());
        }

        // 添加环境属性
        if (random.nextBoolean()) {
            Map<String, Object> envProperties = new HashMap<>();
            envProperties.put("hostname", "server-" + (random.nextInt(10) + 1));
            envProperties.put("region", random.nextBoolean() ? "us-east-1" : "us-west-2");
            envProperties.put("instanceId", "i-" + Long.toHexString(random.nextLong()));
            envProperties.put("cpuUsage", (random.nextInt(80) + 10) + "%");
            envProperties.put("memoryUsage", (random.nextInt(8) + 1) + "GB");
            logEntry.setEnvironmentProperties(envProperties);
        }

        // 添加扩展属性
        if (random.nextBoolean()) {
            Map<String, Object> extendProperties = new HashMap<>();
            extendProperties.put("userId", "user_" + (1000 + random.nextInt(9000)));
            extendProperties.put("requestId", "req_" + System.currentTimeMillis() + "_" + random.nextInt(1000));
            extendProperties.put("duration", random.nextInt(5000) + "ms");
            extendProperties.put("ip", "192.168." + random.nextInt(255) + "." + random.nextInt(255));
            logEntry.setExtendProperties(extendProperties);
        }

        // 添加一些元数据
        if (random.nextBoolean()) {
            Map<String, Object> metadata = new HashMap<>();
            metadata.put("traceId", "trace_" + Long.toHexString(random.nextLong()));
            metadata.put("spanId", "span_" + Long.toHexString(random.nextLong()));
            metadata.put("version", "1." + random.nextInt(10) + "." + random.nextInt(100));
            logEntry.setMetadata(metadata);
        }

        return logEntry;
    }

    private LogLevel getRandomLogLevel() {
        int rand = random.nextInt(100);
        if (rand < 5) return LogLevel.ERROR;      // 5%
        if (rand < 15) return LogLevel.WARN;      // 10%
        if (rand < 70) return LogLevel.INFO;      // 55%
        return LogLevel.DEBUG;                    // 30%
    }

    private String getRandomMessage(LogLevel level) {
        switch (level) {
            case ERROR:
                return errorMessages[random.nextInt(errorMessages.length)];
            case WARN:
                return warnMessages[random.nextInt(warnMessages.length)];
            case INFO:
                return infoMessages[random.nextInt(infoMessages.length)];
            case DEBUG:
                return debugMessages[random.nextInt(debugMessages.length)];
            default:
                return "未知消息";
        }
    }

    private String generateRandomException() {
        String[] exceptionTypes = {
            "java.sql.SQLException",
            "java.net.SocketTimeoutException",
            "java.lang.IllegalArgumentException",
            "java.io.IOException",
            "java.lang.NullPointerException"
        };
        
        String exceptionType = exceptionTypes[random.nextInt(exceptionTypes.length)];
        return exceptionType + ": " + errorMessages[random.nextInt(errorMessages.length)] +
               "\n\tat com.example.service.SomeService.method(SomeService.java:" + (10 + random.nextInt(90)) + ")" +
               "\n\tat com.example.controller.SomeController.handle(SomeController.java:" + (20 + random.nextInt(80)) + ")";
    }
}
