package com.logmanagement.backend.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.*;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spi.service.contexts.SecurityContext;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2WebMvc;

import java.util.Collections;
import java.util.List;

/**
 * Knife4j API文档配置
 * 
 * <AUTHOR> Management System
 * @since 1.0.0
 */
@Configuration
@EnableSwagger2WebMvc
@Profile({"dev", "test"}) // 只在开发和测试环境启用
public class Knife4jConfig {

    /**
     * 创建API文档
     */
    @Bean
    public Docket createRestApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.logmanagement.backend.controller"))
                .paths(PathSelectors.any())
                .build()
                .securitySchemes(securitySchemes())
                .securityContexts(securityContexts());
    }

    /**
     * API信息
     */
    private ApiInfo apiInfo() {
        // 创建联系人信息
        Contact contact = new Contact(
            "Logger Management Team",
            "https://github.com/logger-management",
            "<EMAIL>"
        );

        return new ApiInfoBuilder()
                .title("通用日志管理系统 API 文档")
                .description("## 系统简介\n" +
                           "通用日志管理系统后端API接口文档，提供日志收集、查询、用户管理、应用管理等功能。\n\n" +
                           "## 开发信息\n" +
                           "- **开发团队**: Logger Management Team\n" +
                           "- **联系邮箱**: <EMAIL>\n" +
                           "- **项目地址**: https://github.com/logger-management\n" +
                           "- **API版本**: v1.0.0\n" +
                           "- **最后更新**: 2025-06-19\n\n" +
                           "## 认证说明\n" +
                           "本API使用JWT令牌进行身份认证，请在请求头中添加：\n" +
                           "```\n" +
                           "Authorization: Bearer <your-jwt-token>\n" +
                           "```")
                .version("1.0.0")
                .contact(contact)
                .termsOfServiceUrl("https://github.com/logger-management")
                .license("MIT License")
                .licenseUrl("https://opensource.org/licenses/MIT")
                .build();
    }

    /**
     * 安全模式，这里配置通过请求头Authorization传递token参数
     */
    private List<SecurityScheme> securitySchemes() {
        ApiKey apiKey = new ApiKey("Authorization", "Authorization", "header");
        return Collections.singletonList(apiKey);
    }

    /**
     * 安全上下文
     */
    private List<SecurityContext> securityContexts() {
        SecurityContext securityContext = SecurityContext.builder()
                .securityReferences(defaultAuth())
                .forPaths(PathSelectors.regex("/.*"))
                .build();
        return Collections.singletonList(securityContext);
    }

    /**
     * 默认的安全上下文
     */
    private List<SecurityReference> defaultAuth() {
        AuthorizationScope authorizationScope = new AuthorizationScope("global", "accessEverything");
        AuthorizationScope[] authorizationScopes = new AuthorizationScope[1];
        authorizationScopes[0] = authorizationScope;
        SecurityReference securityReference = new SecurityReference("Authorization", authorizationScopes);
        return Collections.singletonList(securityReference);
    }
}
