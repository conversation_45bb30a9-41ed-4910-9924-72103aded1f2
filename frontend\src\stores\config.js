import { defineStore } from 'pinia'
import { ref } from 'vue'
import { configApi } from '../api/config'

export const useConfigStore = defineStore('config', () => {
  // 分页配置
  const paginationConfig = ref({
    defaultPageSize: 20,
    maxPageSize: 100
  })

  // 系统配置
  const systemConfig = ref({})

  // 配置加载状态
  const configLoaded = ref(false)
  const loading = ref(false)

  // 加载分页配置
  const loadPaginationConfig = async () => {
    if (configLoaded.value) {
      return paginationConfig.value
    }

    try {
      loading.value = true
      const response = await configApi.getPaginationConfig()
      
      if (response.code === 200 && response.data) {
        paginationConfig.value = response.data
      } else {
        console.warn('获取分页配置失败，使用默认配置:', response.message)
      }
    } catch (error) {
      console.warn('获取分页配置失败，使用默认配置:', error)
    } finally {
      loading.value = false
      configLoaded.value = true
    }

    return paginationConfig.value
  }

  // 加载系统配置
  const loadSystemConfig = async () => {
    try {
      loading.value = true
      const response = await configApi.getSystemConfig()
      
      if (response.code === 200 && response.data) {
        systemConfig.value = response.data
        
        // 更新分页配置
        if (response.data.pagination) {
          paginationConfig.value = response.data.pagination
        }
      } else {
        console.warn('获取系统配置失败:', response.message)
      }
    } catch (error) {
      console.warn('获取系统配置失败:', error)
    } finally {
      loading.value = false
      configLoaded.value = true
    }

    return systemConfig.value
  }

  // 获取默认页面大小
  const getDefaultPageSize = () => {
    return paginationConfig.value.defaultPageSize
  }

  // 获取最大页面大小
  const getMaxPageSize = () => {
    return paginationConfig.value.maxPageSize
  }

  // 验证页面大小
  const validatePageSize = (size) => {
    if (!size || size <= 0) {
      return paginationConfig.value.defaultPageSize
    }
    if (size > paginationConfig.value.maxPageSize) {
      return paginationConfig.value.maxPageSize
    }
    return size
  }

  // 重置配置
  const resetConfig = () => {
    paginationConfig.value = {
      defaultPageSize: 20,
      maxPageSize: 100
    }
    systemConfig.value = {}
    configLoaded.value = false
  }

  // 初始化配置（应用启动时调用）
  const initConfig = async () => {
    await loadSystemConfig()
  }

  return {
    // 状态
    paginationConfig,
    systemConfig,
    configLoaded,
    loading,
    
    // 方法
    loadPaginationConfig,
    loadSystemConfig,
    getDefaultPageSize,
    getMaxPageSize,
    validatePageSize,
    resetConfig,
    initConfig
  }
})
