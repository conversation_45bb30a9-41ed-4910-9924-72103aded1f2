pipeline {
    agent any
    
    environment {
        // 项目配置
        PROJECT_NAME = 'log-management'
        DOCKER_REGISTRY = 'your-docker-registry.com'  // 请修改为您的Docker仓库地址
        
        // 镜像标签
        VERSION = sh(script: 'git rev-parse --short HEAD', returnStdout: true).trim()
        IMAGE_TAG = "${VERSION}-${BUILD_NUMBER}"
        FRONTEND_IMAGE = "${DOCKER_REGISTRY}/${PROJECT_NAME}-frontend:${IMAGE_TAG}"
        BACKEND_IMAGE = "${DOCKER_REGISTRY}/${PROJECT_NAME}-backend:${IMAGE_TAG}"
        
        // MongoDB配置 - 请在Jenkins中配置这些凭据
        MONGODB_HOST = credentials('mongodb-host')
        MONGODB_USERNAME = credentials('mongodb-username')
        MONGODB_PASSWORD = credentials('mongodb-password')
        MONGODB_DATABASE = 'logger_management'
        MONGODB_AUTH_DATABASE = 'admin'
        
        // 部署服务器配置
        DEPLOY_SERVER = credentials('deploy-server-host')
        SSH_CREDENTIALS = 'deploy-server-ssh'
    }
    
    options {
        // 构建保留策略
        buildDiscarder(logRotator(numToKeepStr: '10'))
        
        // 超时设置
        timeout(time: 30, unit: 'MINUTES')
        
        // 禁止并发构建
        disableConcurrentBuilds()
    }
    
    parameters {
        choice(
            name: 'DEPLOY_ENV',
            choices: ['development', 'staging', 'production'],
            description: '选择部署环境'
        )
        
        booleanParam(
            name: 'SKIP_TESTS',
            defaultValue: false,
            description: '跳过测试'
        )
        
        booleanParam(
            name: 'ROLLBACK',
            defaultValue: false,
            description: '执行回滚'
        )
    }
    
    stages {
        stage('Checkout') {
            steps {
                echo 'Checking out source code...'
                checkout scm
                
                script {
                    // 获取Git信息
                    env.GIT_COMMIT = sh(script: 'git rev-parse HEAD', returnStdout: true).trim()
                    env.GIT_BRANCH = sh(script: 'git rev-parse --abbrev-ref HEAD', returnStdout: true).trim()
                    env.GIT_AUTHOR = sh(script: 'git log -1 --pretty=format:"%an"', returnStdout: true).trim()
                }
                
                echo "Git Commit: ${env.GIT_COMMIT}"
                echo "Git Branch: ${env.GIT_BRANCH}"
                echo "Git Author: ${env.GIT_AUTHOR}"
            }
        }
        
        stage('Tests') {
            when {
                not { params.SKIP_TESTS }
            }
            parallel {
                stage('Backend Tests') {
                    steps {
                        echo 'Running backend tests...'
                        dir('backend') {
                            sh '''
                                # 运行Maven测试
                                mvn clean test
                            '''
                        }
                    }
                    post {
                        always {
                            // 发布测试结果
                            junit 'backend/target/surefire-reports/*.xml'
                        }
                    }
                }
                
                stage('Frontend Tests') {
                    steps {
                        echo 'Running frontend tests...'
                        dir('frontend') {
                            sh '''
                                # 安装依赖并运行测试
                                npm ci
                                npm run test:unit
                            '''
                        }
                    }
                    post {
                        always {
                            // 发布测试覆盖率报告
                            publishHTML([
                                allowMissing: false,
                                alwaysLinkToLastBuild: true,
                                keepAll: true,
                                reportDir: 'frontend/coverage',
                                reportFiles: 'index.html',
                                reportName: 'Frontend Coverage Report'
                            ])
                        }
                    }
                }
            }
        }
        
        stage('Build Images') {
            parallel {
                stage('Build Backend') {
                    steps {
                        echo 'Building backend image...'
                        dir('backend') {
                            script {
                                // 构建后端镜像
                                def backendImage = docker.build("${BACKEND_IMAGE}")
                                
                                // 推送到镜像仓库
                                docker.withRegistry("https://${DOCKER_REGISTRY}", 'docker-registry-credentials') {
                                    backendImage.push()
                                    backendImage.push('latest')
                                }
                            }
                        }
                    }
                }
                
                stage('Build Frontend') {
                    steps {
                        echo 'Building frontend image...'
                        dir('frontend') {
                            script {
                                // 构建前端镜像
                                def frontendImage = docker.build("${FRONTEND_IMAGE}")
                                
                                // 推送到镜像仓库
                                docker.withRegistry("https://${DOCKER_REGISTRY}", 'docker-registry-credentials') {
                                    frontendImage.push()
                                    frontendImage.push('latest')
                                }
                            }
                        }
                    }
                }
            }
        }
        
        stage('Deploy') {
            when {
                anyOf {
                    branch 'main'
                    branch 'develop'
                    branch 'release/*'
                    expression { params.DEPLOY_ENV != null }
                }
            }
            steps {
                script {
                    if (params.ROLLBACK) {
                        echo 'Performing rollback...'
                        // 执行回滚逻辑
                        sshagent([SSH_CREDENTIALS]) {
                            sh '''
                                ssh -o StrictHostKeyChecking=no root@${DEPLOY_SERVER} "
                                    cd /opt/log-management
                                    docker-compose down
                                    git checkout HEAD~1
                                    docker-compose up -d
                                "
                            '''
                        }
                    } else {
                        echo 'Deploying to server...'
                        
                        // 确定部署环境
                        def deployEnv = params.DEPLOY_ENV ?: (env.BRANCH_NAME == 'main' ? 'production' : 'staging')
                        echo "Deploying to environment: ${deployEnv}"
                        
                        // 如果是生产环境，需要手动确认
                        if (deployEnv == 'production') {
                            input message: 'Deploy to production?', ok: 'Deploy',
                                  submitterParameter: 'DEPLOYER'
                            echo "Deployment approved by: ${env.DEPLOYER}"
                        }
                        
                        // 执行部署
                        sshagent([SSH_CREDENTIALS]) {
                            sh '''
                                # 复制部署脚本到目标服务器
                                scp -o StrictHostKeyChecking=no scripts/jenkins-deploy.sh root@${DEPLOY_SERVER}:/tmp/
                                
                                # 在目标服务器上执行部署
                                ssh -o StrictHostKeyChecking=no root@${DEPLOY_SERVER} "
                                    chmod +x /tmp/jenkins-deploy.sh
                                    /tmp/jenkins-deploy.sh \\
                                        --backend-image ${BACKEND_IMAGE} \\
                                        --frontend-image ${FRONTEND_IMAGE} \\
                                        --mongodb-host ${MONGODB_HOST} \\
                                        --mongodb-username ${MONGODB_USERNAME} \\
                                        --mongodb-password ${MONGODB_PASSWORD} \\
                                        --mongodb-database ${MONGODB_DATABASE}
                                "
                            '''
                        }
                    }
                }
            }
        }
        
        stage('Health Check') {
            steps {
                echo 'Performing health check...'
                script {
                    // 等待服务启动
                    sleep(30)
                    
                    // 检查后端健康状态
                    def backendHealth = sh(
                        script: "curl -f http://${DEPLOY_SERVER}:8080/api/actuator/health",
                        returnStatus: true
                    )
                    
                    // 检查前端可用性
                    def frontendHealth = sh(
                        script: "curl -f http://${DEPLOY_SERVER}/",
                        returnStatus: true
                    )
                    
                    if (backendHealth != 0) {
                        error("Backend health check failed!")
                    }
                    
                    if (frontendHealth != 0) {
                        error("Frontend health check failed!")
                    }
                    
                    echo "All health checks passed!"
                }
            }
        }
    }
    
    post {
        always {
            echo 'Cleaning up...'
            sh '''
                # 清理本地镜像
                docker rmi ${BACKEND_IMAGE} || true
                docker rmi ${FRONTEND_IMAGE} || true
                
                # 清理未使用的镜像
                docker image prune -f
            '''
        }
        
        success {
            echo 'Deployment successful!'
            
            // 发送成功通知
            script {
                def message = """
                ✅ 部署成功！
                
                项目: ${PROJECT_NAME}
                构建号: ${BUILD_NUMBER}
                分支: ${env.GIT_BRANCH}
                提交: ${env.GIT_COMMIT}
                作者: ${env.GIT_AUTHOR}
                环境: ${params.DEPLOY_ENV ?: 'auto'}
                
                访问地址:
                前端: http://${DEPLOY_SERVER}
                后端: http://${DEPLOY_SERVER}:8080
                
                构建日志: ${BUILD_URL}console
                """
                
                // 如果配置了Slack，发送通知
                try {
                    slackSend(
                        channel: '#deployments',
                        color: 'good',
                        message: message
                    )
                } catch (Exception e) {
                    echo "Slack notification failed: ${e.message}"
                }
            }
        }
        
        failure {
            echo 'Deployment failed!'
            
            // 发送失败通知
            script {
                def message = """
                ❌ 部署失败！
                
                项目: ${PROJECT_NAME}
                构建号: ${BUILD_NUMBER}
                分支: ${env.GIT_BRANCH}
                提交: ${env.GIT_COMMIT}
                作者: ${env.GIT_AUTHOR}
                
                错误日志: ${BUILD_URL}console
                """
                
                // 如果配置了Slack，发送通知
                try {
                    slackSend(
                        channel: '#deployments',
                        color: 'danger',
                        message: message
                    )
                } catch (Exception e) {
                    echo "Slack notification failed: ${e.message}"
                }
            }
        }
        
        unstable {
            echo 'Build is unstable'
        }
        
        aborted {
            echo 'Build was aborted'
        }
    }
}
