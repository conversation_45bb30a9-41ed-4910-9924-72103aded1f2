#!/bin/bash

# 通用日志管理系统 Docker 部署脚本 - 外部MongoDB版本
# 作者: Logger Management System
# 版本: 1.0.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查环境变量文件
check_env_file() {
    if [ ! -f ".env.external-mongo" ]; then
        log_error "环境变量文件 .env.external-mongo 不存在"
        log_info "请复制 .env.external-mongo 文件并配置您的MongoDB连接信息"
        log_info "cp .env.external-mongo .env"
        log_info "然后编辑 .env 文件中的MongoDB配置"
        exit 1
    fi
    
    # 检查必要的环境变量
    source .env.external-mongo
    
    if [ -z "$MONGODB_HOST" ] || [ "$MONGODB_HOST" = "your-mongodb-host" ]; then
        log_error "请在 .env.external-mongo 文件中配置 MONGODB_HOST"
        exit 1
    fi
    
    if [ -z "$MONGODB_USERNAME" ] || [ "$MONGODB_USERNAME" = "logger_user" ]; then
        log_warning "建议修改默认的数据库用户名"
    fi
    
    if [ -z "$MONGODB_PASSWORD" ] || [ "$MONGODB_PASSWORD" = "logger_password" ]; then
        log_warning "建议修改默认的数据库密码"
    fi
    
    log_success "环境变量检查通过"
}

# 检查MongoDB连接
check_mongodb_connection() {
    log_info "检查MongoDB连接..."
    
    # 使用mongosh或mongo客户端测试连接
    if command -v mongosh &> /dev/null; then
        MONGO_CMD="mongosh"
    elif command -v mongo &> /dev/null; then
        MONGO_CMD="mongo"
    else
        log_warning "未找到MongoDB客户端，跳过连接测试"
        return 0
    fi
    
    # 构建连接字符串
    if [ -n "$MONGODB_USERNAME" ] && [ -n "$MONGODB_PASSWORD" ]; then
        MONGO_URI="mongodb://$MONGODB_USERNAME:$MONGODB_PASSWORD@$MONGODB_HOST:$MONGODB_PORT/$MONGODB_DATABASE"
    else
        MONGO_URI="mongodb://$MONGODB_HOST:$MONGODB_PORT/$MONGODB_DATABASE"
    fi
    
    # 测试连接
    if $MONGO_CMD "$MONGO_URI" --eval "db.runCommand('ping')" &> /dev/null; then
        log_success "MongoDB连接测试成功"
    else
        log_error "MongoDB连接测试失败，请检查配置"
        log_info "连接信息: $MONGODB_HOST:$MONGODB_PORT"
        log_info "数据库: $MONGODB_DATABASE"
        exit 1
    fi
}

# 检查Docker和Docker Compose
check_requirements() {
    log_info "检查系统要求..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    log_success "系统要求检查通过"
}

# 构建镜像
build_images() {
    log_info "构建应用镜像..."
    
    # 使用外部MongoDB的compose文件
    docker-compose -f docker-compose-external-mongo.yml --env-file .env.external-mongo build
    
    log_success "镜像构建完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 启动后端服务
    log_info "启动后端服务..."
    docker-compose -f docker-compose-external-mongo.yml --env-file .env.external-mongo up -d backend
    
    # 等待后端启动
    log_info "等待后端服务启动..."
    sleep 30
    
    # 启动前端服务
    log_info "启动前端服务..."
    docker-compose -f docker-compose-external-mongo.yml --env-file .env.external-mongo up -d frontend
    
    log_success "所有服务启动完成"
}

# 检查服务状态
check_status() {
    log_info "检查服务状态..."
    docker-compose -f docker-compose-external-mongo.yml ps
    
    log_info "检查服务健康状态..."
    sleep 10
    
    # 检查后端健康状态
    if curl -f http://localhost:${BACKEND_PORT:-8080}/api/actuator/health &> /dev/null; then
        log_success "后端服务健康检查通过"
    else
        log_warning "后端服务健康检查失败，请查看日志"
    fi
    
    # 检查前端健康状态
    if curl -f http://localhost:${FRONTEND_PORT:-80}/ &> /dev/null; then
        log_success "前端服务健康检查通过"
    else
        log_warning "前端服务健康检查失败，请查看日志"
    fi
}

# 显示访问信息
show_access_info() {
    log_success "部署完成！"
    echo ""
    echo "=========================================="
    echo "通用日志管理系统访问信息"
    echo "=========================================="
    echo "前端地址: http://localhost:${FRONTEND_PORT:-80}"
    echo "后端API: http://localhost:${BACKEND_PORT:-8080}/api"
    echo "MongoDB: $MONGODB_HOST:$MONGODB_PORT"
    echo "数据库: $MONGODB_DATABASE"
    echo "=========================================="
    echo ""
    echo "常用命令:"
    echo "  查看日志: docker-compose -f docker-compose-external-mongo.yml logs -f [service_name]"
    echo "  停止服务: docker-compose -f docker-compose-external-mongo.yml down"
    echo "  重启服务: docker-compose -f docker-compose-external-mongo.yml restart [service_name]"
    echo "  查看状态: docker-compose -f docker-compose-external-mongo.yml ps"
    echo "=========================================="
}

# 主函数
main() {
    echo "=========================================="
    echo "通用日志管理系统 Docker 部署脚本"
    echo "外部MongoDB版本"
    echo "=========================================="
    
    check_requirements
    check_env_file
    check_mongodb_connection
    build_images
    start_services
    check_status
    show_access_info
}

# 执行主函数
main "$@"
