# 测试修复后的属性搜索功能

Write-Host "=== 测试修复后的属性搜索功能 ===" -ForegroundColor Green

# 1. 获取认证token
Write-Host "1. 获取认证token..." -ForegroundColor Yellow
$loginBody = @{
    username = "admin"
    password = "admin123"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/auth/login" -Method Post -ContentType "application/json" -Body $loginBody
    $token = $loginResponse.data.token
    Write-Host "✓ 登录成功，获取到token" -ForegroundColor Green
} catch {
    Write-Host "✗ 登录失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 设置请求头
$headers = @{
    Authorization = "Bearer $token"
    "Content-Type" = "application/json"
}

# 2. 测试搜索 "1.0.0" (版本号)
Write-Host "`n2. 测试搜索版本号 '1.0.0'..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "http://localhost:8080/api/logs?page=0&size=5&propertySearch=1.0.0&propertyType=extendProperties" -Method Get -Headers $headers
    $total = if ($response.data.totalElements) { $response.data.totalElements } else { $response.data.total }
    Write-Host "✓ 搜索结果: 找到 $total 条匹配的日志" -ForegroundColor Green
    
    if ($total -gt 0) {
        Write-Host "  前几条结果的extendProperties:" -ForegroundColor Cyan
        $logs = if ($response.data.content) { $response.data.content } else { $response.data }
        for ($i = 0; $i -lt [Math]::Min(3, $logs.Count); $i++) {
            $log = $logs[$i]
            Write-Host "    - $($log.extendProperties | ConvertTo-Json -Compress)" -ForegroundColor White
        }
    }
} catch {
    Write-Host "✗ 搜索失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 3. 测试搜索 "24468" (进程ID)
Write-Host "`n3. 测试搜索进程ID '24468'..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "http://localhost:8080/api/logs?page=0&size=5&propertySearch=24468&propertyType=extendProperties" -Method Get -Headers $headers
    $total = if ($response.data.totalElements) { $response.data.totalElements } else { $response.data.total }
    Write-Host "✓ 搜索结果: 找到 $total 条匹配的日志" -ForegroundColor Green
    
    if ($total -gt 0) {
        Write-Host "  前几条结果的extendProperties:" -ForegroundColor Cyan
        $logs = if ($response.data.content) { $response.data.content } else { $response.data }
        for ($i = 0; $i -lt [Math]::Min(3, $logs.Count); $i++) {
            $log = $logs[$i]
            Write-Host "    - $($log.extendProperties | ConvertTo-Json -Compress)" -ForegroundColor White
        }
    }
} catch {
    Write-Host "✗ 搜索失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 4. 测试搜索 "development" (环境)
Write-Host "`n4. 测试搜索环境 'development'..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "http://localhost:8080/api/logs?page=0&size=5&propertySearch=development&propertyType=extendProperties" -Method Get -Headers $headers
    $total = if ($response.data.totalElements) { $response.data.totalElements } else { $response.data.total }
    Write-Host "✓ 搜索结果: 找到 $total 条匹配的日志" -ForegroundColor Green
    
    if ($total -gt 0) {
        Write-Host "  前几条结果的extendProperties:" -ForegroundColor Cyan
        $logs = if ($response.data.content) { $response.data.content } else { $response.data }
        for ($i = 0; $i -lt [Math]::Min(3, $logs.Count); $i++) {
            $log = $logs[$i]
            Write-Host "    - $($log.extendProperties | ConvertTo-Json -Compress)" -ForegroundColor White
        }
    }
} catch {
    Write-Host "✗ 搜索失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 5. 测试搜索 "AAC-LIUYJ01-NB" (机器名)
Write-Host "`n5. 测试搜索机器名 'AAC-LIUYJ01-NB'..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "http://localhost:8080/api/logs?page=0&size=5&propertySearch=AAC-LIUYJ01-NB&propertyType=extendProperties" -Method Get -Headers $headers
    $total = if ($response.data.totalElements) { $response.data.totalElements } else { $response.data.total }
    Write-Host "✓ 搜索结果: 找到 $total 条匹配的日志" -ForegroundColor Green
    
    if ($total -gt 0) {
        Write-Host "  前几条结果的extendProperties:" -ForegroundColor Cyan
        $logs = if ($response.data.content) { $response.data.content } else { $response.data }
        for ($i = 0; $i -lt [Math]::Min(3, $logs.Count); $i++) {
            $log = $logs[$i]
            Write-Host "    - $($log.extendProperties | ConvertTo-Json -Compress)" -ForegroundColor White
        }
    }
} catch {
    Write-Host "✗ 搜索失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
