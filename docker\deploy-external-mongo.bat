@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: 通用日志管理系统 Docker 部署脚本 - 外部MongoDB版本 (Windows)
:: 作者: Logger Management System
:: 版本: 1.0.0

echo ==========================================
echo 通用日志管理系统 Docker 部署脚本
echo 外部MongoDB版本
echo ==========================================

:: 检查环境变量文件
if not exist ".env.external-mongo" (
    echo [ERROR] 环境变量文件 .env.external-mongo 不存在
    echo [INFO] 请复制 .env.external-mongo 文件并配置您的MongoDB连接信息
    echo [INFO] copy .env.external-mongo .env
    echo [INFO] 然后编辑 .env 文件中的MongoDB配置
    pause
    exit /b 1
)

echo [SUCCESS] 环境变量文件检查通过

:: 检查Docker和Docker Compose
echo [INFO] 检查系统要求...
docker --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker 未安装，请先安装 Docker Desktop
    pause
    exit /b 1
)

docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker Compose 未安装，请先安装 Docker Compose
    pause
    exit /b 1
)

echo [SUCCESS] 系统要求检查通过

:: 构建镜像
echo [INFO] 构建应用镜像...
docker-compose -f docker-compose-external-mongo.yml --env-file .env.external-mongo build
if errorlevel 1 (
    echo [ERROR] 镜像构建失败
    pause
    exit /b 1
)

echo [SUCCESS] 镜像构建完成

:: 启动服务
echo [INFO] 启动服务...

echo [INFO] 启动后端服务...
docker-compose -f docker-compose-external-mongo.yml --env-file .env.external-mongo up -d backend
if errorlevel 1 (
    echo [ERROR] 后端服务启动失败
    pause
    exit /b 1
)

echo [INFO] 等待后端服务启动...
timeout /t 30 /nobreak >nul

echo [INFO] 启动前端服务...
docker-compose -f docker-compose-external-mongo.yml --env-file .env.external-mongo up -d frontend
if errorlevel 1 (
    echo [ERROR] 前端服务启动失败
    pause
    exit /b 1
)

echo [SUCCESS] 所有服务启动完成

:: 检查服务状态
echo [INFO] 检查服务状态...
docker-compose -f docker-compose-external-mongo.yml ps

:: 读取环境变量
for /f "tokens=1,2 delims==" %%a in (.env.external-mongo) do (
    if "%%a"=="FRONTEND_PORT" set FRONTEND_PORT=%%b
    if "%%a"=="BACKEND_PORT" set BACKEND_PORT=%%b
    if "%%a"=="MONGODB_HOST" set MONGODB_HOST=%%b
    if "%%a"=="MONGODB_PORT" set MONGODB_PORT=%%b
    if "%%a"=="MONGODB_DATABASE" set MONGODB_DATABASE=%%b
)

:: 设置默认值
if not defined FRONTEND_PORT set FRONTEND_PORT=80
if not defined BACKEND_PORT set BACKEND_PORT=8080

:: 显示访问信息
echo [SUCCESS] 部署完成！
echo.
echo ==========================================
echo 通用日志管理系统访问信息
echo ==========================================
echo 前端地址: http://localhost:!FRONTEND_PORT!
echo 后端API: http://localhost:!BACKEND_PORT!/api
if defined MONGODB_HOST (
    echo MongoDB: !MONGODB_HOST!:!MONGODB_PORT!
    echo 数据库: !MONGODB_DATABASE!
)
echo ==========================================
echo.
echo 常用命令:
echo   查看日志: docker-compose -f docker-compose-external-mongo.yml logs -f [service_name]
echo   停止服务: docker-compose -f docker-compose-external-mongo.yml down
echo   重启服务: docker-compose -f docker-compose-external-mongo.yml restart [service_name]
echo   查看状态: docker-compose -f docker-compose-external-mongo.yml ps
echo ==========================================

pause
