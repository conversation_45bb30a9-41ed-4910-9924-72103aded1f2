# LOGO 使用说明

## 当前使用的LOGO

系统当前使用的是 `logo.svg` 文件，这是一个SVG格式的矢量图标，具有以下优势：
- 矢量格式，在任何分辨率下都清晰
- 文件体积小
- 可以通过CSS进行样式调整

## 如何替换为自定义LOGO

### 方法1：替换SVG文件
1. 将您的LOGO保存为SVG格式
2. 重命名为 `logo.svg`
3. 替换 `frontend/public/logo.svg` 文件

### 方法2：使用PNG文件
1. 将您的LOGO保存为PNG格式（建议尺寸：200x40px，背景透明）
2. 重命名为 `logo.png`
3. 将文件放置在 `frontend/public/` 目录下
4. 修改 `frontend/src/App.vue` 中的图片路径：
   ```html
   <img src="/logo.png" alt="通用日志管理系统" class="app-logo" />
   ```

### 方法3：使用其他格式
支持的图片格式：PNG, JPG, SVG, WebP
只需要将文件放在 `frontend/public/` 目录下，并更新App.vue中的路径即可。

## LOGO设计建议

- **尺寸**：建议宽度200px，高度40px左右
- **背景**：建议使用透明背景
- **颜色**：由于导航栏背景是渐变色，建议使用白色或浅色LOGO
- **格式**：推荐使用SVG格式以获得最佳显示效果

## 响应式设计

系统已经为不同屏幕尺寸设置了响应式样式：
- 桌面端：高度40px
- 平板端：高度32px  
- 手机端：高度28px

LOGO会自动适应这些尺寸。
