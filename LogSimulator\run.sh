#!/bin/bash

# 日志管理系统模拟器启动脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================"
echo -e "    日志管理系统模拟器"
echo -e "========================================${NC}"
echo

# 检查.NET是否安装
if ! command -v dotnet &> /dev/null; then
    echo -e "${RED}[错误] 未找到 .NET SDK，请先安装 .NET 9.0 SDK${NC}"
    echo "下载地址: https://dotnet.microsoft.com/download"
    exit 1
fi

echo -e "${GREEN}[信息] 检测到 .NET SDK 版本:${NC}"
dotnet --version

echo
echo "请选择运行模式:"
echo "1. 交互模式 (推荐)"
echo "2. 发送单条日志"
echo "3. 批量发送日志 (100条)"
echo "4. 连续发送日志"
echo "5. 测试API连接"
echo "6. 显示配置信息"
echo "7. 自定义批量发送"
echo "8. 构建项目"
echo "9. 退出"
echo

read -p "请输入选项 (1-9): " choice

case $choice in
    1)
        echo
        echo -e "${GREEN}[信息] 启动交互模式...${NC}"
        dotnet run
        ;;
    2)
        echo
        echo -e "${GREEN}[信息] 发送单条日志...${NC}"
        dotnet run -- single
        ;;
    3)
        echo
        echo -e "${GREEN}[信息] 批量发送100条日志...${NC}"
        dotnet run -- batch --count 100 --batch-size 10
        ;;
    4)
        echo
        echo -e "${GREEN}[信息] 开始连续发送日志，按 Ctrl+C 停止...${NC}"
        dotnet run -- continuous
        ;;
    5)
        echo
        echo -e "${GREEN}[信息] 测试API连接...${NC}"
        dotnet run -- test
        ;;
    6)
        echo
        echo -e "${GREEN}[信息] 显示配置信息...${NC}"
        dotnet run -- config
        ;;
    7)
        echo
        read -p "请输入日志总数 (默认100): " count
        count=${count:-100}
        
        read -p "请输入批次大小 (默认10): " batch_size
        batch_size=${batch_size:-10}
        
        read -p "请输入间隔时间/秒 (默认1): " interval
        interval=${interval:-1}
        
        echo
        echo -e "${GREEN}[信息] 批量发送 $count 条日志，批次大小 $batch_size，间隔 $interval 秒...${NC}"
        dotnet run -- batch --count $count --batch-size $batch_size --interval $interval
        ;;
    8)
        echo
        echo -e "${GREEN}[信息] 构建项目...${NC}"
        if dotnet build; then
            echo -e "${GREEN}[成功] 构建完成${NC}"
        else
            echo -e "${RED}[错误] 构建失败${NC}"
        fi
        ;;
    9)
        echo
        echo "退出程序"
        exit 0
        ;;
    *)
        echo
        echo -e "${RED}[错误] 无效选项，请重新选择${NC}"
        exit 1
        ;;
esac

echo
echo -e "${GREEN}操作完成${NC}"
