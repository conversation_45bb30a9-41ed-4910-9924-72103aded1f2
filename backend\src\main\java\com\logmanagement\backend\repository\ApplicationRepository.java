package com.logmanagement.backend.repository;

import com.logmanagement.backend.entity.Application;
import com.logmanagement.backend.entity.ApplicationStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 应用仓库接口
 * 
 * <AUTHOR> Management System
 * @version 1.0.0
 */
@Repository
public interface ApplicationRepository extends MongoRepository<Application, String> {

    /**
     * 根据应用名称查找应用
     * 
     * @param name 应用名称
     * @return 应用信息
     */
    Optional<Application> findByName(String name);

    /**
     * 根据令牌查找应用
     * 
     * @param token 应用令牌
     * @return 应用信息
     */
    Optional<Application> findByToken(String token);

    /**
     * 根据创建者ID查找应用
     * 
     * @param creatorId 创建者ID
     * @param pageable 分页参数
     * @return 应用分页结果
     */
    Page<Application> findByCreatorId(String creatorId, Pageable pageable);

    /**
     * 根据应用状态查找应用
     * 
     * @param status 应用状态
     * @param pageable 分页参数
     * @return 应用分页结果
     */
    Page<Application> findByStatus(ApplicationStatus status, Pageable pageable);

    /**
     * 根据创建者ID和状态查找应用
     * 
     * @param creatorId 创建者ID
     * @param status 应用状态
     * @param pageable 分页参数
     * @return 应用分页结果
     */
    Page<Application> findByCreatorIdAndStatus(String creatorId, ApplicationStatus status, Pageable pageable);

    /**
     * 根据环境查找应用
     * 
     * @param environment 环境
     * @param pageable 分页参数
     * @return 应用分页结果
     */
    Page<Application> findByEnvironment(String environment, Pageable pageable);

    /**
     * 根据应用ID列表查找应用
     * 
     * @param appIds 应用ID列表
     * @return 应用列表
     */
    List<Application> findByIdIn(List<String> appIds);

    /**
     * 查找最后活跃时间在指定时间之前的应用
     * 
     * @param time 时间
     * @return 应用列表
     */
    List<Application> findByLastActiveTimeBefore(LocalDateTime time);

    /**
     * 检查应用名称是否存在
     * 
     * @param name 应用名称
     * @return 是否存在
     */
    boolean existsByName(String name);

    /**
     * 检查令牌是否存在
     * 
     * @param token 应用令牌
     * @return 是否存在
     */
    boolean existsByToken(String token);

    /**
     * 统计指定创建者的应用数量
     * 
     * @param creatorId 创建者ID
     * @return 应用数量
     */
    long countByCreatorId(String creatorId);

    /**
     * 统计指定状态的应用数量
     * 
     * @param status 应用状态
     * @return 应用数量
     */
    long countByStatus(ApplicationStatus status);

    /**
     * 统计指定环境的应用数量
     *
     * @param environment 环境
     * @return 应用数量
     */
    long countByEnvironment(String environment);

    /**
     * 根据应用名称模糊搜索应用
     *
     * @param name 应用名称关键词
     * @param pageable 分页参数
     * @return 应用分页结果
     */
    Page<Application> findByNameContainingIgnoreCase(String name, Pageable pageable);

    /**
     * 根据状态和环境查找应用
     *
     * @param status 应用状态
     * @param environment 环境
     * @param pageable 分页参数
     * @return 应用分页结果
     */
    Page<Application> findByStatusAndEnvironment(ApplicationStatus status, String environment, Pageable pageable);

    /**
     * 根据创建者ID和环境查找应用
     *
     * @param creatorId 创建者ID
     * @param environment 环境
     * @param pageable 分页参数
     * @return 应用分页结果
     */
    Page<Application> findByCreatorIdAndEnvironment(String creatorId, String environment, Pageable pageable);
}
