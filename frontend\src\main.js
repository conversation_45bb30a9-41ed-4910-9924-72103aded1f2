import './assets/main.css'
import './assets/variables.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
import { useConfigStore } from './stores/config'

// Element Plus
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
// Element Plus 中文语言包
import zhCn from 'element-plus/es/locale/lang/zh-cn'

const app = createApp(App)
const pinia = createPinia()

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(pinia)
app.use(router)
// 配置Element Plus使用中文
app.use(ElementPlus, {
  locale: zhCn,
})

// 初始化系统配置
const initApp = async () => {
  const configStore = useConfigStore()
  try {
    await configStore.initConfig()
  } catch (error) {
    console.warn('系统配置初始化失败，使用默认配置:', error)
  }

  app.mount('#app')
}

// 启动应用
initApp()
