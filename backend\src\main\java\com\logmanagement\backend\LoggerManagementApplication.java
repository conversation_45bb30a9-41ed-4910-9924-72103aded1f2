package com.logmanagement.backend;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.data.mongodb.config.EnableMongoAuditing;

/**
 * 通用日志管理系统主应用类
 *
 * <AUTHOR> Management System
 * @version 1.0.0
 */
@SpringBootApplication
@EnableMongoAuditing
public class LoggerManagementApplication {

    public static void main(String[] args) {
        SpringApplication.run(LoggerManagementApplication.class, args);
        System.out.println("=================================");
        System.out.println("通用日志管理系统后端服务启动成功！");
        System.out.println("访问地址: http://localhost:8080/api");
        System.out.println("=================================");
    }
}
