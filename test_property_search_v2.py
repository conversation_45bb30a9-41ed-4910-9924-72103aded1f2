#!/usr/bin/env python3
"""
测试属性搜索功能的脚本
"""

import requests
import json

# 配置
BASE_URL = "http://localhost:8080/api"
USERNAME = "admin"
PASSWORD = "admin123"

def login():
    """登录并获取token"""
    login_data = {
        "username": USERNAME,
        "password": PASSWORD
    }
    
    response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
    if response.status_code == 200:
        result = response.json()
        if result.get("code") == 200:
            return result["data"]["token"]
    
    print(f"登录失败: {response.text}")
    return None

def test_property_search(token, search_term, property_type="all"):
    """测试属性搜索"""
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    params = {
        "page": 1,
        "size": 5,
        "propertySearch": search_term,
        "propertyType": property_type
    }
    
    print(f"\n=== 测试属性搜索: '{search_term}' (类型: {property_type}) ===")
    
    response = requests.get(f"{BASE_URL}/logs", headers=headers, params=params)
    
    if response.status_code == 200:
        result = response.json()
        if result.get("code") == 200:
            data = result["data"]
            total = data["total"]
            logs = data["content"]
            
            print(f"找到 {total} 条匹配的日志")
            
            for i, log in enumerate(logs, 1):
                print(f"\n--- 日志 {i} ---")
                print(f"ID: {log['id']}")
                print(f"消息: {log['message']}")
                print(f"时间: {log['timestamp']}")
                
                # 显示属性信息
                if log.get('extendProperties'):
                    print(f"扩展属性: {json.dumps(log['extendProperties'], ensure_ascii=False)}")
                if log.get('environmentProperties'):
                    print(f"环境属性: {json.dumps(log['environmentProperties'], ensure_ascii=False)}")
                if log.get('metadata'):
                    print(f"元数据: {json.dumps(log['metadata'], ensure_ascii=False)}")
        else:
            print(f"API返回错误: {result}")
    else:
        print(f"请求失败: {response.status_code} - {response.text}")

def main():
    """主函数"""
    print("开始测试属性搜索功能...")
    
    # 登录
    token = login()
    if not token:
        print("无法获取token，退出测试")
        return
    
    print("登录成功，开始测试...")
    
    # 测试不同的搜索条件
    test_cases = [
        ("1.0.0", "all"),           # 搜索版本号
        ("development", "extend"),   # 在扩展属性中搜索
        ("GET", "metadata"),        # 在元数据中搜索HTTP方法
        ("Thread", "all"),          # 搜索线程相关信息
        ("localhost", "metadata"),  # 搜索URL中的localhost
        ("category", "extend"),     # 搜索category键
        ("Business", "extend"),     # 搜索Business值
    ]
    
    for search_term, property_type in test_cases:
        test_property_search(token, search_term, property_type)
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    main()
