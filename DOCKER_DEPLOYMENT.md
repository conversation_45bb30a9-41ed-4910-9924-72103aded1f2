# 通用日志管理系统 Docker 部署完整指南

## 概述

本系统提供两种Docker部署方案：

1. **完整部署方案**：包含前端、后端、MongoDB数据库的完整容器化部署
2. **外部MongoDB方案**：使用现有MongoDB服务器，仅部署前端和后端容器

## 部署方案选择

### 方案一：完整部署（推荐新用户）

**适用场景**：
- 全新部署
- 测试环境
- 开发环境
- 没有现有MongoDB服务器

**包含组件**：
- 前端容器（Vue3 + Nginx）
- 后端容器（Spring Boot + Java 8）
- MongoDB容器（5.0版本）
- MongoDB Express管理界面（可选）

### 方案二：外部MongoDB部署

**适用场景**：
- 已有MongoDB服务器
- 生产环境（使用现有数据库集群）
- 需要与其他系统共享数据库

**包含组件**：
- 前端容器（Vue3 + Nginx）
- 后端容器（Spring Boot + Java 8）
- 连接到外部MongoDB服务器

## 文件结构说明

```
docker/
├── docker-compose.yml              # 完整部署配置
├── docker-compose-external-mongo.yml  # 外部MongoDB部署配置
├── mongo-init.js                   # MongoDB初始化脚本
├── init-external-mongo.js          # 外部MongoDB初始化脚本
├── .env.example                    # 环境变量模板（完整部署）
├── .env.external-mongo             # 环境变量模板（外部MongoDB）
├── deploy.sh / deploy.bat          # 完整部署脚本
├── deploy-external-mongo.sh/.bat   # 外部MongoDB部署脚本
├── stop.sh / stop.bat              # 停止服务脚本
└── README.md                       # 详细部署文档

backend/
├── Dockerfile                      # 后端容器构建文件
└── src/main/resources/
    ├── application-docker.yml      # Docker环境配置
    └── application-docker-external.yml  # 外部MongoDB环境配置

frontend/
├── Dockerfile                      # 前端容器构建文件
└── nginx.conf                      # Nginx配置文件
```

## 快速开始

### 完整部署方案

1. **进入docker目录**
```bash
cd docker
```

2. **运行部署脚本**

**Linux/macOS:**
```bash
chmod +x deploy.sh
./deploy.sh
```

**Windows:**
```cmd
deploy.bat
```

3. **访问应用**
- 前端：http://localhost
- 后端API：http://localhost:8080/api
- MongoDB管理：http://localhost:8081

### 外部MongoDB部署方案

1. **配置环境变量**
```bash
cd docker
cp .env.external-mongo .env
```

2. **编辑配置文件**
修改 `.env` 文件中的MongoDB连接信息：
```env
MONGODB_HOST=your-mongodb-host
MONGODB_PORT=27017
MONGODB_DATABASE=logger_management
MONGODB_USERNAME=logger_user
MONGODB_PASSWORD=logger_password
```

3. **初始化数据库**
```bash
mongosh "**********************************************" < init-external-mongo.js
```

4. **运行部署脚本**

**Linux/macOS:**
```bash
chmod +x deploy-external-mongo.sh
./deploy-external-mongo.sh
```

**Windows:**
```cmd
deploy-external-mongo.bat
```

## 环境变量配置

### 完整部署环境变量

主要配置项（`.env.example`）：
```env
FRONTEND_PORT=80
BACKEND_PORT=8080
MONGODB_PORT=27017
MONGO_INITDB_ROOT_USERNAME=admin
MONGO_INITDB_ROOT_PASSWORD=password123
JWT_SECRET=your-secret-key
```

### 外部MongoDB环境变量

主要配置项（`.env.external-mongo`）：
```env
MONGODB_HOST=your-mongodb-host
MONGODB_PORT=27017
MONGODB_DATABASE=logger_management
MONGODB_USERNAME=logger_user
MONGODB_PASSWORD=logger_password
FRONTEND_PORT=80
BACKEND_PORT=8080
```

## 常用操作命令

### 完整部署命令

```bash
# 启动所有服务
docker-compose up -d

# 停止所有服务
docker-compose down

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f [service_name]

# 重启服务
docker-compose restart [service_name]
```

### 外部MongoDB部署命令

```bash
# 启动服务
docker-compose -f docker-compose-external-mongo.yml --env-file .env up -d

# 停止服务
docker-compose -f docker-compose-external-mongo.yml down

# 查看状态
docker-compose -f docker-compose-external-mongo.yml ps

# 查看日志
docker-compose -f docker-compose-external-mongo.yml logs -f [service_name]
```

## 生产环境建议

### 安全配置

1. **修改默认密码**
   - MongoDB管理员密码
   - JWT密钥
   - 应用数据库用户密码

2. **网络安全**
   - 使用防火墙限制端口访问
   - 配置HTTPS（建议使用反向代理）
   - 限制CORS来源域名

3. **数据备份**
   - 定期备份MongoDB数据
   - 配置日志轮转策略

### 性能优化

1. **资源限制**
   - 为容器设置内存和CPU限制
   - 配置合适的JVM堆内存大小

2. **监控配置**
   - 启用健康检查
   - 配置日志收集
   - 设置告警机制

## 故障排除

### 常见问题

1. **端口冲突**
   - 修改docker-compose.yml中的端口映射
   - 检查系统端口占用情况

2. **MongoDB连接失败**
   - 检查网络连接
   - 验证用户名密码
   - 确认防火墙设置

3. **容器启动失败**
   - 查看容器日志
   - 检查配置文件语法
   - 验证环境变量设置

### 日志查看

```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f backend
docker-compose logs -f frontend
docker-compose logs -f mongodb
```

## 技术支持

如遇到问题，请：
1. 查看相关日志文件
2. 检查配置文件设置
3. 验证网络连接
4. 确认系统资源充足

---

**版本**: 1.0.0  
**更新时间**: 2024-06-20  
**作者**: Logger Management System
