<script setup>
// 通用日志管理系统主应用
import { onMounted, watch, ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Monitor, Odometer, Document, Grid, User, ArrowDown, Setting, SwitchButton, Files, Close } from '@element-plus/icons-vue'
import { useAuthStore } from './stores/auth'
import { useTabStore } from './stores/tabs'
import MessageNotification from './components/MessageNotification.vue'
import { setNotificationInstance, MessageBox, Message } from './utils/message'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()
const tabStore = useTabStore()

// 消息通知组件引用
const messageNotificationRef = ref()

// 处理导航点击
const handleNavClick = (path, tabInfo) => {
  tabStore.addTab({
    path,
    ...tabInfo
  })
  router.push(path)
}

// 处理用户下拉菜单命令
const handleUserCommand = async (command) => {
  switch (command) {
    case 'profile':
      handleNavClick('/profile', {
        title: '个人资料',
        icon: 'User',
        component: 'Profile'
      })
      break
    case 'settings':
      handleNavClick('/settings', {
        title: '设置',
        icon: 'Setting',
        component: 'Settings'
      })
      break
    case 'logout':
      try {
        await MessageBox.confirm('确定要退出登录吗？', '确认退出')

        await authStore.logout()
        tabStore.closeAllTabs() // 清空所有tabs
        Message.success('已退出登录')
        router.push('/login')
      } catch (error) {
        if (error !== 'cancel') {
          Message.error('退出登录失败')
        }
      }
      break
  }
}

// 处理tab切换
const handleTabClick = (tab) => {
  const tabName = tab.paneName || tab.name
  tabStore.switchTab(tabName)
  router.push(tabName)
}

// 处理tab关闭
const handleTabClose = (tabName) => {
  tabStore.closeTab(tabName)
  // 如果关闭的是当前tab，路由会自动切换到新的activeTab
  if (tabStore.activeTab !== route.path) {
    router.push(tabStore.activeTab)
  }
}

// 监听路由变化，同步tab状态
watch(route, (newRoute) => {
  if (newRoute.path !== '/login' && authStore.isAuthenticated) {
    tabStore.addTabFromRoute(newRoute)
    tabStore.switchTab(newRoute.path)
  }
}, { immediate: true })

// 组件挂载时初始化认证状态和tabs
onMounted(async () => {
  if (authStore.token && !authStore.user) {
    try {
      await authStore.initAuth()
    } catch (error) {
      console.warn('初始化认证状态失败:', error.message)
    }
  }

  // 初始化tabs
  if (authStore.isAuthenticated && route.path !== '/login') {
    tabStore.initTabs(route)
  }

  // 设置消息通知实例
  if (messageNotificationRef.value) {
    setNotificationInstance(messageNotificationRef.value)
  }
})
</script>

<template>
  <div id="app">
    <!-- 顶部导航栏 - 只在非登录页面显示 -->
    <div class="app-header" v-if="$route.path !== '/login'">
      <div class="header-content">
        <div class="logo-section">
          <!-- 使用SVG LOGO替代文字 -->
          <img src="/logo.svg" alt="通用日志管理系统" class="app-logo" />
          <!-- 如果需要保留图标，可以取消注释下面这行 -->
          <!-- <el-icon size="24" color="white"><Monitor /></el-icon> -->
        </div>

        <div class="nav-buttons" v-if="authStore.isAuthenticated">
          <el-button
            :type="$route.path === '/dashboard' ? 'primary' : 'info'"
            @click="handleNavClick('/dashboard', { title: '仪表板', icon: 'Odometer', component: 'Dashboard' })"
            class="nav-btn"
          >
            <el-icon><Odometer /></el-icon>
            仪表板
          </el-button>
          <el-button
            :type="$route.path.startsWith('/logs') ? 'primary' : 'info'"
            @click="handleNavClick('/logs', { title: '日志管理', icon: 'Document', component: 'LogList' })"
            class="nav-btn"
          >
            <el-icon><Document /></el-icon>
            日志管理
          </el-button>
          <el-button
            v-if="authStore.isAdmin"
            :type="$route.path.startsWith('/applications') ? 'primary' : 'info'"
            @click="handleNavClick('/applications', { title: '应用管理', icon: 'Grid', component: 'ApplicationList' })"
            class="nav-btn"
          >
            <el-icon><Grid /></el-icon>
            应用管理
          </el-button>
          <el-button
            v-if="authStore.isAdmin"
            :type="$route.path.startsWith('/users') ? 'primary' : 'info'"
            @click="handleNavClick('/users', { title: '用户管理', icon: 'User', component: 'UserList' })"
            class="nav-btn"
          >
            <el-icon><User /></el-icon>
            用户管理
          </el-button>
          <el-button
            :type="$route.path.startsWith('/api-docs') ? 'primary' : 'info'"
            @click="handleNavClick('/api-docs', { title: 'API文档', icon: 'Files', component: 'ApiDocs' })"
            class="nav-btn"
          >
            <el-icon><Files /></el-icon>
            API文档
          </el-button>
        </div>

        <!-- 用户信息区域 -->
        <div class="user-area" v-if="authStore.isAuthenticated">
          <el-dropdown trigger="click" @command="handleUserCommand">
            <div class="user-info">
              <el-avatar :size="32" :src="authStore.user?.avatar">
                <el-icon><User /></el-icon>
              </el-avatar>
              <span class="username">{{ authStore.displayName }}</span>
              <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">
                  <el-icon><User /></el-icon>
                  个人资料
                </el-dropdown-item>
                <el-dropdown-item command="settings">
                  <el-icon><Setting /></el-icon>
                  设置
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <el-icon><SwitchButton /></el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="app-main" :class="{ 'login-page': $route.path === '/login' }">
      <!-- Tab页面导航 - 只在非登录页面且已认证时显示 -->
      <div class="tab-container" v-if="$route.path !== '/login' && authStore.isAuthenticated">
        <el-tabs
          v-model="tabStore.activeTab"
          type="card"
          @tab-click="handleTabClick"
          @tab-remove="handleTabClose"
          class="page-tabs"
        >
          <el-tab-pane
            v-for="tab in tabStore.tabs"
            :key="tab.name"
            :label="tab.title"
            :name="tab.name"
            :closable="tab.closable"
          >
            <template #label>
              <span class="tab-label">
                <el-icon><component :is="tab.icon" /></el-icon>
                <span>{{ tab.title }}</span>
              </span>
            </template>
          </el-tab-pane>
        </el-tabs>
      </div>

      <!-- 页面内容 -->
      <div class="page-content" :class="{ 'with-tabs': $route.path !== '/login' && authStore.isAuthenticated }">
        <router-view v-slot="{ Component }">
          <keep-alive>
            <component :is="Component" />
          </keep-alive>
        </router-view>
      </div>
    </div>

    <!-- 消息通知组件 -->
    <MessageNotification ref="messageNotificationRef" />
  </div>
</template>

<style>
/* CSS 变量在 variables.css 中定义 */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
}

#app {
  height: 100vh;
  background-color: #f5f7fa;
  display: flex;
  flex-direction: column;
}

.app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  height: var(--header-height);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--container-padding);
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
  min-width: 450px;
}

/* LOGO图片样式 */
.app-logo {
  height: 40px;
  max-width: 450px;
  object-fit: contain;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
  transition: transform 0.3s ease;
}

.app-logo:hover {
  transform: scale(1.05);
}

/* 保留原文字样式，以备需要时使用 */
.app-title {
  font-size: 22px;
  font-weight: 700;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.nav-buttons {
  display: flex;
  gap: 10px;
}

.nav-btn {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  color: white !important;
  transition: all 0.3s ease;
  padding: 8px 14px !important;
  font-size: 14px !important;
}

.nav-btn:hover {
  background: rgba(255, 255, 255, 0.2) !important;
  transform: translateY(-1px);
}

.nav-btn.el-button--primary {
  background: rgba(255, 255, 255, 0.25) !important;
  border-color: rgba(255, 255, 255, 0.4) !important;
}

/* 用户信息区域样式 */
.user-area {
  margin-left: 20px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  cursor: pointer;
  transition: all 0.3s ease;
  color: white;
}

.user-info:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.username {
  font-size: 14px;
  font-weight: 500;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dropdown-icon {
  font-size: 12px;
  transition: transform 0.3s ease;
}

.user-info:hover .dropdown-icon {
  transform: rotate(180deg);
}

.app-main {
  flex: 1;
  background-color: #f5f7fa;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.app-main.login-page {
  overflow: hidden;
  background-color: #f8fafc;
}

/* Tab容器样式 */
.tab-container {
  background: white;
  border-bottom: 1px solid var(--border-lighter);
  padding: 0 var(--container-padding);
  flex-shrink: 0;
}

.page-tabs {
  margin-bottom: 0;
}

.page-tabs .el-tabs__header {
  margin: 0;
  border-bottom: none;
}

.page-tabs .el-tabs__nav-wrap {
  padding: 8px 0;
}

.page-tabs .el-tabs__item {
  border: 1px solid var(--border-lighter);
  border-radius: 6px 6px 0 0;
  margin-right: 4px;
  padding: 0 16px;
  height: 36px;
  line-height: 34px;
  background: #f8f9fa;
  color: var(--text-regular);
  transition: all 0.3s ease;
}

.page-tabs .el-tabs__item:hover {
  background: #e9ecef;
  color: var(--text-primary);
}

.page-tabs .el-tabs__item.is-active {
  background: white;
  color: var(--primary-color);
  border-bottom-color: white;
  font-weight: 500;
}

.page-tabs .el-tabs__item .el-icon {
  margin-right: 6px;
}

.tab-label {
  display: flex;
  align-items: center;
  gap: 6px;
}

/* 页面内容样式 */
.page-content {
  flex: 1;
  overflow-y: auto;
  background: #f5f7fa;
}

.page-content.with-tabs {
  padding: var(--container-padding) 0;
}

.page-content:not(.with-tabs) {
  padding: var(--spacing-xl);
}

/* Tab响应式设计 */
@media (max-width: 768px) {
  .tab-container {
    padding: 0 var(--container-padding);
  }

  .page-tabs .el-tabs__item {
    padding: 0 12px;
    font-size: 13px;
    height: 32px;
    line-height: 30px;
  }

  .page-tabs .el-tabs__item .el-icon {
    margin-right: 4px;
  }

  .page-content.with-tabs {
    padding: var(--container-padding) 0;
  }
}

@media (max-width: 480px) {
  .page-tabs .el-tabs__item {
    padding: 0 8px;
    font-size: 12px;
    height: 30px;
    line-height: 28px;
  }

  .tab-label span {
    display: none;
  }

  .page-tabs .el-tabs__item .el-icon {
    margin-right: 0;
  }
}

/* 滚动条样式 - 只在非登录页面显示 */
.app-main:not(.login-page)::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.app-main:not(.login-page)::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.app-main:not(.login-page)::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.app-main:not(.login-page)::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 登录页面隐藏滚动条 */
.app-main.login-page::-webkit-scrollbar {
  display: none;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .header-content {
    max-width: var(--container-max-width);
    padding: 0 var(--container-padding);
  }
}

@media (max-width: 992px) {
  :root {
    --container-padding: 16px;
  }

  .header-content {
    padding: 0 var(--container-padding);
  }

  .nav-buttons {
    gap: 8px;
  }
}

@media (max-width: 768px) {
  :root {
    --container-padding: 15px;
  }

  .app-header {
    height: auto;
    min-height: var(--header-height);
  }

  .header-content {
    padding: 12px var(--container-padding);
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .logo-section {
    gap: 8px;
    min-width: auto;
    justify-content: center;
    padding: 8px 0;
    overflow: hidden;
  }

  /* 移动端LOGO样式 */
  .app-logo {
    height: 28px;
    max-width: 400px;
  }

  .app-title {
    font-size: 18px;
  }

  .nav-buttons {
    gap: 8px;
    flex-wrap: wrap;
    justify-content: center;
  }

  .nav-btn {
    padding: 8px 12px !important;
    font-size: 13px !important;
    flex: 1;
    min-width: 100px;
    max-width: 120px;
  }

  .nav-btn .el-icon {
    margin-right: 6px;
  }

  .user-area {
    margin-left: 0;
    margin-top: 8px;
    display: flex;
    justify-content: center;
  }

  .user-info {
    padding: 8px 16px;
  }

  .username {
    max-width: 150px;
  }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {
  :root {
    --container-padding: 12px;
  }

  .header-content {
    padding: 10px var(--container-padding);
  }

  /* 超小屏幕LOGO样式 */
  .app-logo {
    height: 24px;
    max-width: 350px;
  }

  .app-title {
    font-size: 16px;
  }

  .nav-buttons {
    gap: 6px;
  }

  .nav-btn {
    padding: 6px 10px !important;
    font-size: 12px !important;
    min-width: 85px;
    max-width: 100px;
  }
}

/* 全局移动端优化 */
@media (max-width: 768px) {
  /* 对话框移动端优化 */
  :deep(.el-dialog) {
    width: 95% !important;
    margin: 5vh auto !important;
  }

  :deep(.el-dialog__header) {
    padding: 16px 20px 12px !important;
  }

  :deep(.el-dialog__body) {
    padding: 12px 20px !important;
    max-height: 70vh;
    overflow-y: auto;
  }

  :deep(.el-dialog__footer) {
    padding: 12px 20px 16px !important;
  }

  /* 表单移动端优化 */
  :deep(.el-form-item__label) {
    font-size: 13px !important;
    line-height: 1.4 !important;
  }

  :deep(.el-input__wrapper) {
    font-size: 14px !important;
  }

  :deep(.el-button) {
    font-size: 13px !important;
  }

  /* 自定义对话框移动端优化 */
  :deep(.custom-message-box) {
    width: 90% !important;
    margin: 5vh auto !important;
  }
}
</style>
