@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: 通用日志管理系统 Docker 停止脚本 (Windows)
:: 作者: Logger Management System
:: 版本: 1.0.0

if "%1"=="--help" goto :show_help
if "%1"=="--remove-volumes" goto :stop_with_volumes

:stop_normal
echo [INFO] 停止所有服务...
docker-compose down
if errorlevel 1 (
    echo [ERROR] 停止服务失败
    pause
    exit /b 1
)
echo [SUCCESS] 服务已停止
goto :end

:stop_with_volumes
echo [WARNING] 将删除所有数据卷，这将清除所有数据！
set /p confirm="确定要继续吗？(y/N): "
if /i "!confirm!"=="y" (
    echo [INFO] 停止服务并删除数据卷...
    docker-compose down -v
    if errorlevel 1 (
        echo [ERROR] 停止服务失败
        pause
        exit /b 1
    )
    echo [SUCCESS] 服务已停止，数据卷已删除
) else (
    echo [INFO] 操作已取消
)
goto :end

:show_help
echo 用法: %0 [选项]
echo.
echo 选项:
echo   --remove-volumes    停止服务并删除所有数据卷（谨慎使用）
echo   --help             显示此帮助信息
echo.
echo 示例:
echo   %0                 # 停止服务但保留数据
echo   %0 --remove-volumes # 停止服务并删除所有数据
goto :end

:end
if not "%1"=="--help" pause
