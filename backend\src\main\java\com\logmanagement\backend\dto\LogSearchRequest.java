package com.logmanagement.backend.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.logmanagement.backend.entity.LogLevel;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 日志搜索请求DTO
 * 
 * <AUTHOR> Management System
 * @version 1.0.0
 */
public class LogSearchRequest {

    /**
     * 页码（从1开始）
     */
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码必须大于0")
    private Integer page = 1;

    /**
     * 每页大小
     */
    @NotNull(message = "每页大小不能为空")
    @Min(value = 1, message = "每页大小必须大于0")
    private Integer size = 20;

    /**
     * 排序字段
     */
    private String sort = "timestamp";

    /**
     * 排序方向
     */
    private String order = "desc";

    /**
     * 日志级别
     */
    private LogLevel level;

    /**
     * 日志来源
     */
    private String source;

    /**
     * 应用ID
     */
    private String applicationId;

    /**
     * 环境
     */
    private String environment;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 关键词搜索
     */
    private String keyword;

    /**
     * 线程名称
     */
    private String thread;

    /**
     * 应用ID列表（用于权限过滤）
     */
    private List<String> applicationIds;

    // 构造函数
    public LogSearchRequest() {
    }

    // Getter 和 Setter 方法
    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }

    public String getSort() {
        return sort;
    }

    public void setSort(String sort) {
        this.sort = sort;
    }

    public String getOrder() {
        return order;
    }

    public void setOrder(String order) {
        this.order = order;
    }

    public LogLevel getLevel() {
        return level;
    }

    public void setLevel(LogLevel level) {
        this.level = level;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }



    public String getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(String applicationId) {
        this.applicationId = applicationId;
    }

    public String getEnvironment() {
        return environment;
    }

    public void setEnvironment(String environment) {
        this.environment = environment;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getThread() {
        return thread;
    }

    public void setThread(String thread) {
        this.thread = thread;
    }

    public List<String> getApplicationIds() {
        return applicationIds;
    }

    public void setApplicationIds(List<String> applicationIds) {
        this.applicationIds = applicationIds;
    }

    @Override
    public String toString() {
        return "LogSearchRequest{" +
                "page=" + page +
                ", size=" + size +
                ", sort='" + sort + '\'' +
                ", order='" + order + '\'' +
                ", level=" + level +
                ", source='" + source + '\'' +
                ", applicationId='" + applicationId + '\'' +
                ", environment='" + environment + '\'' +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", keyword='" + keyword + '\'' +
                ", thread='" + thread + '\'' +
                ", applicationIds=" + applicationIds +
                '}';
    }
}
