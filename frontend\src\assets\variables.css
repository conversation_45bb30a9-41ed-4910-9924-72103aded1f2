/* 全局CSS变量定义 */
:root {
  /* 布局尺寸 */
  --container-max-width: 1600px;
  --container-padding: 20px;
  --header-height: 60px;
  
  /* 内容页面最大宽度 */
  --content-max-width: 1600px;
  --profile-max-width: 1200px;
  --settings-max-width: 1000px;
  
  /* 响应式断点 */
  --breakpoint-xl: 1200px;
  --breakpoint-lg: 992px;
  --breakpoint-md: 768px;
  --breakpoint-sm: 480px;
  
  /* 颜色变量 */
  --primary-color: #409eff;
  --success-color: #67c23a;
  --warning-color: #e6a23c;
  --danger-color: #f56c6c;
  --info-color: #909399;
  
  /* 背景色 */
  --bg-color: #f5f7fa;
  --card-bg-color: #ffffff;
  
  /* 文字颜色 */
  --text-primary: #303133;
  --text-regular: #606266;
  --text-secondary: #909399;
  --text-placeholder: #c0c4cc;
  
  /* 边框颜色 */
  --border-color: #dcdfe6;
  --border-light: #e4e7ed;
  --border-lighter: #ebeef5;
  --border-extra-light: #f2f6fc;
  
  /* 阴影 */
  --box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  --box-shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  
  /* 圆角 */
  --border-radius-small: 4px;
  --border-radius-base: 8px;
  --border-radius-large: 12px;
  
  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 20px;
  --spacing-xxl: 24px;
}

/* 响应式变量重定义 */
@media (max-width: 1200px) {
  :root {
    --container-padding: 20px;
  }
}

@media (max-width: 992px) {
  :root {
    --container-padding: 16px;
  }
}

@media (max-width: 768px) {
  :root {
    --container-padding: 15px;
    --spacing-xl: 15px;
    --spacing-xxl: 20px;
  }
}

@media (max-width: 480px) {
  :root {
    --container-padding: 12px;
    --spacing-xl: 12px;
    --spacing-xxl: 16px;
  }
}

/* 通用容器类 */
.container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--container-padding);
}

.content-container {
  max-width: var(--content-max-width);
  margin: 0 auto;
  padding: var(--spacing-xl);
  background-color: var(--bg-color);
  min-height: calc(100vh - var(--header-height));
}

.profile-container {
  max-width: var(--profile-max-width);
  margin: 0 auto;
  padding: var(--spacing-xl);
}

.settings-container {
  max-width: var(--settings-max-width);
  margin: 0 auto;
  padding: var(--spacing-xl);
}

/* 响应式容器调整 */
@media (max-width: 768px) {
  .content-container {
    padding: var(--spacing-xl);
    min-height: calc(100vh - 120px); /* 调整移动端导航栏高度 */
  }
  
  .profile-container,
  .settings-container {
    padding: var(--spacing-xl);
  }
}

@media (max-width: 480px) {
  .content-container,
  .profile-container,
  .settings-container {
    padding: var(--spacing-xl);
  }
}
