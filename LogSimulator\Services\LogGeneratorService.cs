using LogSimulator.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using LogLevel = LogSimulator.Models.LogLevel;

namespace LogSimulator.Services;

public class LogGeneratorService
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<LogGeneratorService> _logger;
    private readonly Random _random = new();

    private readonly string[] _userIds = { "user001", "user002", "user003", "user004", "user005" };
    private readonly string[] _sources = { "UserController", "OrderController", "PaymentController", "AuthController", "ProductController" };
    private readonly string[] _ipAddresses = { "*************", "*************", "*********", "***********", "***********" };
    private readonly string[] _userAgents = {
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36"
    };

    private readonly Dictionary<string, string[]> _messageTemplates = new()
    {
        ["INFO"] = new[]
        {
            "用户 {userId} 成功登录系统",
            "订单 {orderId} 创建成功",
            "用户 {userId} 查看了产品列表",
            "系统健康检查通过",
            "缓存刷新完成",
            "定时任务执行成功",
            "用户 {userId} 更新了个人信息",
            "文件上传完成: {fileName}",
            "邮件发送成功: {email}",
            "数据备份完成"
        },
        ["WARN"] = new[]
        {
            "用户 {userId} 登录失败，密码错误",
            "API调用频率过高，来源IP: {ipAddress}",
            "数据库连接池使用率达到80%",
            "磁盘空间不足，剩余: {diskSpace}GB",
            "缓存命中率较低: {hitRate}%",
            "第三方服务响应缓慢: {serviceName}",
            "用户 {userId} 尝试访问未授权资源",
            "内存使用率较高: {memoryUsage}%",
            "网络延迟较高: {latency}ms"
        },
        ["ERROR"] = new[]
        {
            "数据库连接失败: {error}",
            "支付接口调用异常: {error}",
            "文件处理失败: {fileName}",
            "邮件发送失败: {email}",
            "用户认证服务异常",
            "订单处理失败: {orderId}",
            "系统内部错误: {error}",
            "第三方API调用失败: {apiName}",
            "数据同步异常: {error}"
        },
        ["DEBUG"] = new[]
        {
            "执行SQL查询: {sql}",
            "缓存键值: {cacheKey}",
            "方法调用: {methodName}",
            "参数验证: {parameters}",
            "响应时间: {responseTime}ms",
            "内存分配: {memorySize}MB",
            "线程ID: {threadId}",
            "会话状态: {sessionState}",
            "配置加载: {configKey}"
        }
    };

    public LogGeneratorService(IConfiguration configuration, ILogger<LogGeneratorService> logger)
    {
        _configuration = configuration;
        _logger = logger;
    }

    public LogEntry GenerateLogEntry()
    {
        var level = GetRandomLogLevel();
        var applicationId = _configuration["LogManagement:AppId"] ?? "unknown-app";
        
        var logEntry = new LogEntry
        {
            ApplicationId = applicationId,
            Level = level,
            Message = GenerateMessage(level),
            Timestamp = DateTime.Now, // 使用本地时间而不是UTC时间
            Source = GetRandomSource(),
            Environment = "development",
            ExtendProperties = GenerateExtraData(level),
            Thread = $"Thread-{Environment.CurrentManagedThreadId}"
        };

        if (level == LogLevel.ERROR)
        {
            logEntry.Exception = GenerateExceptionString();
        }

        return logEntry;
    }

    public List<LogEntry> GenerateLogEntries(int count)
    {
        var logs = new List<LogEntry>();
        for (int i = 0; i < count; i++)
        {
            logs.Add(GenerateLogEntry());
            
            if (_configuration.GetValue<bool>("Simulation:EnableRandomDelay"))
            {
                var maxDelay = _configuration.GetValue<int>("Simulation:MaxRandomDelayMs");
                Thread.Sleep(_random.Next(0, maxDelay));
            }
        }
        return logs;
    }

    private LogLevel GetRandomLogLevel()
    {
        var weights = _configuration.GetSection("Simulation:LogLevelWeights");
        var totalWeight = 0;
        var levelWeights = new Dictionary<LogLevel, int>();

        foreach (var level in _configuration.GetSection("Simulation:LogLevels").Get<string[]>() ?? Array.Empty<string>())
        {
            if (Enum.TryParse<LogLevel>(level, out var logLevel))
            {
                var weight = weights.GetValue<int>(level);
                levelWeights[logLevel] = weight;
                totalWeight += weight;
            }
        }

        var randomValue = _random.Next(0, totalWeight);
        var currentWeight = 0;

        foreach (var kvp in levelWeights)
        {
            currentWeight += kvp.Value;
            if (randomValue < currentWeight)
            {
                return kvp.Key;
            }
        }

        return LogLevel.INFO;
    }

    private string GenerateMessage(LogLevel level)
    {
        var levelString = level.ToString();
        if (!_messageTemplates.ContainsKey(levelString))
            return $"Generated {level} message at {DateTime.Now}";

        var templates = _messageTemplates[levelString];
        var template = templates[_random.Next(templates.Length)];

        return template
            .Replace("{userId}", GetRandomUserId())
            .Replace("{orderId}", $"ORD{_random.Next(10000, 99999)}")
            .Replace("{fileName}", $"file_{_random.Next(1000, 9999)}.txt")
            .Replace("{email}", $"user{_random.Next(1, 100)}@example.com")
            .Replace("{ipAddress}", GetRandomIpAddress())
            .Replace("{diskSpace}", _random.Next(1, 50).ToString())
            .Replace("{hitRate}", _random.Next(30, 95).ToString())
            .Replace("{serviceName}", $"Service{_random.Next(1, 5)}")
            .Replace("{memoryUsage}", _random.Next(60, 95).ToString())
            .Replace("{latency}", _random.Next(100, 2000).ToString())
            .Replace("{error}", $"Error_{_random.Next(1000, 9999)}")
            .Replace("{apiName}", $"API_{_random.Next(1, 10)}")
            .Replace("{sql}", $"SELECT * FROM table_{_random.Next(1, 5)}")
            .Replace("{cacheKey}", $"cache_{_random.Next(1000, 9999)}")
            .Replace("{methodName}", $"Method{_random.Next(1, 20)}")
            .Replace("{parameters}", $"param1=value{_random.Next(1, 100)}")
            .Replace("{responseTime}", _random.Next(10, 500).ToString())
            .Replace("{memorySize}", _random.Next(1, 100).ToString())
            .Replace("{threadId}", _random.Next(1, 50).ToString())
            .Replace("{sessionState}", _random.Next(0, 2) == 0 ? "Active" : "Inactive")
            .Replace("{configKey}", $"config.key{_random.Next(1, 10)}");
    }

    private string GetRandomSource() => _sources[_random.Next(_sources.Length)];
    private string GetRandomUserId() => _userIds[_random.Next(_userIds.Length)];
    private string GetRandomIpAddress() => _ipAddresses[_random.Next(_ipAddresses.Length)];
    private string GetRandomUserAgent() => _userAgents[_random.Next(_userAgents.Length)];

    private Dictionary<string, object> GenerateExtraData(LogLevel level)
    {
        var extraData = new Dictionary<string, object>
        {
            ["environment"] = "development",
            ["version"] = "1.0.0",
            ["processId"] = Environment.ProcessId,
            ["machineName"] = Environment.MachineName,
            ["threadId"] = Environment.CurrentManagedThreadId
        };

        switch (level)
        {
            case LogLevel.ERROR:
                extraData["severity"] = "High";
                extraData["requiresAttention"] = true;
                break;
            case LogLevel.WARN:
                extraData["severity"] = "Medium";
                extraData["monitoringAlert"] = true;
                break;
            case LogLevel.INFO:
                extraData["category"] = "Business";
                break;
            case LogLevel.DEBUG:
                extraData["category"] = "Technical";
                extraData["debugLevel"] = _random.Next(1, 5);
                break;
        }

        return extraData;
    }

    private string GenerateExceptionString()
    {
        var exceptionTypes = new[]
        {
            "System.ArgumentNullException",
            "System.InvalidOperationException",
            "System.TimeoutException",
            "System.Data.SqlException",
            "System.Net.HttpRequestException",
            "System.IO.FileNotFoundException",
            "System.UnauthorizedAccessException"
        };

        var exceptionMessages = new[]
        {
            "参数不能为空",
            "操作无效，对象状态不正确",
            "操作超时",
            "数据库连接失败",
            "HTTP请求失败",
            "找不到指定文件",
            "访问被拒绝"
        };

        var exceptionType = exceptionTypes[_random.Next(exceptionTypes.Length)];
        var exceptionMessage = exceptionMessages[_random.Next(exceptionMessages.Length)];

        return $"{exceptionType}: {exceptionMessage}\n{GenerateStackTrace()}";
    }

    private string GenerateStackTrace()
    {
        return $@"   at LogSimulator.Services.{GetRandomSource()}.Method{_random.Next(1, 10)}() in C:\Source\LogSimulator\Services\{GetRandomSource()}.cs:line {_random.Next(10, 200)}
   at LogSimulator.Controllers.{GetRandomSource()}.Action{_random.Next(1, 5)}() in C:\Source\LogSimulator\Controllers\{GetRandomSource()}.cs:line {_random.Next(10, 100)}
   at System.Threading.Tasks.Task.Execute()";
    }


}
