# 详细测试属性搜索功能

Write-Host "=== 详细测试属性搜索功能 ===" -ForegroundColor Green

# 1. 获取认证token
Write-Host "1. 获取认证token..." -ForegroundColor Yellow
$loginBody = '{"username":"admin","password":"admin123"}'

try {
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/auth/login" -Method Post -ContentType "application/json" -Body $loginBody
    $token = $loginResponse.data.token
    Write-Host "✓ 登录成功" -ForegroundColor Green
} catch {
    Write-Host "✗ 登录失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 2. 先获取一些日志数据看看结构
Write-Host "`n2. 获取日志数据查看结构..." -ForegroundColor Yellow
try {
    $uri = "http://localhost:8080/api/logs?page=0&size=3"
    $response = Invoke-RestMethod -Uri $uri -Method Get -Headers @{Authorization="Bearer $token"}
    $total = if ($response.data.totalElements) { $response.data.totalElements } else { $response.data.total }
    Write-Host "✓ 总共有 $total 条日志" -ForegroundColor Green
    
    $logs = if ($response.data.content) { $response.data.content } else { $response.data }
    if ($logs.Count -gt 0) {
        Write-Host "  第一条日志的extendProperties:" -ForegroundColor Cyan
        $firstLog = $logs[0]
        if ($firstLog.extendProperties) {
            Write-Host "    $($firstLog.extendProperties | ConvertTo-Json -Compress)" -ForegroundColor White
        } else {
            Write-Host "    extendProperties 字段为空" -ForegroundColor Red
        }
    }
} catch {
    Write-Host "✗ 获取日志失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 3. 测试搜索 "1.0.0"
Write-Host "`n3. 测试搜索版本号 '1.0.0'..." -ForegroundColor Yellow
try {
    $uri = "http://localhost:8080/api/logs?page=0&size=5&propertySearch=1.0.0&propertyType=extendProperties"
    $response = Invoke-RestMethod -Uri $uri -Method Get -Headers @{Authorization="Bearer $token"}
    $total = if ($response.data.totalElements) { $response.data.totalElements } else { $response.data.total }
    Write-Host "✓ 搜索结果: 找到 $total 条匹配的日志" -ForegroundColor Green
    Write-Host "  完整响应: $($response | ConvertTo-Json -Depth 2 -Compress)" -ForegroundColor Gray
} catch {
    Write-Host "✗ 搜索失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 4. 测试搜索 "24468"
Write-Host "`n4. 测试搜索进程ID '24468'..." -ForegroundColor Yellow
try {
    $uri = "http://localhost:8080/api/logs?page=0&size=5&propertySearch=24468&propertyType=extendProperties"
    $response = Invoke-RestMethod -Uri $uri -Method Get -Headers @{Authorization="Bearer $token"}
    $total = if ($response.data.totalElements) { $response.data.totalElements } else { $response.data.total }
    Write-Host "✓ 搜索结果: 找到 $total 条匹配的日志" -ForegroundColor Green
    Write-Host "  完整响应: $($response | ConvertTo-Json -Depth 2 -Compress)" -ForegroundColor Gray
} catch {
    Write-Host "✗ 搜索失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 5. 测试搜索 "development"
Write-Host "`n5. 测试搜索环境 'development'..." -ForegroundColor Yellow
try {
    $uri = "http://localhost:8080/api/logs?page=0&size=5&propertySearch=development&propertyType=extendProperties"
    $response = Invoke-RestMethod -Uri $uri -Method Get -Headers @{Authorization="Bearer $token"}
    $total = if ($response.data.totalElements) { $response.data.totalElements } else { $response.data.total }
    Write-Host "✓ 搜索结果: 找到 $total 条匹配的日志" -ForegroundColor Green
    Write-Host "  完整响应: $($response | ConvertTo-Json -Depth 2 -Compress)" -ForegroundColor Gray
} catch {
    Write-Host "✗ 搜索失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
