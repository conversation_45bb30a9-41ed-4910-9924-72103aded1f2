#!/usr/bin/env python3
"""
简单的HTTP服务器，用于托管测试页面
运行方式: python start_test_server.py
访问地址: http://localhost:8000/test_simple_property_search.html
"""

import http.server
import socketserver
import os
import sys

# 设置端口
PORT = 8000

# 切换到项目根目录
os.chdir(os.path.dirname(os.path.abspath(__file__)))

class MyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        # 添加CORS头
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', '*')
        super().end_headers()

    def do_OPTIONS(self):
        # 处理预检请求
        self.send_response(200)
        self.end_headers()

if __name__ == "__main__":
    try:
        with socketserver.TCPServer(("", PORT), MyHTTPRequestHandler) as httpd:
            print(f"测试服务器启动成功！")
            print(f"访问地址: http://localhost:{PORT}/test_simple_property_search.html")
            print(f"按 Ctrl+C 停止服务器")
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n服务器已停止")
        sys.exit(0)
    except Exception as e:
        print(f"启动服务器失败: {e}")
        sys.exit(1)
