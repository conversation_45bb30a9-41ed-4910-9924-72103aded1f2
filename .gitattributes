# 设置默认行为，以防人们没有设置core.autocrlf
* text=auto

# 明确声明你希望始终被标准化并在检出时转换为本地行结束符的文件
*.java text
*.js text
*.vue text
*.json text
*.yml text
*.yaml text
*.xml text
*.properties text
*.md text
*.txt text
*.sql text

# 声明你希望始终使用LF行结束符的文件
*.sh text eol=lf

# 表示二进制文件，不应该被修改
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.ico binary
*.jar binary
*.war binary
*.ear binary
*.zip binary
*.tar.gz binary
*.pdf binary

# 设置语言统计
*.js linguist-language=JavaScript
*.vue linguist-language=Vue
*.java linguist-language=Java
