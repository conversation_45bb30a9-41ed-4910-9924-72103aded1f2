<template>
  <div class="user-list">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>用户管理</h1>
      <el-button type="primary" @click="showCreateDialog = true">
        <el-icon><Plus /></el-icon>
        创建用户
      </el-button>
    </div>

    <!-- 搜索表单 -->
    <div class="search-section">
      <el-form :model="searchForm" :inline="true" class="search-form">
        <el-form-item label="用户名">
          <el-input 
            v-model="searchForm.username" 
            placeholder="搜索用户名"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        
        <el-form-item label="角色">
          <el-select v-model="searchForm.role" placeholder="用户角色" clearable style="width: 150px">
            <el-option label="超级管理员" value="SUPER_ADMIN" />
            <el-option label="管理员" value="ADMIN" />
            <el-option label="开发者" value="DEVELOPER" />
            <el-option label="观察者" value="VIEWER" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="用户状态" clearable style="width: 120px">
            <el-option label="激活" value="ACTIVE" />
            <el-option label="禁用" value="DISABLED" />
            <el-option label="锁定" value="LOCKED" />
            <el-option label="待激活" value="PENDING" />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <div class="search-buttons">
            <el-button type="primary" @click="handleSearch" :loading="loading">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </div>
        </el-form-item>
      </el-form>
    </div>

    <!-- 用户表格 -->
    <div class="table-section">
      <div class="table-header">
        <h2>用户列表</h2>
        <div class="header-actions">
          <el-button 
            type="danger" 
            size="small"
            :disabled="!selectedUsers.length"
            @click="handleBatchDelete"
          >
            <el-icon><Delete /></el-icon>
            批量删除
          </el-button>
          <el-button size="small" @click="handleRefresh">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>
      
      <el-table 
        :data="users" 
        v-loading="loading"
        @selection-change="handleSelectionChange"
        stripe
        border
        style="width: 100%"
        :empty-text="loading ? '加载中...' : '暂无数据'"
      >
        <el-table-column type="selection" width="55" align="center" :selectable="isUserSelectable" />
        
        <el-table-column prop="username" label="用户名" width="120" />
        
        <el-table-column prop="realName" label="真实姓名" width="120" />
        
        <el-table-column prop="email" label="邮箱" width="200" show-overflow-tooltip />
        
        <el-table-column prop="role" label="角色" width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="getRoleTagType(row.role)" size="small">
              {{ getRoleText(row.role) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="authorizedAppCount" label="授权应用" width="100" align="center">
          <template #default="{ row }">
            <el-button
              link
              size="small"
              @click="handleViewAuthorizedApps(row)"
              :disabled="!row.authorizedAppIds || row.authorizedAppIds.length === 0"
            >
              {{ row.authorizedAppIds ? row.authorizedAppIds.length : 0 }}
            </el-button>
          </template>
        </el-table-column>
        
        <el-table-column prop="lastLoginTime" label="最后登录" width="200" class-name="time-column">
          <template #default="{ row }">
            <span v-if="row.lastLoginTime" style="color: #606266; font-size: 12px;">
              {{ formatTime(row.lastLoginTime) }}
            </span>
            <span v-else style="color: #c0c4cc;">从未登录</span>
          </template>
        </el-table-column>

        <el-table-column prop="createdAt" label="创建时间" width="200" class-name="time-column">
          <template #default="{ row }">
            <span style="color: #606266; font-size: 12px;">
              {{ formatTime(row.createdAt) }}
            </span>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="280" fixed="right" align="center">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button
                type="primary"
                size="small"
                @click="handleEdit(row)"
                :disabled="row.role === 'SUPER_ADMIN'"
              >
                <el-icon><Edit /></el-icon>
                <span>编辑</span>
              </el-button>
              <el-tooltip
                content="为用户授权应用的权限"
                placement="top"
                :disabled="row.role === 'SUPER_ADMIN'"
              >
                <el-button
                  type="warning"
                  size="small"
                  @click="handleAuthorize(row)"
                  :disabled="row.role === 'SUPER_ADMIN'"
                >
                  <el-icon><Key /></el-icon>
                  <span>授权</span>
                </el-button>
              </el-tooltip>
              <el-button
                :type="row.status === 'ACTIVE' ? 'warning' : 'success'"
                size="small"
                @click="handleToggleStatus(row)"
                :disabled="row.role === 'SUPER_ADMIN'"
              >
                <el-icon><SwitchButton /></el-icon>
                <span>{{ row.status === 'ACTIVE' ? '禁用' : '启用' }}</span>
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="handleDelete(row)"
                :disabled="row.role === 'SUPER_ADMIN'"
              >
                <el-icon><Delete /></el-icon>
                <span>删除</span>
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="getPageSizeOptions()"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 创建用户对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="创建用户"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="createForm" :rules="createRules" ref="createFormRef" label-width="100px">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="createForm.username" placeholder="请输入用户名" />
        </el-form-item>
        
        <el-form-item label="密码" prop="password">
          <el-input 
            v-model="createForm.password" 
            type="password" 
            placeholder="请输入密码"
            show-password
          />
        </el-form-item>
        
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="createForm.email" placeholder="请输入邮箱" />
        </el-form-item>
        
        <el-form-item label="真实姓名" prop="realName">
          <el-input v-model="createForm.realName" placeholder="请输入真实姓名" />
        </el-form-item>
        
        <el-form-item label="用户角色" prop="role">
          <el-select v-model="createForm.role" placeholder="选择用户角色" style="width: 100%">
            <el-option label="超级管理员" value="SUPER_ADMIN" />
            <el-option label="管理员" value="ADMIN" />
            <el-option label="开发者" value="DEVELOPER" />
            <el-option label="观察者" value="VIEWER" />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showCreateDialog = false">取消</el-button>
          <el-button type="primary" @click="handleCreate" :loading="createLoading">
            创建
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 编辑用户对话框 -->
    <el-dialog
      v-model="showEditDialog"
      title="编辑用户"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form :model="editForm" :rules="editRules" ref="editFormRef" label-width="100px">
        <el-form-item label="用户名">
          <el-input v-model="editForm.username" disabled />
        </el-form-item>

        <el-form-item label="邮箱" prop="email">
          <el-input v-model="editForm.email" placeholder="请输入邮箱" />
        </el-form-item>

        <el-form-item label="真实姓名" prop="realName">
          <el-input v-model="editForm.realName" placeholder="请输入真实姓名" />
        </el-form-item>

        <el-form-item label="新密码" prop="password">
          <el-input
            v-model="editForm.password"
            type="password"
            placeholder="留空则不修改密码"
            show-password
            clearable
          />
          <div style="color: #909399; font-size: 12px; margin-top: 4px;">
            留空则不修改密码，如需修改请输入新密码
          </div>
        </el-form-item>

        <el-form-item label="用户角色" prop="role">
          <el-select v-model="editForm.role" placeholder="选择用户角色" style="width: 100%">
            <el-option label="超级管理员" value="SUPER_ADMIN" />
            <el-option label="开发者" value="DEVELOPER" />
            <el-option label="观察者" value="VIEWER" />
          </el-select>
        </el-form-item>

        <el-form-item label="用户状态" prop="status">
          <el-select v-model="editForm.status" placeholder="选择用户状态" style="width: 100%">
            <el-option label="激活" value="ACTIVE" />
            <el-option label="禁用" value="DISABLED" />
          </el-select>
        </el-form-item>

        <el-form-item label="授权应用">
          <el-select
            v-model="editForm.authorizedAppIds"
            multiple
            placeholder="选择授权应用"
            style="width: 100%"
            clearable
          >
            <el-option
              v-for="app in availableApps"
              :key="app.id"
              :label="`${app.name} (${app.environment})`"
              :value="app.id"
            />
          </el-select>
          <div style="color: #909399; font-size: 12px; margin-top: 4px;">
            选择用户可以访问的应用，超级管理员默认拥有所有应用权限
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showEditDialog = false">取消</el-button>
          <el-button type="primary" @click="handleUpdate" :loading="editLoading">
            保存修改
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 用户授权对话框 -->
    <el-dialog
      v-model="showAuthorizeDialog"
      width="700px"
      :close-on-click-modal="false"
      class="authorize-dialog"
      align-center
    >
      <template #header>
        <div class="authorize-header">
          <div class="header-icon">
            <el-icon size="24" color="#409eff">
              <Key />
            </el-icon>
          </div>
          <div class="header-content">
            <h3>用户授权管理</h3>
            <p>为用户分配应用访问权限</p>
          </div>
        </div>
      </template>

      <div class="authorize-content">
        <!-- 用户信息卡片 -->
        <div class="user-info-card">
          <div class="user-avatar">
            <el-avatar :size="48" :src="currentUser.avatar">
              <el-icon size="24"><User /></el-icon>
            </el-avatar>
          </div>
          <div class="user-details">
            <h4 class="user-name">{{ currentUser.realName || currentUser.username }}</h4>
            <div class="user-meta">
              <el-tag :type="getRoleTagType(currentUser.role)" size="small">
                {{ getRoleText(currentUser.role) }}
              </el-tag>
              <span class="user-username">@{{ currentUser.username }}</span>
            </div>
            <div class="user-email">{{ currentUser.email }}</div>
          </div>
          <div class="user-status">
            <el-tag :type="getStatusTagType(currentUser.status)" size="small">
              {{ getStatusText(currentUser.status) }}
            </el-tag>
          </div>
        </div>

        <!-- 应用授权区域 -->
        <div class="app-authorization">
          <div class="section-header">
            <h4>
              <el-icon><Grid /></el-icon>
              应用权限分配
            </h4>
            <div class="section-subtitle">
              选择用户可以访问的应用系统
            </div>
          </div>

          <div v-if="availableApps.length === 0" class="empty-state">
            <el-icon size="48" color="#c0c4cc"><Document /></el-icon>
            <p>暂无可用应用</p>
            <span>请先创建应用后再进行授权</span>
          </div>

          <div v-else class="app-list">
            <el-checkbox-group v-model="authorizedAppIds" class="app-checkbox-group">
              <div
                v-for="app in availableApps"
                :key="app.id"
                class="app-item"
                :class="{ 'app-item-checked': authorizedAppIds.includes(app.id) }"
              >
                <el-checkbox :value="app.id" class="app-checkbox">
                  <div class="app-card">
                    <div class="app-content">
                      <div class="app-icon">
                        <el-icon size="20" color="#409eff">
                          <Monitor />
                        </el-icon>
                      </div>
                      <span class="app-name">{{ app.name }}</span>
                      <el-tag
                        size="small"
                        :type="getEnvironmentTagType(app.environment)"
                        class="env-tag"
                        v-if="app.environment && app.environment !== 'undefined'"
                      >
                        {{ getEnvironmentText(app.environment) }}
                      </el-tag>
                      <span class="app-version" v-if="app.version && app.version !== 'undefined'">
                        v{{ app.version }}
                      </span>
                    </div>
                  </div>
                </el-checkbox>
              </div>
            </el-checkbox-group>
          </div>

          <!-- 权限说明 -->
          <div class="permission-note">
            <el-alert
              type="info"
              :closable="false"
              show-icon
            >
              <template #title>
                <span>权限说明</span>
              </template>
              <div class="note-content">
                <p>• 超级管理员默认拥有所有应用的访问权限</p>
                <p>• 普通用户只能访问被授权的应用</p>
                <p>• 权限变更后需要用户重新登录才能生效</p>
              </div>
            </el-alert>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="authorize-footer">
          <el-button @click="showAuthorizeDialog = false" size="large">
            取消
          </el-button>
          <el-button
            type="primary"
            @click="handleSaveAuthorization"
            :loading="authorizeLoading"
            size="large"
          >
            <el-icon v-if="!authorizeLoading"><Check /></el-icon>
            {{ authorizeLoading ? '保存中...' : '保存授权' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import {
  Plus, Search, Refresh, Delete, Edit, Key, SwitchButton, User, Grid, Document, Monitor, Check
} from '@element-plus/icons-vue'
import { userApi } from '../api/users'
import { applicationApi } from '../api/applications'
import { useAuthStore } from '../stores/auth'
import { useConfigStore } from '../stores/config'
import { formatTime } from '../utils/dateTime'
import { Message, MessageBox } from '../utils/message'

const authStore = useAuthStore()
const configStore = useConfigStore()

// 响应式数据
const loading = ref(false)
const createLoading = ref(false)
const editLoading = ref(false)
const authorizeLoading = ref(false)
const users = ref([])
const selectedUsers = ref([])
const availableApps = ref([])
const showCreateDialog = ref(false)
const showEditDialog = ref(false)
const showAuthorizeDialog = ref(false)
const createFormRef = ref()
const editFormRef = ref()
const currentUser = ref({})
const authorizedAppIds = ref([])

// 搜索表单
const searchForm = reactive({
  username: '',
  role: '',
  status: ''
})

// 分页信息
const pagination = reactive({
  page: 1,
  size: configStore.getDefaultPageSize(),
  total: 0
})

// 创建表单
const createForm = reactive({
  username: '',
  password: '',
  email: '',
  realName: '',
  role: ''
})

// 编辑表单
const editForm = reactive({
  id: '',
  username: '',
  email: '',
  realName: '',
  password: '',
  role: '',
  status: '',
  authorizedAppIds: []
})

// 表单验证规则
const createRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择用户角色', trigger: 'change' }
  ]
}

// 编辑表单验证规则
const editRules = {
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' },
    { min: 2, max: 50, message: '真实姓名长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  password: [
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择用户角色', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择用户状态', trigger: 'change' }
  ]
}

// 获取页面大小选项
const getPageSizeOptions = () => {
  const maxSize = configStore.getMaxPageSize()
  const options = [10, 20, 50]
  if (maxSize >= 100) {
    options.push(100)
  }
  if (maxSize > 100) {
    options.push(maxSize)
  }
  return options
}

// 获取角色标签类型
const getRoleTagType = (role) => {
  const typeMap = {
    SUPER_ADMIN: 'danger',
    ADMIN: 'warning',
    DEVELOPER: 'primary',
    VIEWER: 'info'
  }
  return typeMap[role] || 'info'
}

// 获取角色文本
const getRoleText = (role) => {
  const textMap = {
    SUPER_ADMIN: '超级管理员',
    ADMIN: '管理员',
    DEVELOPER: '开发者',
    VIEWER: '观察者'
  }
  return textMap[role] || role
}

// 获取状态标签类型
const getStatusTagType = (status) => {
  const typeMap = {
    ACTIVE: 'success',
    DISABLED: 'danger',
    LOCKED: 'warning',
    PENDING: 'info'
  }
  return typeMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const textMap = {
    ACTIVE: '激活',
    DISABLED: '禁用',
    LOCKED: '锁定',
    PENDING: '待激活'
  }
  return textMap[status] || status
}



// 获取应用名称
const getAppName = (appId) => {
  const app = availableApps.value.find(app => app.id === appId)
  return app ? `${app.name} (${app.environment})` : appId
}

// 获取环境标签类型
const getEnvironmentTagType = (environment) => {
  const typeMap = {
    'prod': 'danger',
    'production': 'danger',
    'staging': 'warning',
    'test': 'info',
    'dev': 'success',
    'development': 'success'
  }
  return typeMap[environment?.toLowerCase()] || 'info'
}

// 获取环境文本
const getEnvironmentText = (environment) => {
  const textMap = {
    'prod': '生产环境',
    'production': '生产环境',
    'staging': '模拟环境',
    'test': '测试环境',
    'dev': '开发环境',
    'development': '开发环境'
  }
  return textMap[environment?.toLowerCase()] || environment
}

// 搜索
const handleSearch = async () => {
  pagination.page = 1
  await loadUsers()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    username: '',
    role: '',
    status: ''
  })
  pagination.page = 1
  loadUsers()
}

// 刷新
const handleRefresh = () => {
  loadUsers()
}

// 判断用户是否可以被选中（超级管理员不允许选中）
const isUserSelectable = (row) => {
  return row.role !== 'SUPER_ADMIN'
}

// 选择变化
const handleSelectionChange = (selection) => {
  selectedUsers.value = selection
}

// 编辑用户
const handleEdit = (user) => {
  // 检查是否为超级管理员
  if (user.role === 'SUPER_ADMIN') {
    Message.warning('超级管理员用户不允许编辑')
    return
  }

  // 填充编辑表单
  Object.assign(editForm, {
    id: user.id,
    username: user.username,
    email: user.email,
    realName: user.realName,
    password: '', // 密码留空，表示不修改
    role: user.role,
    status: user.status,
    authorizedAppIds: user.authorizedAppIds || []
  })

  showEditDialog.value = true
}

// 用户授权
const handleAuthorize = async (user) => {
  // 检查是否为超级管理员
  if (user.role === 'SUPER_ADMIN') {
    Message.warning('超级管理员默认拥有所有应用权限，无需单独授权')
    return
  }

  currentUser.value = user
  authorizedAppIds.value = user.authorizedAppIds || []

  // 加载可用应用列表
  await loadAvailableApps()

  showAuthorizeDialog.value = true
}

// 查看授权应用
const handleViewAuthorizedApps = (user) => {
  Message.info(`用户 ${user.username} 已授权 ${user.authorizedAppIds?.length || 0} 个应用`)
}

// 切换用户状态
const handleToggleStatus = async (user) => {
  // 检查是否为超级管理员
  if (user.role === 'SUPER_ADMIN') {
    Message.warning('超级管理员用户不允许禁用或启用')
    return
  }

  try {
    const action = user.status === 'ACTIVE' ? '禁用' : '启用'
    await MessageBox.confirm(`确定要${action}用户 "${user.username}" 吗？`, `确认${action}`)

    let response
    try {
      // 尝试使用真实API
      response = await userApi.toggleUserStatus(user.id)
    } catch (error) {
      console.error('切换用户状态失败:', error)
      Message.error('切换用户状态失败，请检查后端服务是否正常运行')
      return
    }

    if (response.code === 200) {
      Message.success(response.message || `用户${action}成功`)
      await loadUsers()
    } else {
      Message.error(response.message || `${action}失败`)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('切换用户状态失败:', error)
      Message.error('操作失败')
    }
  }
}

// 删除用户
const handleDelete = async (user) => {
  // 检查是否为超级管理员
  if (user.role === 'SUPER_ADMIN') {
    Message.warning('超级管理员用户不允许删除')
    return
  }

  try {
    await MessageBox.delete('确定要删除这个用户吗？删除后无法恢复。', '确认删除')

    let response
    try {
      // 尝试使用真实API
      response = await userApi.deleteUser(user.id)
    } catch (error) {
      console.error('删除用户失败:', error)
      Message.error('删除用户失败，请检查后端服务是否正常运行')
      return
    }

    if (response.code === 200) {
      Message.success(response.message || '删除成功')
      await loadUsers()
    } else {
      Message.error(response.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除用户失败:', error)
      Message.error('删除失败')
    }
  }
}

// 批量删除
const handleBatchDelete = async () => {
  try {
    // 检查是否包含超级管理员
    const superAdmins = selectedUsers.value.filter(user => user.role === 'SUPER_ADMIN')
    if (superAdmins.length > 0) {
      Message.warning(`选中的用户中包含 ${superAdmins.length} 个超级管理员，超级管理员不允许删除`)
      return
    }

    await MessageBox.delete(`确定要删除选中的 ${selectedUsers.value.length} 个用户吗？删除后无法恢复。`, '确认批量删除')

    let successCount = 0
    let failCount = 0

    // 逐个删除用户
    for (const user of selectedUsers.value) {
      try {
        let response
        try {
          // 尝试使用真实API
          response = await userApi.deleteUser(user.id)
        } catch (error) {
          console.error('删除用户失败:', error)
          failCount++
          continue
        }

        if (response.code === 200) {
          successCount++
        } else {
          failCount++
          console.error(`删除用户 ${user.username} 失败:`, response.message)
        }
      } catch (error) {
        failCount++
        console.error(`删除用户 ${user.username} 异常:`, error)
      }
    }

    // 显示结果
    if (failCount === 0) {
      Message.success(`批量删除成功，共删除 ${successCount} 个用户`)
    } else if (successCount === 0) {
      Message.error(`批量删除失败，${failCount} 个用户删除失败`)
    } else {
      Message.warning(`批量删除完成，成功 ${successCount} 个，失败 ${failCount} 个`)
    }

    await loadUsers()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      Message.error('批量删除失败')
    }
  }
}

// 创建用户
const handleCreate = async () => {
  try {
    await createFormRef.value.validate()
    createLoading.value = true

    let response
    try {
      // 尝试使用真实API
      response = await userApi.createUser(createForm)
    } catch (error) {
      console.error('创建用户失败:', error)
      Message.error('创建用户失败，请检查后端服务是否正常运行')
      return
    }

    if (response.code === 200) {
      Message.success(response.message || '用户创建成功')
      showCreateDialog.value = false
      resetCreateForm()
      await loadUsers()
    } else {
      Message.error(response.message || '创建失败')
    }
  } catch (error) {
    console.error('创建用户失败:', error)
    Message.error('创建失败')
  } finally {
    createLoading.value = false
  }
}

// 保存授权
const handleSaveAuthorization = async () => {
  try {
    authorizeLoading.value = true

    // 准备更新数据
    const updateData = {
      email: currentUser.value.email,
      realName: currentUser.value.realName,
      role: currentUser.value.role,
      status: currentUser.value.status,
      authorizedAppIds: authorizedAppIds.value
    }

    let response
    try {
      // 尝试使用真实API
      response = await userApi.updateUser(currentUser.value.id, updateData)
    } catch (error) {
      console.error('保存授权失败:', error)
      Message.error('保存授权失败，请检查后端服务是否正常运行')
      return
    }

    if (response.code === 200) {
      Message.success(response.message || '授权保存成功')
      showAuthorizeDialog.value = false
      await loadUsers()
    } else {
      Message.error(response.message || '授权保存失败')
    }
  } catch (error) {
    console.error('保存授权失败:', error)
    Message.error('授权保存失败')
  } finally {
    authorizeLoading.value = false
  }
}

// 更新用户
const handleUpdate = async () => {
  try {
    await editFormRef.value.validate()
    editLoading.value = true

    // 准备更新数据，排除不需要的字段
    const updateData = {
      email: editForm.email,
      realName: editForm.realName,
      role: editForm.role,
      status: editForm.status,
      authorizedAppIds: editForm.authorizedAppIds
    }

    // 如果提供了新密码，则包含密码字段
    if (editForm.password && editForm.password.trim()) {
      updateData.password = editForm.password
    }

    let response
    try {
      // 尝试使用真实API
      response = await userApi.updateUser(editForm.id, updateData)
    } catch (error) {
      console.error('更新用户失败:', error)
      Message.error('更新用户失败，请检查后端服务是否正常运行')
      return
    }

    if (response.code === 200) {
      Message.success(response.message || '用户更新成功')
      showEditDialog.value = false
      resetEditForm()
      await loadUsers()
    } else {
      Message.error(response.message || '更新失败')
    }
  } catch (error) {
    console.error('更新用户失败:', error)
    Message.error('更新失败')
  } finally {
    editLoading.value = false
  }
}

// 重置创建表单
const resetCreateForm = () => {
  Object.assign(createForm, {
    username: '',
    password: '',
    email: '',
    realName: '',
    role: ''
  })
  createFormRef.value?.resetFields()
}

// 重置编辑表单
const resetEditForm = () => {
  Object.assign(editForm, {
    id: '',
    username: '',
    email: '',
    realName: '',
    password: '',
    role: '',
    status: '',
    authorizedAppIds: []
  })
  editFormRef.value?.resetFields()
}

// 分页大小变化
const handleSizeChange = (size) => {
  pagination.size = size
  loadUsers()
}

// 当前页变化
const handleCurrentChange = (page) => {
  pagination.page = page
  loadUsers()
}

// 加载用户数据
const loadUsers = async () => {
  try {
    loading.value = true

    const params = {
      page: pagination.page,
      size: pagination.size,
      sort: 'createdAt',
      order: 'desc'
    }

    // 添加搜索条件
    if (searchForm.username) {
      params.keyword = searchForm.username
    }
    if (searchForm.role) {
      params.role = searchForm.role
    }
    if (searchForm.status) {
      params.status = searchForm.status
    }

    let response
    try {
      // 尝试使用真实API
      response = await userApi.getUsers(params)

      if (response.code === 200 && response.data) {
        let userList = response.data.content || []

        // 如果不是管理员，只显示当前用户自己的信息
        if (!authStore.isAdmin && authStore.user) {
          userList = userList.filter(user => user.id === authStore.user.id)
        }

        users.value = userList
        pagination.total = userList.length
      } else {
        throw new Error(response.message || '获取用户列表失败')
      }
    } catch (error) {
      console.error('获取用户列表失败:', error)
      users.value = []
      pagination.total = 0
      Message.error('获取用户列表失败，请检查后端服务是否正常运行')
    }

    // 加载可用应用
    await loadAvailableApps()
  } catch (error) {
    console.error('加载用户列表失败:', error)
    Message.error('加载用户列表失败')
  } finally {
    loading.value = false
  }
}

// 加载可用应用
const loadAvailableApps = async () => {
  try {
    let response
    try {
      // 尝试使用真实API
      response = await applicationApi.getApplications({ page: 1, size: 100 })
      if (response.code === 200 && response.data) {
        availableApps.value = response.data.content || []
      }
    } catch (error) {
      console.error('获取应用列表失败:', error)
      availableApps.value = []
    }
  } catch (error) {
    console.error('加载应用列表失败:', error)
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadUsers()
})
</script>

<style scoped>
.user-list {
  padding: var(--container-padding);
  background-color: var(--bg-color);
  min-height: calc(100vh - var(--header-height));
  max-width: var(--content-max-width);
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.search-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-form {
  margin: 0;
}

.search-buttons {
  display: flex;
  gap: 12px;
  align-items: center;
}

.table-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #ebeef5;
  background-color: #fafafa;
}

.table-header h2 {
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.action-buttons {
  display: flex;
  gap: 2px;
  justify-content: center;
  align-items: flex-start;
  flex-wrap: nowrap;
}

.action-buttons .el-button {
  min-width: 56px !important;
  width: 56px !important;
  height: 28px !important;
  padding: 4px 4px !important;
  font-size: 12px !important;
  border-radius: 4px !important;
  font-weight: 500 !important;
  display: flex !important;
  flex-direction: row !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 3px !important;
  line-height: 1 !important;
  box-sizing: border-box !important;
  vertical-align: top !important;
}

/* 确保所有按钮类型都应用相同样式 */
.action-buttons .el-button--primary,
.action-buttons .el-button--warning,
.action-buttons .el-button--success,
.action-buttons .el-button--danger {
  min-width: 56px !important;
  width: 56px !important;
  height: 28px !important;
  padding: 4px 4px !important;
}

.action-buttons .el-button .el-icon {
  font-size: 12px !important;
  margin: 0 !important;
  flex-shrink: 0 !important;
}

.action-buttons .el-button span {
  font-size: 12px !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

/* 超级管理员操作按钮禁用样式 */
.action-buttons .el-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.action-buttons .el-button:disabled:hover {
  opacity: 0.5;
}

/* 超级管理员复选框禁用样式 */
:deep(.el-table__row .el-checkbox.is-disabled) {
  opacity: 0.5;
}

:deep(.el-table__row .el-checkbox.is-disabled .el-checkbox__input) {
  cursor: not-allowed;
}

:deep(.el-table__row .el-checkbox.is-disabled .el-checkbox__inner) {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  cursor: not-allowed;
}

.pagination-wrapper {
  padding: 20px;
  display: flex;
  justify-content: center;
  background-color: #fafafa;
  border-top: 1px solid #ebeef5;
}

/* 授权对话框样式 */
.authorize-dialog {
  border-radius: 16px;
  overflow: hidden;
}

.authorize-dialog :deep(.el-dialog__header) {
  padding: 0;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

.authorize-dialog :deep(.el-dialog__body) {
  padding: 24px;
  max-height: 70vh;
  overflow-y: auto;
}

.authorize-dialog :deep(.el-dialog__footer) {
  padding: 16px 24px 24px;
  border-top: 1px solid #f0f0f0;
}

.authorize-header {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #e3f2fd 100%);
}

.header-icon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  background: white;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content h3 {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.header-content p {
  margin: 0;
  font-size: 14px;
  color: #606266;
}

.authorize-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 用户信息卡片 */
.user-info-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.user-avatar {
  flex-shrink: 0;
}

.user-details {
  flex: 1;
  min-width: 0;
}

.user-name {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.user-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 6px;
}

.user-username {
  font-size: 13px;
  color: #909399;
}

.user-email {
  font-size: 13px;
  color: #606266;
}

.user-status {
  flex-shrink: 0;
}

/* 应用授权区域 */
.app-authorization {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.section-header h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-subtitle {
  font-size: 13px;
  color: #606266;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
}

.empty-state p {
  margin: 12px 0 4px 0;
  font-size: 16px;
  font-weight: 500;
}

.empty-state span {
  font-size: 13px;
}

/* 应用列表 */
.app-list {
  max-height: 400px;
  overflow-y: auto;
  padding: 4px;
}

.app-checkbox-group {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 12px;
}

.app-item {
  border-radius: 12px;
  border: 2px solid #e4e7ed;
  transition: all 0.3s ease;
  overflow: hidden;
}

.app-item:hover {
  border-color: #c6e2ff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.1);
}

.app-item-checked {
  border-color: #409eff;
  background: #f0f8ff;
}

.app-checkbox {
  width: 100%;
  margin: 0;
}

.app-checkbox :deep(.el-checkbox__input) {
  position: absolute;
  top: 12px;
  right: 12px;
  z-index: 1;
}

.app-checkbox :deep(.el-checkbox__label) {
  width: 100%;
  padding: 0;
}

/* 兼容新版本的复选框结构 */
.app-checkbox :deep(.el-checkbox__input + *) {
  width: 100%;
  padding: 0;
}

.app-card {
  padding: 16px;
  padding-right: 40px;
  cursor: pointer;
}

.app-content {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.app-icon {
  flex-shrink: 0;
  width: 32px;
  height: 32px;
  background: #f0f8ff;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.app-name {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  line-height: 1.4;
  flex-shrink: 0;
}

.app-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.env-tag {
  font-size: 11px;
}

.app-version {
  font-size: 11px;
  color: #909399;
  background: #f5f7fa;
  padding: 2px 6px;
  border-radius: 4px;
}

/* 权限说明 */
.permission-note {
  margin-top: 8px;
}

.note-content p {
  margin: 4px 0;
  font-size: 13px;
  line-height: 1.5;
}

/* 对话框底部 */
.authorize-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .user-list {
    padding: var(--container-padding);
    min-height: calc(100vh - 120px);
  }

  /* 授权对话框移动端适配 */
  .authorize-dialog :deep(.el-dialog) {
    width: 95% !important;
    margin: 5vh auto !important;
  }

  .authorize-header {
    padding: 20px 16px;
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .authorize-dialog :deep(.el-dialog__body) {
    padding: 16px;
    max-height: 60vh;
  }

  .user-info-card {
    flex-direction: column;
    text-align: center;
    padding: 16px;
  }

  .user-meta {
    justify-content: center;
  }

  .app-checkbox-group {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .app-card {
    padding: 12px;
    padding-right: 36px;
  }

  .app-content {
    gap: 8px;
  }

  .app-icon {
    width: 28px;
    height: 28px;
  }

  .app-name {
    font-size: 13px;
  }

  .authorize-footer {
    flex-direction: column-reverse;
    gap: 8px;
  }

  .authorize-footer .el-button {
    width: 100%;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
    margin-bottom: 16px;
  }

  .page-header h1 {
    font-size: 20px;
    text-align: center;
  }

  .page-header .el-button {
    align-self: center;
    width: 200px;
  }

  .search-section {
    padding: 16px;
    margin-bottom: 16px;
  }

  .search-form {
    display: block;
  }

  .search-form .el-form-item {
    margin-bottom: 12px;
    display: block;
  }

  .search-form .el-form-item label {
    font-size: 13px;
    margin-bottom: 6px;
    display: block;
  }

  .search-form .el-input,
  .search-form .el-select {
    width: 100% !important;
  }

  .search-buttons {
    justify-content: center;
    gap: 12px;
    margin-top: 8px;
  }

  .search-buttons .el-button {
    flex: 1;
    max-width: 120px;
    height: 36px;
    padding: 8px 16px;
    font-size: 13px;
  }

  .table-section {
    margin-bottom: 16px;
  }

  .table-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
    padding: 16px;
  }

  .table-header h2 {
    font-size: 16px;
    text-align: center;
  }

  .header-actions {
    justify-content: center;
    gap: 10px;
  }

  .header-actions .el-button {
    height: 32px;
    padding: 8px 16px;
    font-size: 12px;
    flex: 1;
    max-width: 120px;
  }

  /* 表格样式优化 */
  :deep(.el-table th) {
    font-weight: bold;
    padding: 6px 0;
  }

  :deep(.el-table td) {
    padding: 6px 0;
  }

  /* 移动端表格优化 */
  :deep(.el-table) {
    font-size: 12px;
  }

  :deep(.el-table .el-table__header th) {
    padding: 4px 4px;
    font-size: 12px;
    font-weight: bold;
  }

  :deep(.el-table .el-table__body td) {
    padding: 4px 4px;
    font-size: 12px;
  }

  .action-buttons {
    flex-direction: column !important;
    gap: 4px !important;
    align-items: stretch !important;
  }

  .action-buttons .el-button {
    width: 100% !important;
    min-width: auto !important;
    height: 32px !important;
    padding: 6px 8px !important;
    font-size: 12px !important;
    flex-direction: row !important;
    justify-content: flex-start !important;
    gap: 6px !important;
  }

  .action-buttons .el-button--primary,
  .action-buttons .el-button--warning,
  .action-buttons .el-button--success,
  .action-buttons .el-button--danger {
    width: 100% !important;
    min-width: auto !important;
    height: 32px !important;
  }

  .action-buttons .el-button .el-icon {
    font-size: 12px !important;
    margin: 0 !important;
  }

  .action-buttons .el-button span {
    font-size: 12px !important;
  }

  .pagination-wrapper {
    padding: 16px;
  }

  :deep(.el-pagination) {
    justify-content: center;
  }

  :deep(.el-pagination .el-pager li) {
    min-width: 28px;
    height: 28px;
    line-height: 28px;
    font-size: 12px;
  }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {
  .user-list {
    padding: var(--container-padding);
  }

  .page-header h1 {
    font-size: 18px;
  }

  .page-header .el-button {
    width: 160px;
  }

  .search-section {
    padding: 12px;
  }

  .search-buttons .el-button {
    height: 32px;
    font-size: 12px;
  }

  .table-header {
    padding: 12px;
  }

  .header-actions .el-button {
    height: 28px;
    padding: 6px 12px;
    font-size: 11px;
  }
}
</style>
