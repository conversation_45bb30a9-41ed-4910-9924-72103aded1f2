import axios from 'axios'
import { Message } from '../utils/message'

// 创建axios实例
const request = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    // 从localStorage获取token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    // 直接返回data部分
    return response.data
  },
  (error) => {
    console.error('API请求错误:', error)
    
    // 处理不同的错误状态码
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          // 未授权，清除token并跳转到登录页
          localStorage.removeItem('token')
          localStorage.removeItem('refreshToken')
          localStorage.removeItem('userInfo')

          // 如果不是在登录页面，则跳转到登录页
          if (window.location.pathname !== '/login') {
            window.location.href = '/login'
          }

          Message.error('登录已过期，请重新登录')
          break

        case 403:
          Message.error('权限不足，无法访问该资源')
          break

        case 404:
          Message.error('请求的资源不存在')
          break

        case 500:
          Message.error('服务器内部错误')
          break

        default:
          Message.error(data?.message || '请求失败')
      }
      
      return Promise.reject(new Error(data?.message || '请求失败'))
    } else if (error.request) {
      // 网络错误
      Message.error('网络连接失败，请检查网络设置')
      return Promise.reject(new Error('网络连接失败'))
    } else {
      // 其他错误
      Message.error('请求配置错误')
      return Promise.reject(error)
    }
  }
)

export default request
