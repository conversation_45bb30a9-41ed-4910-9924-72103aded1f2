import request from './request'

export const authApi = {
  // 用户登录
  login(data) {
    return request({
      url: '/auth/login',
      method: 'post',
      data
    })
  },

  // 用户登出
  logout() {
    return request({
      url: '/auth/logout',
      method: 'post'
    })
  },

  // 获取当前用户信息
  getCurrentUser() {
    return request({
      url: '/auth/me',
      method: 'get'
    })
  },

  // 刷新token
  refreshToken() {
    return request({
      url: '/auth/refresh',
      method: 'post'
    })
  },

  // 修改密码
  changePassword(data) {
    return request({
      url: '/auth/change-password',
      method: 'post',
      data
    })
  },

  // 验证token
  validateToken() {
    return request({
      url: '/auth/validate',
      method: 'get'
    })
  }
}

// 模拟登录API（当后端不可用时）
export const mockAuthApi = {
  async login(data) {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 1000))

    const { username, password } = data

    // 预定义的用户账户
    const users = {
      admin: {
        username: 'admin',
        password: '123456',
        role: 'SUPER_ADMIN',
        name: '系统管理员',
        email: '<EMAIL>',
        permissions: ['*']
      },
      developer: {
        username: 'developer',
        password: '123456',
        role: 'DEVELOPER',
        name: '开发者',
        email: '<EMAIL>',
        permissions: ['logs:read', 'logs:write', 'applications:read']
      },
      user: {
        username: 'user',
        password: '123456',
        role: 'USER',
        name: '普通用户',
        email: '<EMAIL>',
        permissions: ['logs:read']
      }
    }

    const user = users[username]
    
    if (!user || user.password !== password) {
      throw new Error('用户名或密码错误')
    }

    // 生成模拟token
    const token = `mock_jwt_token_${username}_${Date.now()}`

    return {
      user: {
        id: Date.now(),
        username: user.username,
        name: user.name,
        email: user.email,
        role: user.role,
        permissions: user.permissions,
        avatar: null,
        createdAt: new Date().toISOString()
      },
      token,
      refreshToken: `refresh_${token}`,
      expiresIn: 7200 // 2小时
    }
  },

  async logout() {
    await new Promise(resolve => setTimeout(resolve, 500))
    return { message: '登出成功' }
  },

  async getCurrentUser() {
    const token = localStorage.getItem('token')
    if (!token || !token.startsWith('mock_jwt_token_')) {
      throw new Error('无效的token')
    }

    // 从token中解析用户名
    const username = token.split('_')[3]
    
    const users = {
      admin: {
        id: 1,
        username: 'admin',
        name: '系统管理员',
        email: '<EMAIL>',
        role: 'SUPER_ADMIN',
        permissions: ['*'],
        avatar: null,
        createdAt: '2024-01-01T00:00:00Z'
      },
      developer: {
        id: 2,
        username: 'developer',
        name: '开发者',
        email: '<EMAIL>',
        role: 'DEVELOPER',
        permissions: ['logs:read', 'logs:write', 'applications:read'],
        avatar: null,
        createdAt: '2024-01-01T00:00:00Z'
      },
      user: {
        id: 3,
        username: 'user',
        name: '普通用户',
        email: '<EMAIL>',
        role: 'USER',
        permissions: ['logs:read'],
        avatar: null,
        createdAt: '2024-01-01T00:00:00Z'
      }
    }

    const user = users[username]
    if (!user) {
      throw new Error('用户不存在')
    }

    return user
  },

  async validateToken() {
    const token = localStorage.getItem('token')
    if (!token || !token.startsWith('mock_jwt_token_')) {
      throw new Error('无效的token')
    }

    return { valid: true }
  },

  async changePassword(data) {
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 简单验证
    if (data.oldPassword !== '123456') {
      throw new Error('原密码错误')
    }

    return { message: '密码修改成功' }
  }
}
