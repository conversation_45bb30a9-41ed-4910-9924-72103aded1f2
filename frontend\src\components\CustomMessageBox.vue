<template>
  <teleport to="body">
    <transition name="modal" appear>
      <div v-if="visible" class="modal-overlay" @click="handleOverlayClick">
        <div class="modal-container" @click.stop>
          <div class="modal-header">
            <div class="modal-icon">
              <el-icon :size="24" :color="iconColor">
                <component :is="iconComponent" />
              </el-icon>
            </div>
            <h3 class="modal-title">{{ title }}</h3>
          </div>
          
          <div class="modal-body">
            <p class="modal-message">{{ message }}</p>
            
            <!-- 输入框（用于prompt类型） -->
            <div v-if="type === 'prompt'" class="modal-input">
              <el-input
                v-model="inputValue"
                :placeholder="inputPlaceholder"
                :type="inputType"
                ref="inputRef"
                @keyup.enter="handleConfirm"
              />
            </div>
          </div>
          
          <div class="modal-footer">
            <el-button
              v-if="showCancelButton"
              @click="handleCancel"
              :loading="loading"
            >
              {{ cancelButtonText }}
            </el-button>
            <el-button
              type="primary"
              @click="handleConfirm"
              :loading="loading"
              :class="confirmButtonClass"
            >
              {{ confirmButtonText }}
            </el-button>
          </div>
        </div>
      </div>
    </transition>
  </teleport>
</template>

<script setup>
import { ref, computed, nextTick, watch } from 'vue'
import {
  WarningFilled,
  InfoFilled,
  CircleCheckFilled,
  CircleCloseFilled,
  QuestionFilled
} from '@element-plus/icons-vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  type: {
    type: String,
    default: 'info', // info, warning, error, success, confirm, prompt
    validator: (value) => ['info', 'warning', 'error', 'success', 'confirm', 'prompt'].includes(value)
  },
  title: {
    type: String,
    default: '提示'
  },
  message: {
    type: String,
    required: true
  },
  confirmButtonText: {
    type: String,
    default: '确定'
  },
  cancelButtonText: {
    type: String,
    default: '取消'
  },
  showCancelButton: {
    type: Boolean,
    default: true
  },
  confirmButtonClass: {
    type: String,
    default: ''
  },
  inputPlaceholder: {
    type: String,
    default: '请输入'
  },
  inputType: {
    type: String,
    default: 'text'
  },
  closeOnClickModal: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['confirm', 'cancel', 'close'])

const loading = ref(false)
const inputValue = ref('')
const inputRef = ref()

// 图标配置
const iconConfig = computed(() => {
  const configs = {
    info: { component: InfoFilled, color: '#409eff' },
    warning: { component: WarningFilled, color: '#e6a23c' },
    error: { component: CircleCloseFilled, color: '#f56c6c' },
    success: { component: CircleCheckFilled, color: '#67c23a' },
    confirm: { component: QuestionFilled, color: '#e6a23c' },
    prompt: { component: QuestionFilled, color: '#409eff' }
  }
  return configs[props.type] || configs.info
})

const iconComponent = computed(() => iconConfig.value.component)
const iconColor = computed(() => iconConfig.value.color)

// 监听visible变化，自动聚焦输入框
watch(() => props.visible, (newVal) => {
  if (newVal && props.type === 'prompt') {
    nextTick(() => {
      inputRef.value?.focus()
    })
  }
})

// 处理确认
const handleConfirm = async () => {
  loading.value = true
  try {
    if (props.type === 'prompt') {
      emit('confirm', inputValue.value)
    } else {
      emit('confirm')
    }
  } finally {
    loading.value = false
  }
}

// 处理取消
const handleCancel = () => {
  emit('cancel')
}

// 处理遮罩点击
const handleOverlayClick = () => {
  if (props.closeOnClickModal) {
    handleCancel()
  }
}

// 重置输入值
const resetInput = () => {
  inputValue.value = ''
}

defineExpose({
  resetInput
})
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  padding: 20px;
}

.modal-container {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  max-width: 500px;
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  padding: 24px 24px 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.modal-icon {
  flex-shrink: 0;
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.modal-body {
  padding: 20px 24px;
  flex: 1;
  overflow-y: auto;
}

.modal-message {
  font-size: 14px;
  line-height: 1.6;
  color: #606266;
  margin: 0;
  word-break: break-word;
}

.modal-input {
  margin-top: 16px;
}

.modal-footer {
  padding: 16px 24px 24px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  border-top: 1px solid #f0f0f0;
}

/* 动画效果 */
.modal-enter-active {
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.modal-leave-active {
  transition: all 0.2s ease-in;
}

.modal-enter-from {
  opacity: 0;
  transform: scale(0.8);
}

.modal-leave-to {
  opacity: 0;
  transform: scale(0.8);
}

/* 移动端适配 */
@media (max-width: 768px) {
  .modal-overlay {
    padding: 10px;
  }
  
  .modal-container {
    border-radius: 12px;
    max-width: none;
  }
  
  .modal-header {
    padding: 20px 20px 12px;
  }
  
  .modal-title {
    font-size: 16px;
  }
  
  .modal-body {
    padding: 16px 20px;
  }
  
  .modal-footer {
    padding: 12px 20px 20px;
    flex-direction: column-reverse;
  }
  
  .modal-footer .el-button {
    width: 100%;
    margin: 0;
  }
  
  .modal-footer .el-button + .el-button {
    margin-bottom: 8px;
  }
}
</style>
