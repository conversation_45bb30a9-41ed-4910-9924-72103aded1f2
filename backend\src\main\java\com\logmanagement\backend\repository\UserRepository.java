package com.logmanagement.backend.repository;

import com.logmanagement.backend.entity.User;
import com.logmanagement.backend.entity.UserRole;
import com.logmanagement.backend.entity.UserStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 用户仓库接口
 * 
 * <AUTHOR> Management System
 * @version 1.0.0
 */
@Repository
public interface UserRepository extends MongoRepository<User, String> {

    /**
     * 根据用户名查找用户
     *
     * @param username 用户名
     * @return 用户信息
     */
    Optional<User> findByUsername(String username);

    /**
     * 根据邮箱查找用户
     *
     * @param email 邮箱
     * @return 用户信息
     */
    Optional<User> findByEmail(String email);

    /**
     * 根据用户名或邮箱查找用户
     * 
     * @param username 用户名
     * @param email 邮箱
     * @return 用户信息
     */
    Optional<User> findByUsernameOrEmail(String username, String email);

    /**
     * 根据用户角色查找用户
     * 
     * @param role 用户角色
     * @param pageable 分页参数
     * @return 用户分页结果
     */
    Page<User> findByRole(UserRole role, Pageable pageable);

    /**
     * 根据用户状态查找用户
     * 
     * @param status 用户状态
     * @param pageable 分页参数
     * @return 用户分页结果
     */
    Page<User> findByStatus(UserStatus status, Pageable pageable);

    /**
     * 根据用户角色和状态查找用户
     * 
     * @param role 用户角色
     * @param status 用户状态
     * @param pageable 分页参数
     * @return 用户分页结果
     */
    Page<User> findByRoleAndStatus(UserRole role, UserStatus status, Pageable pageable);

    /**
     * 根据授权应用ID查找用户
     * 
     * @param appId 应用ID
     * @return 用户列表
     */
    List<User> findByAuthorizedAppIdsContaining(String appId);

    /**
     * 检查用户名是否存在
     * 
     * @param username 用户名
     * @return 是否存在
     */
    boolean existsByUsername(String username);

    /**
     * 检查邮箱是否存在
     * 
     * @param email 邮箱
     * @return 是否存在
     */
    boolean existsByEmail(String email);

    /**
     * 统计指定角色的用户数量
     * 
     * @param role 用户角色
     * @return 用户数量
     */
    long countByRole(UserRole role);

    /**
     * 统计指定状态的用户数量
     *
     * @param status 用户状态
     * @return 用户数量
     */
    long countByStatus(UserStatus status);

    /**
     * 根据用户名模糊搜索用户
     *
     * @param username 用户名关键词
     * @param pageable 分页参数
     * @return 用户分页结果
     */
    Page<User> findByUsernameContainingIgnoreCase(String username, Pageable pageable);

    /**
     * 根据真实姓名模糊搜索用户
     *
     * @param realName 真实姓名关键词
     * @param pageable 分页参数
     * @return 用户分页结果
     */
    Page<User> findByRealNameContainingIgnoreCase(String realName, Pageable pageable);

    /**
     * 根据邮箱模糊搜索用户
     *
     * @param email 邮箱关键词
     * @param pageable 分页参数
     * @return 用户分页结果
     */
    Page<User> findByEmailContainingIgnoreCase(String email, Pageable pageable);
}
