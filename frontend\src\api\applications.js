import request from './request'

export const applicationApi = {
  // 获取应用列表
  getApplications(params) {
    return request({
      url: '/applications',
      method: 'get',
      params
    })
  },

  // 获取应用详情
  getApplication(id) {
    return request({
      url: `/applications/${id}`,
      method: 'get'
    })
  },

  // 创建应用
  createApplication(data) {
    return request({
      url: '/applications',
      method: 'post',
      data
    })
  },

  // 更新应用
  updateApplication(id, data) {
    return request({
      url: `/applications/${id}`,
      method: 'put',
      data
    })
  },

  // 删除应用
  deleteApplication(id) {
    return request({
      url: `/applications/${id}`,
      method: 'delete'
    })
  },

  // 重新生成应用Token
  regenerateToken(id) {
    return request({
      url: `/applications/${id}/regenerate-token`,
      method: 'post'
    })
  }
}

// 模拟应用API（备用）
export const mockApplicationApi = {
  // 获取应用列表
  async getApplications(params = {}) {
    // 模拟延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    const mockData = [
      {
        id: 'app-001',
        name: '用户管理系统',
        description: '负责用户注册、登录、权限管理等核心功能的系统',
        status: 'ACTIVE',
        environment: 'dev',
        version: '2.1.3',
        token: '8e8bbf7e79dc490aa3fa24939b907bd1',
        creatorUsername: 'admin',
        createdAt: '2025-06-17T02:18:48.343Z',
        logRetentionDays: 30
      },
      {
        id: 'app-002',
        name: '订单管理系统',
        description: '处理订单创建、支付、发货、退款等业务流程',
        status: 'ACTIVE',
        environment: 'test',
        version: '1.8.2',
        token: '9397a5a38fd6448e8dba9755d3414efa',
        creatorUsername: 'admin',
        createdAt: '2025-06-17T02:18:48.343Z',
        logRetentionDays: 30
      },
      {
        id: 'app-003',
        name: '支付系统',
        description: '集成多种支付方式，提供安全可靠的支付服务',
        status: 'ACTIVE',
        environment: 'staging',
        version: '3.0.1',
        token: 'ef64b3b116e04fb0be05352ce48dd7b1',
        creatorUsername: 'admin',
        createdAt: '2025-06-17T02:18:48.343Z',
        logRetentionDays: 30
      },
      {
        id: 'app-004',
        name: '通知系统',
        description: '统一的消息推送平台，支持邮件、短信、APP推送',
        status: 'ACTIVE',
        environment: 'prod',
        version: '1.5.0',
        token: 'b557de3a23b0423ab7a59424896941fb',
        creatorUsername: 'admin',
        createdAt: '2025-06-17T02:18:48.343Z',
        logRetentionDays: 30
      },
      {
        id: 'app-005',
        name: '数据分析系统',
        description: '提供实时数据分析和报表生成功能',
        status: 'ACTIVE',
        environment: 'prod',
        version: '2.3.1',
        token: 'c668e4b227c1456b9e16463d7f8e9c2a',
        creatorUsername: 'admin',
        createdAt: '2025-06-17T02:18:48.343Z',
        logRetentionDays: 60
      },
      {
        id: 'app-006',
        name: '库存管理系统',
        description: '商品库存管理、入库出库、库存预警等功能',
        status: 'ACTIVE',
        environment: 'development',
        version: '1.2.0-beta',
        token: 'd779f5c338d2467ca27574e8g9f0ad3b',
        creatorUsername: 'admin',
        createdAt: '2025-06-17T02:18:48.343Z',
        logRetentionDays: 30
      }
    ]

    // 简单的分页处理
    const page = params.page || 1
    const size = params.size || 10
    const start = (page - 1) * size
    const end = start + size

    return {
      code: 200,
      message: '获取应用列表成功',
      data: {
        content: mockData.slice(start, end),
        totalElements: mockData.length,
        totalPages: Math.ceil(mockData.length / size),
        size: size,
        number: page - 1,
        numberOfElements: Math.min(size, mockData.length - start)
      }
    }
  },

  // 获取应用详情
  async getApplication(id) {
    await new Promise(resolve => setTimeout(resolve, 300))
    
    const mockApps = await this.getApplications()
    const app = mockApps.data.content.find(app => app.id === id)
    
    if (app) {
      return {
        code: 200,
        message: '获取应用详情成功',
        data: app
      }
    } else {
      return {
        code: 404,
        message: '应用不存在',
        data: null
      }
    }
  },

  // 创建应用
  async createApplication(data) {
    await new Promise(resolve => setTimeout(resolve, 800))
    
    const newApp = {
      id: 'app-' + Date.now(),
      name: data.name,
      description: data.description,
      status: 'ACTIVE',
      environment: data.environment || 'dev',
      version: data.version || '1.0.0',
      token: 'mock_token_' + Date.now(),
      creatorUsername: 'current_user',
      createdAt: new Date().toISOString(),
      logRetentionDays: data.logRetentionDays || 30
    }

    return {
      code: 200,
      message: '应用创建成功',
      data: newApp
    }
  },

  // 更新应用
  async updateApplication(id, data) {
    await new Promise(resolve => setTimeout(resolve, 600))
    
    return {
      code: 200,
      message: '应用更新成功',
      data: {
        id: id,
        ...data,
        updatedAt: new Date().toISOString()
      }
    }
  },

  // 删除应用
  async deleteApplication(id) {
    await new Promise(resolve => setTimeout(resolve, 400))
    
    return {
      code: 200,
      message: '应用删除成功',
      data: null
    }
  },

  // 重新生成Token
  async regenerateToken(id) {
    await new Promise(resolve => setTimeout(resolve, 500))
    
    return {
      code: 200,
      message: 'Token重新生成成功',
      data: {
        token: 'new_mock_token_' + Date.now()
      }
    }
  }
}
