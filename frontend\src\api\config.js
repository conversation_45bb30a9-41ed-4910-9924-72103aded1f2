import request from './request'

export const configApi = {
  // 获取分页配置
  getPaginationConfig() {
    return request({
      url: '/config/pagination',
      method: 'get'
    })
  },

  // 获取系统配置
  getSystemConfig() {
    return request({
      url: '/config/system',
      method: 'get'
    })
  }
}

// 缓存配置数据
let cachedPaginationConfig = null

// 获取分页配置（带缓存）
export const getPaginationConfig = async () => {
  if (cachedPaginationConfig) {
    return cachedPaginationConfig
  }

  try {
    const response = await configApi.getPaginationConfig()
    if (response.code === 200 && response.data) {
      cachedPaginationConfig = response.data
      return cachedPaginationConfig
    }
  } catch (error) {
    console.warn('获取分页配置失败，使用默认配置:', error)
  }

  // 返回默认配置
  const defaultConfig = {
    defaultPageSize: 20,
    maxPageSize: 100
  }
  cachedPaginationConfig = defaultConfig
  return defaultConfig
}

// 清除配置缓存
export const clearConfigCache = () => {
  cachedPaginationConfig = null
}
