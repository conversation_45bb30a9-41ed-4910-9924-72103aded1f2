using LogSimulator.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace LogSimulator.Services;

/// <summary>
/// 自定义DateTime转换器，用于LocalDateTime格式
/// </summary>
public class LocalDateTimeConverter : JsonConverter<DateTime>
{
    public override DateTime Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        return DateTime.Parse(reader.GetString()!);
    }

    public override void Write(Utf8JsonWriter writer, DateTime value, JsonSerializerOptions options)
    {
        writer.WriteStringValue(value.ToString("yyyy-MM-dd HH:mm:ss.fff"));
    }
}

/// <summary>
/// 日志API服务
/// </summary>
public class LogApiService
{
    private readonly HttpClient _httpClient;
    private readonly IConfiguration _configuration;
    private readonly ILogger<LogApiService> _logger;
    private readonly string _apiBaseUrl;
    private readonly JsonSerializerOptions _jsonOptions;

    public LogApiService(HttpClient httpClient, IConfiguration configuration, ILogger<LogApiService> logger)
    {
        _httpClient = httpClient;
        _configuration = configuration;
        _logger = logger;
        _apiBaseUrl = _configuration["LogManagement:ApiBaseUrl"] ?? "http://localhost:8080/api";

        // 配置JSON序列化设置
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = false
        };
        _jsonOptions.Converters.Add(new LocalDateTimeConverter());

        // 配置HttpClient
        _httpClient.Timeout = TimeSpan.FromSeconds(30);

        // 配置请求头
        ConfigureHeaders();
    }

    /// <summary>
    /// 配置请求头
    /// </summary>
    private void ConfigureHeaders()
    {
        // 基本请求头
        _httpClient.DefaultRequestHeaders.Add("User-Agent", "LogSimulator/1.0.0");
        _httpClient.DefaultRequestHeaders.Add("Accept", "application/json");

        // 认证请求头
        var appId = _configuration["LogManagement:AppId"];
        var apiKey = _configuration["LogManagement:ApiKey"];

        if (!string.IsNullOrEmpty(appId))
        {
            _httpClient.DefaultRequestHeaders.Add("X-App-ID", appId);
            _logger.LogDebug("已配置X-App-ID请求头: {AppId}", appId);
        }

        if (!string.IsNullOrEmpty(apiKey))
        {
            _httpClient.DefaultRequestHeaders.Add("X-Api-Key", apiKey);
            _logger.LogDebug("已配置X-Api-Key请求头");
        }

        if (string.IsNullOrEmpty(appId) || string.IsNullOrEmpty(apiKey))
        {
            _logger.LogWarning("AppID或ApiKey未配置，API调用可能失败");
        }
    }



    /// <summary>
    /// 发送单个日志条目
    /// </summary>
    public async Task<ApiResponse<object>> SendLogAsync(LogEntry logEntry)
    {
        try
        {
            var json = JsonSerializer.Serialize(logEntry, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            _logger.LogDebug("发送日志到API: {ApiUrl}/external/logs/create", _apiBaseUrl);
            _logger.LogDebug("发送的JSON数据: {Json}", json);

            var response = await _httpClient.PostAsync($"{_apiBaseUrl}/external/logs/create", content);
            var responseContent = await response.Content.ReadAsStringAsync();

            // 尝试解析ApiResponse
            try
            {
                var apiResponse = JsonSerializer.Deserialize<ApiResponse<object>>(responseContent, _jsonOptions);
                if (apiResponse != null)
                {
                    _logger.LogDebug("收到API响应: Code={Code}, Message={Message}",
                        apiResponse.Code, apiResponse.Message);
                    return apiResponse;
                }
            }
            catch (JsonException ex)
            {
                _logger.LogWarning("无法解析API响应为ApiResponse格式: {Error}", ex.Message);
            }

            // 如果无法解析为ApiResponse，则根据HTTP状态码创建响应
            if (response.IsSuccessStatusCode)
            {
                _logger.LogDebug("日志发送成功: {Response}", responseContent);
                return new ApiResponse<object>
                {
                    Code = 200,
                    Message = "日志发送成功",
                    Data = null,
                    Timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
                };
            }
            else
            {
                _logger.LogWarning("日志发送失败: {StatusCode} - {Error}", response.StatusCode, responseContent);
                return new ApiResponse<object>
                {
                    Code = (int)response.StatusCode,
                    Message = $"HTTP {response.StatusCode}: {responseContent}",
                    Data = null,
                    Timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
                };
            }
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "HTTP请求异常: {Message}", ex.Message);
            return new ApiResponse<object>
            {
                Code = 500,
                Message = $"HTTP请求异常: {ex.Message}",
                Data = null,
                Timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
            };
        }
        catch (TaskCanceledException ex)
        {
            _logger.LogError(ex, "请求超时: {Message}", ex.Message);
            return new ApiResponse<object>
            {
                Code = 408,
                Message = $"请求超时: {ex.Message}",
                Data = null,
                Timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送日志时发生未知异常: {Message}", ex.Message);
            return new ApiResponse<object>
            {
                Code = 500,
                Message = $"未知异常: {ex.Message}",
                Data = null,
                Timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
            };
        }
    }

    /// <summary>
    /// 批量发送日志条目
    /// </summary>
    public async Task<ApiResponse<object>> SendLogsBatchAsync(List<LogEntry> logEntries)
    {
        try
        {
            var batchRequest = new BatchLogRequest { Logs = logEntries };
            var json = JsonSerializer.Serialize(batchRequest, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            _logger.LogDebug("批量发送 {Count} 条日志到API: {ApiUrl}/external/logs/batch", logEntries.Count, _apiBaseUrl);

            var response = await _httpClient.PostAsync($"{_apiBaseUrl}/external/logs/batch", content);
            var responseContent = await response.Content.ReadAsStringAsync();

            // 尝试解析ApiResponse
            try
            {
                var apiResponse = JsonSerializer.Deserialize<ApiResponse<object>>(responseContent, _jsonOptions);
                if (apiResponse != null)
                {
                    _logger.LogDebug("收到批量API响应: Code={Code}, Message={Message}",
                        apiResponse.Code, apiResponse.Message);
                    return apiResponse;
                }
            }
            catch (JsonException ex)
            {
                _logger.LogWarning("无法解析批量API响应为ApiResponse格式: {Error}", ex.Message);
            }

            // 如果无法解析为ApiResponse，则根据HTTP状态码创建响应
            if (response.IsSuccessStatusCode)
            {
                _logger.LogDebug("批量日志发送成功: {Response}", responseContent);
                return new ApiResponse<object>
                {
                    Code = 200,
                    Message = "批量日志发送成功",
                    Data = null,
                    Timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
                };
            }
            else
            {
                _logger.LogWarning("批量日志发送失败: {StatusCode} - {Error}", response.StatusCode, responseContent);
                return new ApiResponse<object>
                {
                    Code = (int)response.StatusCode,
                    Message = $"HTTP {response.StatusCode}: {responseContent}",
                    Data = null,
                    Timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
                };
            }
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "HTTP请求异常: {Message}", ex.Message);
            return new ApiResponse<object>
            {
                Code = 500,
                Message = $"HTTP请求异常: {ex.Message}",
                Data = null,
                Timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
            };
        }
        catch (TaskCanceledException ex)
        {
            _logger.LogError(ex, "请求超时: {Message}", ex.Message);
            return new ApiResponse<object>
            {
                Code = 408,
                Message = $"请求超时: {ex.Message}",
                Data = null,
                Timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量发送日志时发生未知异常: {Message}", ex.Message);
            return new ApiResponse<object>
            {
                Code = 500,
                Message = $"未知异常: {ex.Message}",
                Data = null,
                Timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
            };
        }
    }



    /// <summary>
    /// 检查API健康状态
    /// </summary>
    public async Task<bool> CheckApiHealthAsync()
    {
        try
        {
            _logger.LogDebug("检查API健康状态: {ApiUrl}/system/health", _apiBaseUrl);

            var response = await _httpClient.GetAsync($"{_apiBaseUrl}/system/health");

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                _logger.LogDebug("API健康检查成功: {Response}", responseContent);
                return true;
            }
            else
            {
                _logger.LogWarning("API健康检查失败: {StatusCode}", response.StatusCode);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "API健康检查异常: {Message}", ex.Message);
            return false;
        }
    }

    /// <summary>
    /// 获取应用程序统计信息
    /// </summary>
    public async Task<string?> GetApplicationStatsAsync()
    {
        try
        {
            var applicationId = _configuration["LogManagement:AppId"];
            _logger.LogDebug("获取应用程序统计信息: {ApplicationId}", applicationId);

            var response = await _httpClient.GetAsync($"{_apiBaseUrl}/logs/stats/{applicationId}");

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                _logger.LogDebug("获取统计信息成功");
                return responseContent;
            }
            else
            {
                _logger.LogWarning("获取统计信息失败: {StatusCode}", response.StatusCode);
                return null;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取统计信息异常: {Message}", ex.Message);
            return null;
        }
    }

    /// <summary>
    /// 测试连接
    /// </summary>
    public async Task<bool> TestConnectionAsync()
    {
        try
        {
            _logger.LogInformation("测试API连接: {ApiUrl}", _apiBaseUrl);

            // 检查健康状态
            var healthCheck = await CheckApiHealthAsync();
            if (!healthCheck)
            {
                _logger.LogError("API健康检查失败，无法建立连接");
                return false;
            }

            _logger.LogInformation("API连接测试成功");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "API连接测试失败: {Message}", ex.Message);
            return false;
        }
    }
}
