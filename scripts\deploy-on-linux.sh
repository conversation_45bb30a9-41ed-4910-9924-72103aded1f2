#!/bin/bash

# 通用日志管理系统 - Linux自动化部署脚本
# 作者: Logger Management System
# 版本: 1.0.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
PROJECT_NAME="logger-management"
DEPLOY_USER=$(whoami)
DEPLOY_TIME=$(date '+%Y-%m-%d %H:%M:%S')

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示系统信息
show_system_info() {
    log_info "系统信息检查..."
    echo "操作系统: $(uname -s)"
    echo "内核版本: $(uname -r)"
    echo "架构: $(uname -m)"
    echo "部署用户: $DEPLOY_USER"
    echo "部署时间: $DEPLOY_TIME"
    echo "当前目录: $(pwd)"
}

# 检查系统要求
check_requirements() {
    log_info "检查系统要求..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装"
        log_info "请先安装Docker: https://docs.docker.com/engine/install/"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装"
        log_info "请先安装Docker Compose: https://docs.docker.com/compose/install/"
        exit 1
    fi
    
    # 检查Docker服务状态
    if ! systemctl is-active --quiet docker; then
        log_warning "Docker服务未运行，尝试启动..."
        sudo systemctl start docker
        if ! systemctl is-active --quiet docker; then
            log_error "Docker服务启动失败"
            exit 1
        fi
    fi
    
    # 检查用户权限
    if ! groups $USER | grep -q docker; then
        log_warning "当前用户不在docker组中，可能需要sudo权限"
        log_info "建议将用户添加到docker组: sudo usermod -aG docker $USER"
    fi
    
    log_success "系统要求检查通过"
}

# 检查项目结构
check_project_structure() {
    log_info "检查项目结构..."
    
    local required_dirs=("backend" "frontend" "docker")
    local required_files=("docker/docker-compose.yml" "backend/pom.xml" "frontend/package.json")
    
    for dir in "${required_dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            log_error "缺少必要目录: $dir"
            exit 1
        fi
    done
    
    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            log_error "缺少必要文件: $file"
            exit 1
        fi
    done
    
    log_success "项目结构检查通过"
}

# 选择部署模式
select_deployment_mode() {
    echo ""
    echo "请选择部署模式:"
    echo "1) 完整部署 (包含MongoDB容器)"
    echo "2) 外部MongoDB部署 (使用现有MongoDB服务器)"
    echo ""
    
    while true; do
        read -p "请输入选择 (1 或 2): " choice
        case $choice in
            1)
                DEPLOYMENT_MODE="full"
                COMPOSE_FILE="docker-compose.yml"
                ENV_FILE="docker/.env.example"
                break
                ;;
            2)
                DEPLOYMENT_MODE="external"
                COMPOSE_FILE="docker-compose-external-mongo.yml"
                ENV_FILE="docker/.env.external-mongo"
                break
                ;;
            *)
                echo "无效选择，请输入 1 或 2"
                ;;
        esac
    done
    
    log_info "选择的部署模式: $DEPLOYMENT_MODE"
}

# 配置环境变量
configure_environment() {
    log_info "配置环境变量..."
    
    cd docker
    
    if [ ! -f ".env" ]; then
        if [ -f "$ENV_FILE" ]; then
            cp "$ENV_FILE" .env
            log_info "已创建环境变量文件: .env"
        else
            log_error "环境变量模板文件不存在: $ENV_FILE"
            exit 1
        fi
    else
        log_info "环境变量文件已存在: .env"
    fi
    
    # 如果是外部MongoDB模式，检查配置
    if [ "$DEPLOYMENT_MODE" = "external" ]; then
        log_warning "请确保已配置正确的MongoDB连接信息"
        log_info "编辑文件: docker/.env"
        
        read -p "是否现在编辑配置文件? (y/N): " edit_config
        if [[ $edit_config =~ ^[Yy]$ ]]; then
            ${EDITOR:-nano} .env
        fi
    fi
    
    cd ..
}

# 构建和启动服务
deploy_services() {
    log_info "开始部署服务..."
    
    cd docker
    
    # 停止现有服务（如果存在）
    if [ -f "$COMPOSE_FILE" ]; then
        log_info "停止现有服务..."
        docker-compose -f "$COMPOSE_FILE" down 2>/dev/null || true
    fi
    
    # 构建镜像
    log_info "构建Docker镜像..."
    docker-compose -f "$COMPOSE_FILE" build
    
    # 启动服务
    log_info "启动服务..."
    if [ "$DEPLOYMENT_MODE" = "full" ]; then
        # 完整部署模式
        docker-compose -f "$COMPOSE_FILE" up -d
    else
        # 外部MongoDB模式
        docker-compose -f "$COMPOSE_FILE" --env-file .env up -d
    fi
    
    cd ..
    
    log_success "服务部署完成"
}

# 等待服务启动
wait_for_services() {
    log_info "等待服务启动..."
    
    local max_wait=120
    local wait_time=0
    
    while [ $wait_time -lt $max_wait ]; do
        if curl -f http://localhost:8080/api/actuator/health &>/dev/null; then
            log_success "后端服务已启动"
            break
        fi
        
        echo -n "."
        sleep 5
        wait_time=$((wait_time + 5))
    done
    
    if [ $wait_time -ge $max_wait ]; then
        log_warning "后端服务启动超时，请检查日志"
    fi
    
    # 检查前端服务
    if curl -f http://localhost/ &>/dev/null; then
        log_success "前端服务已启动"
    else
        log_warning "前端服务可能未正常启动"
    fi
}

# 检查服务状态
check_service_status() {
    log_info "检查服务状态..."
    
    cd docker
    docker-compose -f "$COMPOSE_FILE" ps
    cd ..
    
    echo ""
    log_info "服务健康检查..."
    
    # 检查后端健康状态
    if curl -f http://localhost:8080/api/actuator/health &>/dev/null; then
        log_success "✓ 后端服务健康"
    else
        log_error "✗ 后端服务异常"
    fi
    
    # 检查前端服务
    if curl -f http://localhost/ &>/dev/null; then
        log_success "✓ 前端服务健康"
    else
        log_error "✗ 前端服务异常"
    fi
}

# 显示部署结果
show_deployment_result() {
    log_success "部署完成！"
    echo ""
    echo "=========================================="
    echo "通用日志管理系统部署信息"
    echo "=========================================="
    echo "部署模式: $DEPLOYMENT_MODE"
    echo "部署时间: $DEPLOY_TIME"
    echo "部署用户: $DEPLOY_USER"
    echo ""
    echo "访问地址:"
    echo "  前端应用: http://$(hostname -I | awk '{print $1}')"
    echo "  后端API:  http://$(hostname -I | awk '{print $1}'):8080/api"
    
    if [ "$DEPLOYMENT_MODE" = "full" ]; then
        echo "  MongoDB管理: http://$(hostname -I | awk '{print $1}'):8081"
        echo "    用户名: admin"
        echo "    密码: admin123"
    fi
    
    echo ""
    echo "常用命令:"
    echo "  查看日志: cd docker && docker-compose -f $COMPOSE_FILE logs -f"
    echo "  停止服务: cd docker && docker-compose -f $COMPOSE_FILE down"
    echo "  重启服务: cd docker && docker-compose -f $COMPOSE_FILE restart"
    echo "  查看状态: cd docker && docker-compose -f $COMPOSE_FILE ps"
    echo "=========================================="
}

# 主函数
main() {
    echo "=========================================="
    echo "通用日志管理系统 - Linux自动化部署"
    echo "=========================================="
    
    show_system_info
    check_requirements
    check_project_structure
    select_deployment_mode
    configure_environment
    deploy_services
    wait_for_services
    check_service_status
    show_deployment_result
}

# 执行主函数
main "$@"
