@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: 通用日志管理系统 - 一键远程部署脚本
:: 作者: Logger Management System
:: 版本: 1.0.0

echo ==========================================
echo 通用日志管理系统 - 一键远程部署
echo ==========================================

:: 检查配置文件
if not exist "%~dp0deploy-config.txt" (
    echo [ERROR] 配置文件不存在: deploy-config.txt
    echo [INFO] 正在创建配置文件模板...
    call :create_config_template
    echo [INFO] 请编辑 deploy-config.txt 文件后重新运行此脚本
    pause
    exit /b 1
)

echo [INFO] 开始一键部署流程...

:: 步骤1: 打包源码
echo.
echo [STEP 1/3] 打包源码...
call "%~dp0package-source.bat"
if errorlevel 1 (
    echo [ERROR] 源码打包失败
    pause
    exit /b 1
)

:: 步骤2: 传输文件
echo.
echo [STEP 2/3] 传输文件到Linux服务器...
call "%~dp0transfer-to-linux.bat"
if errorlevel 1 (
    echo [ERROR] 文件传输失败
    pause
    exit /b 1
)

:: 步骤3: 远程部署
echo.
echo [STEP 3/3] 远程部署完成
echo [SUCCESS] 一键部署流程完成！

:: 读取配置获取服务器信息
for /f "tokens=1,2 delims==" %%a in (%~dp0deploy-config.txt) do (
    if "%%a"=="SERVER_HOST" set SERVER_HOST=%%b
    if "%%a"=="SERVER_USER" set SERVER_USER=%%b
)

echo.
echo ==========================================
echo 部署完成信息
echo ==========================================
if defined SERVER_HOST (
    echo 前端地址: http://%SERVER_HOST%
    echo 后端API: http://%SERVER_HOST%:8080/api
    echo 服务器: %SERVER_USER%@%SERVER_HOST%
)
echo ==========================================

pause
exit /b 0

:: 创建配置文件模板
:create_config_template
(
echo # 通用日志管理系统 - 部署配置文件
echo # 请根据实际情况修改以下配置
echo.
echo # Linux服务器配置
echo SERVER_HOST=your-linux-server-ip
echo SERVER_USER=your-username
echo SERVER_PORT=22
echo.
echo # 部署路径 ^(在Linux服务器上的目录^)
echo DEPLOY_PATH=/home/<USER>/logger-management
echo.
echo # SSH密钥路径 ^(可选，如果使用密钥认证^)
echo # SSH_KEY_PATH=C:\Users\<USER>\.ssh\id_rsa
echo.
echo # 示例配置:
echo # SERVER_HOST=*************
echo # SERVER_USER=ubuntu
echo # SERVER_PORT=22
echo # DEPLOY_PATH=/home/<USER>/logger-management
echo # SSH_KEY_PATH=C:\Users\<USER>\.ssh\id_rsa
) > "%~dp0deploy-config.txt"
goto :eof

