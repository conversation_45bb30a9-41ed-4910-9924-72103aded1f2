<template>
  <div class="settings-container">
    <div class="settings-header">
      <h1>系统设置</h1>
      <p>管理您的偏好设置和系统配置</p>
    </div>

    <div class="settings-content">
      <!-- 界面设置 -->
      <el-card class="settings-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <el-icon><Monitor /></el-icon>
            <span>界面设置</span>
          </div>
        </template>

        <div class="settings-section">
          <div class="setting-item">
            <div class="setting-label">
              <h4>主题模式</h4>
              <p>选择您偏好的界面主题</p>
            </div>
            <div class="setting-control">
              <el-radio-group v-model="settings.theme" @change="handleThemeChange">
                <el-radio value="light">浅色主题</el-radio>
                <el-radio value="dark">深色主题</el-radio>
                <el-radio value="auto">跟随系统</el-radio>
              </el-radio-group>
            </div>
          </div>

          <el-divider />

          <div class="setting-item">
            <div class="setting-label">
              <h4>语言设置</h4>
              <p>选择界面显示语言</p>
            </div>
            <div class="setting-control">
              <el-select v-model="settings.language" @change="handleLanguageChange">
                <el-option label="简体中文" value="zh-CN" />
                <el-option label="English" value="en-US" />
              </el-select>
            </div>
          </div>

          <el-divider />

          <div class="setting-item">
            <div class="setting-label">
              <h4>紧凑模式</h4>
              <p>启用紧凑模式以显示更多内容</p>
            </div>
            <div class="setting-control">
              <el-switch 
                v-model="settings.compactMode" 
                @change="handleCompactModeChange"
              />
            </div>
          </div>
        </div>
      </el-card>

      <!-- 通知设置 -->
      <el-card class="settings-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <el-icon><Bell /></el-icon>
            <span>通知设置</span>
          </div>
        </template>

        <div class="settings-section">
          <div class="setting-item">
            <div class="setting-label">
              <h4>桌面通知</h4>
              <p>允许系统发送桌面通知</p>
            </div>
            <div class="setting-control">
              <el-switch 
                v-model="settings.desktopNotifications" 
                @change="handleNotificationChange"
              />
            </div>
          </div>

          <el-divider />

          <div class="setting-item">
            <div class="setting-label">
              <h4>错误日志通知</h4>
              <p>当出现错误日志时发送通知</p>
            </div>
            <div class="setting-control">
              <el-switch 
                v-model="settings.errorNotifications" 
                @change="handleErrorNotificationChange"
              />
            </div>
          </div>

          <el-divider />

          <div class="setting-item">
            <div class="setting-label">
              <h4>通知声音</h4>
              <p>通知时播放提示音</p>
            </div>
            <div class="setting-control">
              <el-switch 
                v-model="settings.notificationSound" 
                @change="handleSoundChange"
              />
            </div>
          </div>
        </div>
      </el-card>

      <!-- 数据设置 -->
      <el-card class="settings-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <el-icon><DataBoard /></el-icon>
            <span>数据设置</span>
          </div>
        </template>

        <div class="settings-section">
          <div class="setting-item">
            <div class="setting-label">
              <h4>自动刷新</h4>
              <p>设置数据自动刷新间隔</p>
            </div>
            <div class="setting-control">
              <el-select v-model="settings.autoRefresh" @change="handleAutoRefreshChange">
                <el-option label="关闭" :value="0" />
                <el-option label="30秒" :value="30" />
                <el-option label="1分钟" :value="60" />
                <el-option label="5分钟" :value="300" />
                <el-option label="10分钟" :value="600" />
              </el-select>
            </div>
          </div>

          <el-divider />

          <div class="setting-item">
            <div class="setting-label">
              <h4>每页显示条数</h4>
              <p>设置列表页面默认显示的记录数</p>
            </div>
            <div class="setting-control">
              <el-select v-model="settings.pageSize" @change="handlePageSizeChange">
                <el-option label="10条" :value="10" />
                <el-option label="20条" :value="20" />
                <el-option label="50条" :value="50" />
                <el-option label="100条" :value="100" />
              </el-select>
            </div>
          </div>

          <el-divider />

          <div class="setting-item">
            <div class="setting-label">
              <h4>时间格式</h4>
              <p>选择时间显示格式</p>
            </div>
            <div class="setting-control">
              <el-radio-group v-model="settings.timeFormat" @change="handleTimeFormatChange">
                <el-radio value="12h">12小时制</el-radio>
                <el-radio value="24h">24小时制</el-radio>
              </el-radio-group>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 安全设置 -->
      <el-card class="settings-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <el-icon><Lock /></el-icon>
            <span>安全设置</span>
          </div>
        </template>

        <div class="settings-section">
          <div class="setting-item">
            <div class="setting-label">
              <h4>会话超时</h4>
              <p>设置登录会话的超时时间</p>
            </div>
            <div class="setting-control">
              <el-select v-model="settings.sessionTimeout" @change="handleSessionTimeoutChange">
                <el-option label="30分钟" :value="30" />
                <el-option label="1小时" :value="60" />
                <el-option label="2小时" :value="120" />
                <el-option label="4小时" :value="240" />
                <el-option label="8小时" :value="480" />
              </el-select>
            </div>
          </div>

          <el-divider />

          <div class="setting-item">
            <div class="setting-label">
              <h4>记住登录状态</h4>
              <p>下次访问时自动登录</p>
            </div>
            <div class="setting-control">
              <el-switch 
                v-model="settings.rememberLogin" 
                @change="handleRememberLoginChange"
              />
            </div>
          </div>
        </div>
      </el-card>

      <!-- 操作按钮 -->
      <div class="settings-actions">
        <el-button type="primary" @click="saveSettings" :loading="saving">
          保存设置
        </el-button>
        <el-button @click="resetSettings">
          恢复默认
        </el-button>
        <el-button type="danger" @click="clearCache">
          清除缓存
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Monitor, Bell, DataBoard, Lock } from '@element-plus/icons-vue'

// 响应式数据
const saving = ref(false)

// 设置数据
const settings = reactive({
  // 界面设置
  theme: 'light',
  language: 'zh-CN',
  compactMode: false,
  
  // 通知设置
  desktopNotifications: true,
  errorNotifications: true,
  notificationSound: true,
  
  // 数据设置
  autoRefresh: 60,
  pageSize: 20,
  timeFormat: '24h',
  
  // 安全设置
  sessionTimeout: 120,
  rememberLogin: true
})

// 默认设置
const defaultSettings = {
  theme: 'light',
  language: 'zh-CN',
  compactMode: false,
  desktopNotifications: true,
  errorNotifications: true,
  notificationSound: true,
  autoRefresh: 60,
  pageSize: 20,
  timeFormat: '24h',
  sessionTimeout: 120,
  rememberLogin: true
}

// 处理主题变更
const handleThemeChange = (value) => {
  ElMessage.info(`主题已切换为: ${value === 'light' ? '浅色' : value === 'dark' ? '深色' : '跟随系统'}`)
}

// 处理语言变更
const handleLanguageChange = (value) => {
  ElMessage.info(`语言已切换为: ${value === 'zh-CN' ? '简体中文' : 'English'}`)
}

// 处理紧凑模式变更
const handleCompactModeChange = (value) => {
  ElMessage.info(`紧凑模式已${value ? '启用' : '关闭'}`)
}

// 处理通知设置变更
const handleNotificationChange = (value) => {
  if (value && 'Notification' in window) {
    Notification.requestPermission().then(permission => {
      if (permission === 'granted') {
        ElMessage.success('桌面通知已启用')
      } else {
        settings.desktopNotifications = false
        ElMessage.warning('请在浏览器设置中允许通知权限')
      }
    })
  } else if (!value) {
    ElMessage.info('桌面通知已关闭')
  }
}

// 处理错误通知变更
const handleErrorNotificationChange = (value) => {
  ElMessage.info(`错误日志通知已${value ? '启用' : '关闭'}`)
}

// 处理通知声音变更
const handleSoundChange = (value) => {
  ElMessage.info(`通知声音已${value ? '启用' : '关闭'}`)
}

// 处理自动刷新变更
const handleAutoRefreshChange = (value) => {
  const text = value === 0 ? '关闭' : `${value < 60 ? value + '秒' : value / 60 + '分钟'}`
  ElMessage.info(`自动刷新间隔设置为: ${text}`)
}

// 处理每页条数变更
const handlePageSizeChange = (value) => {
  ElMessage.info(`每页显示条数设置为: ${value}条`)
}

// 处理时间格式变更
const handleTimeFormatChange = (value) => {
  ElMessage.info(`时间格式设置为: ${value === '12h' ? '12小时制' : '24小时制'}`)
}

// 处理会话超时变更
const handleSessionTimeoutChange = (value) => {
  const text = value < 60 ? `${value}分钟` : `${value / 60}小时`
  ElMessage.info(`会话超时时间设置为: ${text}`)
}

// 处理记住登录变更
const handleRememberLoginChange = (value) => {
  ElMessage.info(`记住登录状态已${value ? '启用' : '关闭'}`)
}

// 保存设置
const saveSettings = async () => {
  try {
    saving.value = true
    
    // 保存到localStorage
    localStorage.setItem('userSettings', JSON.stringify(settings))
    
    // 这里可以调用API保存到后端
    // await settingsApi.saveSettings(settings)
    
    ElMessage.success('设置保存成功')
  } catch (error) {
    console.error('保存设置失败:', error)
    ElMessage.error('保存设置失败')
  } finally {
    saving.value = false
  }
}

// 恢复默认设置
const resetSettings = async () => {
  try {
    await ElMessageBox.confirm('确定要恢复默认设置吗？', '确认操作', {
      type: 'warning'
    })
    
    Object.assign(settings, defaultSettings)
    ElMessage.success('已恢复默认设置')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('恢复默认设置失败')
    }
  }
}

// 清除缓存
const clearCache = async () => {
  try {
    await ElMessageBox.confirm('确定要清除所有缓存数据吗？', '确认操作', {
      type: 'warning'
    })
    
    // 清除localStorage中的缓存数据（除了认证信息）
    const authToken = localStorage.getItem('auth_token')
    const authUser = localStorage.getItem('auth_user')
    
    localStorage.clear()
    
    // 恢复认证信息
    if (authToken) localStorage.setItem('auth_token', authToken)
    if (authUser) localStorage.setItem('auth_user', authUser)
    
    ElMessage.success('缓存清除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('清除缓存失败')
    }
  }
}

// 加载设置
const loadSettings = () => {
  try {
    const savedSettings = localStorage.getItem('userSettings')
    if (savedSettings) {
      const parsed = JSON.parse(savedSettings)
      Object.assign(settings, { ...defaultSettings, ...parsed })
    }
  } catch (error) {
    console.error('加载设置失败:', error)
    Object.assign(settings, defaultSettings)
  }
}

// 组件挂载时加载设置
onMounted(() => {
  loadSettings()
})
</script>

<style scoped>
.settings-container {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.settings-header {
  text-align: center;
  margin-bottom: 30px;
}

.settings-header h1 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
  color: #303133;
}

.settings-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.settings-content {
  display: grid;
  gap: 24px;
}

.settings-card {
  border-radius: 12px;
  overflow: hidden;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #303133;
}

.settings-section {
  padding: 8px 0;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
}

.setting-label {
  flex: 1;
  margin-right: 24px;
}

.setting-label h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.setting-label p {
  margin: 0;
  font-size: 14px;
  color: #606266;
  line-height: 1.4;
}

.setting-control {
  flex-shrink: 0;
}

.settings-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding: 24px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .settings-container {
    padding: 15px;
  }

  .setting-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .setting-label {
    margin-right: 0;
  }

  .setting-control {
    width: 100%;
  }

  .settings-actions {
    flex-direction: column;
  }
}
</style>
