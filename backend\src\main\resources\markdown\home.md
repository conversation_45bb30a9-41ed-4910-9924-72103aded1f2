# 通用日志管理系统 API 文档

欢迎使用通用日志管理系统的API文档！

## 系统简介

通用日志管理系统是一个基于Spring Boot和MongoDB的现代化日志收集、存储和查询系统。系统提供了完整的用户认证、应用管理和日志管理功能。

## 主要功能

### 🔐 认证管理
- **用户登录/登出**: 支持用户名/邮箱登录，JWT令牌认证
- **令牌管理**: 令牌验证、刷新，24小时有效期
- **用户信息**: 获取当前登录用户详细信息
- **安全特性**: BCrypt密码加密，无状态认证

### 📱 应用管理
- **应用CRUD**: 创建、查询、更新、删除应用
- **Token管理**: 每个应用独有Token，支持重新生成
- **权限控制**: 基于用户角色的应用访问控制
- **状态管理**: 应用启用/禁用状态管理

### 📊 日志管理
- **日志CRUD**: 单条/批量日志的增删改查
- **高级搜索**: 支持时间范围、级别、关键词等多条件搜索
- **统计分析**: 日志数量统计、级别分布分析
- **数据维护**: 过期日志清理、批量操作

### 👥 用户管理
- **用户CRUD**: 用户账户的完整生命周期管理
- **权限分配**: 应用访问权限分配
- **角色管理**: 超级管理员、管理员、普通用户
- **密码管理**: 密码修改、强度验证

### ⚙️ 系统配置
- **配置查询**: 获取系统配置信息
- **健康检查**: 系统状态监控
- **分页配置**: 分页参数配置信息

## 快速开始

### 1. 获取认证令牌

```bash
POST /api/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "123456"
}
```

### 2. 使用令牌访问API

```bash
GET /api/logs
Authorization: Bearer <your-jwt-token>
```

### 3. 创建应用

```bash
POST /api/applications
Authorization: Bearer <your-jwt-token>
Content-Type: application/json

{
  "name": "my-app",
  "description": "我的应用",
  "environment": "prod"
}
```

### 4. 推送日志

```bash
POST /api/logs
Authorization: Bearer <your-jwt-token>
Content-Type: application/json

{
  "level": "INFO",
  "message": "用户登录成功",
  "source": "com.example.UserService",
  "applicationName": "my-app"
}
```

## 认证说明

### JWT令牌格式
```
Authorization: Bearer <jwt-token>
```

### 令牌有效期
- 默认有效期：24小时
- 支持令牌刷新
- 无状态认证，服务端不存储会话

### 权限级别
- **超级管理员**: 所有权限
- **管理员**: 用户和应用管理权限
- **普通用户**: 只能访问授权的应用

## 响应格式

所有API都返回统一的响应格式：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": { ... },
  "timestamp": 1640995200000
}
```

### 状态码说明
- **200**: 操作成功
- **400**: 请求参数错误
- **401**: 未授权访问
- **404**: 资源不存在
- **500**: 服务器内部错误

## 分页说明

### 分页参数
- `page`: 页码，从1开始
- `size`: 每页大小，默认10，最大100
- `sort`: 排序字段
- `order`: 排序方向，asc/desc

### 分页响应
```json
{
  "content": [...],
  "totalElements": 100,
  "totalPages": 10,
  "size": 10,
  "number": 0
}
```

## 环境说明

- **开发环境**: `dev` - 用于开发测试
- **测试环境**: `test` - 用于集成测试
- **生产环境**: `prod` - 生产环境

## 错误处理

### 常见错误
- **认证失败**: 检查令牌是否有效
- **权限不足**: 检查用户角色和权限
- **参数错误**: 检查请求参数格式
- **资源不存在**: 检查资源ID是否正确

### 调试建议
1. 使用健康检查接口确认服务状态
2. 检查认证令牌是否正确携带
3. 查看响应消息获取详细错误信息
4. 使用系统配置接口获取配置信息

## 联系我们

如有问题，请联系开发团队。

---

*本文档由Knife4j自动生成 - Logger Management System v1.0.0*
