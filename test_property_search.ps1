# PowerShell脚本测试属性搜索功能

$baseUrl = "http://localhost:8080/api"

# 登录获取token
function Get-AuthToken {
    $loginData = @{
        username = "admin"
        password = "admin123"
    } | ConvertTo-Json

    try {
        $response = Invoke-RestMethod -Uri "$baseUrl/auth/login" -Method Post -ContentType "application/json" -Body $loginData
        if ($response.code -eq 200) {
            return $response.data.token
        } else {
            Write-Host "登录失败: $($response.message)" -ForegroundColor Red
            return $null
        }
    } catch {
        Write-Host "登录请求失败: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

# 测试属性搜索
function Test-PropertySearch {
    param(
        [string]$token,
        [string]$searchTerm,
        [string]$propertyType = "all"
    )

    $headers = @{
        "Authorization" = "Bearer $token"
        "Content-Type" = "application/json"
    }

    $params = @{
        page = 1
        size = 5
        propertySearch = $searchTerm
        propertyType = $propertyType
    }

    Write-Host "`n=== 测试属性搜索: '$searchTerm' (类型: $propertyType) ===" -ForegroundColor Yellow

    try {
        $response = Invoke-RestMethod -Uri "$baseUrl/logs" -Method Get -Headers $headers -Body $params
        
        if ($response.code -eq 200) {
            $total = $response.data.total
            $logs = $response.data.content
            
            Write-Host "找到 $total 条匹配的日志" -ForegroundColor Green
            
            for ($i = 0; $i -lt $logs.Count; $i++) {
                $log = $logs[$i]
                Write-Host "`n--- 日志 $($i + 1) ---" -ForegroundColor Cyan
                Write-Host "ID: $($log.id)"
                Write-Host "消息: $($log.message)"
                Write-Host "时间: $($log.timestamp)"
                
                if ($log.extendProperties) {
                    Write-Host "扩展属性: $($log.extendProperties | ConvertTo-Json -Compress)" -ForegroundColor Blue
                }
                if ($log.environmentProperties) {
                    Write-Host "环境属性: $($log.environmentProperties | ConvertTo-Json -Compress)" -ForegroundColor Blue
                }
                if ($log.metadata) {
                    Write-Host "元数据: $($log.metadata | ConvertTo-Json -Compress)" -ForegroundColor Blue
                }
            }
        } else {
            Write-Host "API返回错误: $($response.message)" -ForegroundColor Red
        }
    } catch {
        Write-Host "请求失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 主程序
Write-Host "开始测试属性搜索功能..." -ForegroundColor Green

# 登录
$token = Get-AuthToken
if (-not $token) {
    Write-Host "无法获取token，退出测试" -ForegroundColor Red
    exit 1
}

Write-Host "登录成功，开始测试..." -ForegroundColor Green

# 测试不同的搜索条件
$testCases = @(
    @{ searchTerm = "1.0.0"; propertyType = "all" },           # 搜索版本号
    @{ searchTerm = "development"; propertyType = "extend" },   # 在扩展属性中搜索
    @{ searchTerm = "GET"; propertyType = "metadata" },        # 在元数据中搜索HTTP方法
    @{ searchTerm = "Thread"; propertyType = "all" },          # 搜索线程相关信息
    @{ searchTerm = "localhost"; propertyType = "metadata" },  # 搜索URL中的localhost
    @{ searchTerm = "category"; propertyType = "extend" },     # 搜索category键
    @{ searchTerm = "Business"; propertyType = "extend" }      # 搜索Business值
)

foreach ($testCase in $testCases) {
    Test-PropertySearch -token $token -searchTerm $testCase.searchTerm -propertyType $testCase.propertyType
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
