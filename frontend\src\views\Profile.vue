<template>
  <div class="profile-container">
    <div class="profile-header">
      <h1>个人资料</h1>
      <p>管理您的个人信息和账户设置</p>
    </div>

    <div class="profile-content">
      <!-- 基本信息卡片 -->
      <el-card class="profile-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <el-icon><User /></el-icon>
            <span>基本信息</span>
          </div>
        </template>

        <div class="profile-info">
          <div class="avatar-section">
            <el-avatar :size="80" :src="userInfo.avatar">
              <el-icon><User /></el-icon>
            </el-avatar>
            <!-- 暂时隐藏头像上传功能 -->
            <!-- <el-button type="primary" size="small" @click="handleAvatarUpload" class="upload-btn">
              更换头像
            </el-button> -->
          </div>

          <div class="info-section">
            <el-form :model="userInfo" :rules="rules" ref="profileFormRef" label-width="100px">
              <el-form-item label="用户名">
                <el-input v-model="userInfo.username" disabled />
              </el-form-item>

              <el-form-item label="邮箱" prop="email">
                <el-input 
                  v-model="userInfo.email" 
                  :disabled="!editMode"
                  placeholder="请输入邮箱"
                />
              </el-form-item>

              <el-form-item label="真实姓名" prop="realName">
                <el-input 
                  v-model="userInfo.realName" 
                  :disabled="!editMode"
                  placeholder="请输入真实姓名"
                />
              </el-form-item>

              <el-form-item label="角色">
                <el-tag :type="getRoleTagType(userInfo.role)">
                  {{ getRoleDisplayName(userInfo.role) }}
                </el-tag>
              </el-form-item>

              <el-form-item label="状态">
                <el-tag :type="userInfo.status === 'ACTIVE' ? 'success' : 'danger'">
                  {{ userInfo.status === 'ACTIVE' ? '正常' : '禁用' }}
                </el-tag>
              </el-form-item>

              <el-form-item label="注册时间">
                <span>{{ formatTime(userInfo.createdAt) }}</span>
              </el-form-item>

              <el-form-item>
                <el-button v-if="!editMode" type="primary" @click="enableEdit">
                  编辑资料
                </el-button>
                <template v-else>
                  <el-button type="primary" @click="saveProfile" :loading="saving">
                    保存
                  </el-button>
                  <el-button @click="cancelEdit">
                    取消
                  </el-button>
                </template>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </el-card>

      <!-- 修改密码卡片 -->
      <el-card class="profile-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <el-icon><Lock /></el-icon>
            <span>修改密码</span>
          </div>
        </template>

        <el-form :model="passwordForm" :rules="passwordRules" ref="passwordFormRef" label-width="100px">
          <el-form-item label="当前密码" prop="currentPassword">
            <el-input 
              v-model="passwordForm.currentPassword" 
              type="password" 
              placeholder="请输入当前密码"
              show-password
            />
          </el-form-item>

          <el-form-item label="新密码" prop="newPassword">
            <el-input 
              v-model="passwordForm.newPassword" 
              type="password" 
              placeholder="请输入新密码"
              show-password
            />
          </el-form-item>

          <el-form-item label="确认密码" prop="confirmPassword">
            <el-input 
              v-model="passwordForm.confirmPassword" 
              type="password" 
              placeholder="请再次输入新密码"
              show-password
            />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="changePassword" :loading="changingPassword">
              修改密码
            </el-button>
            <el-button @click="resetPasswordForm">
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 授权应用卡片（非管理员用户） -->
      <el-card v-if="!authStore.isAdmin" class="profile-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <el-icon><Grid /></el-icon>
            <span>授权应用</span>
          </div>
        </template>

        <div class="authorized-apps" v-loading="appsLoading">
          <div v-if="!appsLoading && authorizedApps.length === 0" class="no-apps">
            <el-empty description="暂无授权应用" />
          </div>
          <div v-else-if="!appsLoading" class="apps-grid">
            <div v-for="app in authorizedApps" :key="app.id" class="app-item">
              <el-icon class="app-icon"><Grid /></el-icon>
              <div class="app-info">
                <h4>{{ app.name }}</h4>
                <p>{{ app.description || '暂无描述' }}</p>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { User, Lock, Grid } from '@element-plus/icons-vue'
import { useAuthStore } from '../stores/auth'
import { userApi } from '../api/users'
import { formatTime } from '../utils/dateTime'

const authStore = useAuthStore()

// 响应式数据
const editMode = ref(false)
const saving = ref(false)
const changingPassword = ref(false)
const appsLoading = ref(false)
const profileFormRef = ref()
const passwordFormRef = ref()

// 用户信息
const userInfo = reactive({
  id: '',
  username: '',
  email: '',
  realName: '',
  role: '',
  status: '',
  avatar: '',
  createdAt: ''
})

// 原始用户信息（用于取消编辑时恢复）
const originalUserInfo = reactive({})

// 密码表单
const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 授权应用列表
const authorizedApps = ref([])

// 表单验证规则
const rules = {
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '真实姓名长度在 2 到 20 个字符', trigger: 'blur' }
  ]
}

const passwordRules = {
  currentPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 获取角色标签类型
const getRoleTagType = (role) => {
  const typeMap = {
    ADMIN: 'danger',
    DEVELOPER: 'warning',
    USER: 'info'
  }
  return typeMap[role] || 'info'
}

// 获取角色显示名称
const getRoleDisplayName = (role) => {
  const nameMap = {
    ADMIN: '管理员',
    DEVELOPER: '开发者',
    USER: '普通用户'
  }
  return nameMap[role] || role
}



// 启用编辑模式
const enableEdit = () => {
  Object.assign(originalUserInfo, userInfo)
  editMode.value = true
}

// 取消编辑
const cancelEdit = () => {
  Object.assign(userInfo, originalUserInfo)
  editMode.value = false
}

// 保存个人资料
const saveProfile = async () => {
  try {
    await profileFormRef.value.validate()
    
    saving.value = true
    
    const updateData = {
      email: userInfo.email,
      realName: userInfo.realName
    }
    
    const response = await userApi.updateUser(userInfo.id, updateData)
    
    if (response.code === 200) {
      ElMessage.success('个人资料更新成功')
      editMode.value = false
      
      // 更新store中的用户信息
      await authStore.getCurrentUser()
    } else {
      throw new Error(response.message || '更新失败')
    }
  } catch (error) {
    console.error('保存个人资料失败:', error)
    ElMessage.error('保存个人资料失败，请检查后端服务是否正常运行')
  } finally {
    saving.value = false
  }
}

// 修改密码
const changePassword = async () => {
  try {
    await passwordFormRef.value.validate()

    changingPassword.value = true

    const response = await userApi.changePassword({
      currentPassword: passwordForm.currentPassword,
      newPassword: passwordForm.newPassword
    })

    if (response.code === 200) {
      ElMessage.success('密码修改成功')
      resetPasswordForm()
    } else {
      throw new Error(response.message || '修改密码失败')
    }
  } catch (error) {
    console.error('修改密码失败:', error)
    ElMessage.error('修改密码失败，请检查当前密码是否正确')
  } finally {
    changingPassword.value = false
  }
}

// 重置密码表单
const resetPasswordForm = () => {
  Object.assign(passwordForm, {
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  })
  passwordFormRef.value?.clearValidate()
}

// 处理头像上传 - 暂时隐藏
// const handleAvatarUpload = () => {
//   ElMessage.info('头像上传功能开发中...')
// }

// 加载用户信息
const loadUserInfo = () => {
  if (authStore.user) {
    Object.assign(userInfo, {
      id: authStore.user.id,
      username: authStore.user.username,
      email: authStore.user.email || '',
      realName: authStore.user.realName || '',
      role: authStore.user.role,
      status: authStore.user.status || 'ACTIVE',
      avatar: authStore.user.avatar || '',
      createdAt: authStore.user.createdAt || ''
    })
  } else {
    console.warn('authStore.user 为空，无法加载用户信息')
  }
}

// 加载授权应用
const loadAuthorizedApps = async () => {
  if (!authStore.isAdmin && authStore.user?.authorizedAppIds) {
    try {
      appsLoading.value = true
      const appIds = authStore.user.authorizedAppIds || []

      if (appIds.length === 0) {
        authorizedApps.value = []
        return
      }

      // 动态导入应用API
      const { applicationApi } = await import('../api/applications')

      // 获取所有应用列表
      const response = await applicationApi.getApplications({ page: 1, size: 100 })

      if (response.code === 200 && response.data) {
        let allApps = response.data.content || response.data || []

        // 过滤出用户有权限的应用
        const userAuthorizedApps = allApps.filter(app => appIds.includes(app.id))
        authorizedApps.value = userAuthorizedApps

        if (userAuthorizedApps.length === 0) {
          ElMessage.warning('未找到匹配的授权应用，可能应用已被删除')
        }
      } else {
        console.warn('获取应用列表失败:', response.message)
        ElMessage.warning('获取应用列表失败，请检查后端服务')
        authorizedApps.value = []
      }
    } catch (error) {
      console.error('加载授权应用失败:', error)
      ElMessage.error('加载授权应用失败，请检查后端服务是否正常运行')

      // 如果API调用失败，显示基本信息
      const appIds = authStore.user.authorizedAppIds || []
      authorizedApps.value = appIds.map(id => ({
        id,
        name: `应用 ${id}`,
        description: '无法获取应用详情，请检查后端服务'
      }))
    } finally {
      appsLoading.value = false
    }
  } else {
    authorizedApps.value = []
    appsLoading.value = false
  }
}

// 监听用户信息变化
watch(() => authStore.user, (newUser) => {
  if (newUser) {
    loadUserInfo()
    loadAuthorizedApps()
  }
}, { immediate: false })

// 组件挂载时加载数据
onMounted(() => {
  loadUserInfo()
  loadAuthorizedApps()
})
</script>

<style scoped>
.profile-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.profile-header {
  text-align: center;
  margin-bottom: 30px;
}

.profile-header h1 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
  color: #303133;
}

.profile-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.profile-content {
  display: grid;
  gap: 24px;
}

.profile-card {
  border-radius: 12px;
  overflow: hidden;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #303133;
}

.profile-info {
  display: flex;
  gap: 32px;
  align-items: flex-start;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  flex-shrink: 0;
}

/* 暂时隐藏头像上传按钮样式 */
/* .upload-btn {
  margin-top: 8px;
} */

.info-section {
  flex: 1;
}

.authorized-apps {
  min-height: 200px;
}

.no-apps {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.apps-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.app-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.app-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.app-icon {
  font-size: 24px;
  color: #409eff;
  flex-shrink: 0;
}

.app-info h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.app-info p {
  margin: 0;
  font-size: 14px;
  color: #606266;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .profile-container {
    padding: 15px;
  }

  .profile-info {
    flex-direction: column;
    gap: 24px;
  }

  .apps-grid {
    grid-template-columns: 1fr;
  }
}
</style>
