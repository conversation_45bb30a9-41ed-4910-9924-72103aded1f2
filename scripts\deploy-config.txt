# 通用日志管理系统 - 部署配置文件示例
# 复制此文件为 deploy-config.txt 并根据实际情况修改配置

# ===========================================
# Linux服务器连接配置
# ===========================================

# 服务器IP地址或域名 (必填)
SERVER_HOST=************

# SSH用户名 (必填)
SERVER_USER=root

# SSH端口 (默认22)
SERVER_PORT=22

# 部署目录 (在Linux服务器上的目录)
DEPLOY_PATH=/home/<USER>

# SSH密钥路径 (可选，如果使用密钥认证)
# 如果不设置，将使用密码认证
# SSH_KEY_PATH=C:\Users\<USER>\.ssh\id_rsa

# ===========================================
# 部署选项配置
# ===========================================

# 自动备份现有部署 (true/false)
AUTO_BACKUP=true

# 备份保留数量
BACKUP_KEEP_COUNT=3

# 部署后自动启动服务 (true/false)
AUTO_START=true

# 部署超时时间 (秒)
DEPLOY_TIMEOUT=600

# ===========================================
# 应用配置
# ===========================================

# 前端端口
FRONTEND_PORT=80

# 后端端口
BACKEND_PORT=8080

# MongoDB端口 (如果使用内置MongoDB)
MONGODB_PORT=27017

# ===========================================
# 外部MongoDB配置 (如果使用外部MongoDB)
# ===========================================

# 是否使用外部MongoDB (true/false)
USE_EXTERNAL_MONGODB=true

# 外部MongoDB主机
EXTERNAL_MONGODB_HOST=************

# 外部MongoDB端口
EXTERNAL_MONGODB_PORT=27017

# 外部MongoDB数据库名
EXTERNAL_MONGODB_DATABASE=logger_management

# 外部MongoDB用户名
EXTERNAL_MONGODB_USERNAME=mongo_5MySKx

# 外部MongoDB密码
EXTERNAL_MONGODB_PASSWORD=mongo_b2H8yD

# ===========================================
# 安全配置
# ===========================================

# JWT密钥 (生产环境请使用强密钥)
JWT_SECRET=YourSecureJWTSecretKeyHere2024!@#$%^&*()

# 允许的CORS来源 (生产环境请指定具体域名)
CORS_ALLOWED_ORIGINS=*

# ===========================================
# 通知配置 (可选)
# ===========================================

# 部署完成后发送通知 (true/false)
SEND_NOTIFICATION=false

# 通知邮箱
NOTIFICATION_EMAIL=<EMAIL>

# 通知Webhook URL
NOTIFICATION_WEBHOOK=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK

# ===========================================
# 示例配置 (请根据实际情况修改)
# ===========================================

# 示例1: 使用密码认证的基本配置
# SERVER_HOST=*************
# SERVER_USER=ubuntu
# SERVER_PORT=22
# DEPLOY_PATH=/home/<USER>/logger-management

# 示例2: 使用SSH密钥认证
# SERVER_HOST=your-server.com
# SERVER_USER=deploy
# SERVER_PORT=2222
# DEPLOY_PATH=/opt/logger-management
# SSH_KEY_PATH=C:\Users\<USER>\.ssh\deploy_key

# 示例3: 使用外部MongoDB的生产环境配置
# SERVER_HOST=prod-server.yourcompany.com
# SERVER_USER=deploy
# DEPLOY_PATH=/opt/logger-management
# SSH_KEY_PATH=C:\Users\<USER>\.ssh\prod_key
# USE_EXTERNAL_MONGODB=true
# EXTERNAL_MONGODB_HOST=mongodb-cluster.yourcompany.com
# EXTERNAL_MONGODB_PORT=27017
# EXTERNAL_MONGODB_DATABASE=logger_management_prod
# EXTERNAL_MONGODB_USERNAME=app_user
# EXTERNAL_MONGODB_PASSWORD=SecurePassword123
# JWT_SECRET=ProductionSecureJWTKey2024!@#$%^&*()
# CORS_ALLOWED_ORIGINS=https://logs.yourcompany.com
# FRONTEND_PORT=80
# BACKEND_PORT=8080
