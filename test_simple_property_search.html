<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>属性搜索测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; }
        .error { color: red; }
        .success { color: green; }
        button { margin: 5px; padding: 10px; }
    </style>
</head>
<body>
    <h1>属性搜索功能测试</h1>
    
    <div class="test-section">
        <h3>登录状态</h3>
        <button onclick="login()">登录</button>
        <div id="loginResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>测试属性搜索</h3>
        <div>
            <label>搜索词: </label>
            <input type="text" id="searchTerm" value="1.0.0" placeholder="输入搜索词">
        </div>
        <div>
            <label>属性类型: </label>
            <select id="propertyType">
                <option value="all">全部</option>
                <option value="extend">扩展属性</option>
                <option value="environment">环境属性</option>
                <option value="metadata">元数据</option>
            </select>
        </div>
        <button onclick="testPropertySearch()">测试搜索</button>
        <div id="searchResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>快速测试</h3>
        <button onclick="quickTest('1.0.0', 'extend')">测试扩展属性中的版本号</button>
        <button onclick="quickTest('GET', 'metadata')">测试元数据中的HTTP方法</button>
        <button onclick="quickTest('Business', 'extend')">测试扩展属性中的Business</button>
        <button onclick="quickTest('localhost', 'metadata')">测试元数据中的localhost</button>
    </div>

    <script>
        let authToken = '';
        const baseUrl = 'http://1ocalhost:8080/api';

        async function login() {
            try {
                const response = await fetch(`${baseUrl}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: '123456'
                    })
                });

                const result = await response.json();
                if (result.code === 200) {
                    authToken = result.data.token;
                    document.getElementById('loginResult').innerHTML = 
                        '<span class="success">登录成功！Token已获取</span>';
                } else {
                    document.getElementById('loginResult').innerHTML = 
                        `<span class="error">登录失败: ${result.message}</span>`;
                }
            } catch (error) {
                document.getElementById('loginResult').innerHTML = 
                    `<span class="error">登录请求失败: ${error.message}</span>`;
            }
        }

        async function testPropertySearch() {
            if (!authToken) {
                alert('请先登录');
                return;
            }

            const searchTerm = document.getElementById('searchTerm').value;
            const propertyType = document.getElementById('propertyType').value;

            await performSearch(searchTerm, propertyType, 'searchResult');
        }

        async function quickTest(searchTerm, propertyType) {
            if (!authToken) {
                alert('请先登录');
                return;
            }

            await performSearch(searchTerm, propertyType, 'searchResult');
        }

        async function performSearch(searchTerm, propertyType, resultElementId) {
            try {
                const params = new URLSearchParams({
                    page: 1,
                    size: 5,
                    propertySearch: searchTerm,
                    propertyType: propertyType
                });

                const response = await fetch(`${baseUrl}/logs?${params}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();
                
                let html = `<h4>搜索: "${searchTerm}" (类型: ${propertyType})</h4>`;
                
                if (result.code === 200) {
                    const total = result.data.total;
                    const logs = result.data.content;
                    
                    html += `<p class="success">找到 ${total} 条匹配的日志</p>`;
                    
                    logs.forEach((log, index) => {
                        html += `<div style="border: 1px solid #ddd; margin: 5px 0; padding: 10px;">`;
                        html += `<strong>日志 ${index + 1}:</strong><br>`;
                        html += `ID: ${log.id}<br>`;
                        html += `消息: ${log.message}<br>`;
                        html += `时间: ${log.timestamp}<br>`;
                        
                        if (log.extendProperties) {
                            html += `<strong>扩展属性:</strong> ${JSON.stringify(log.extendProperties)}<br>`;
                        }
                        if (log.environmentProperties) {
                            html += `<strong>环境属性:</strong> ${JSON.stringify(log.environmentProperties)}<br>`;
                        }
                        if (log.metadata) {
                            html += `<strong>元数据:</strong> ${JSON.stringify(log.metadata)}<br>`;
                        }
                        html += `</div>`;
                    });
                } else {
                    html += `<span class="error">API返回错误: ${result.message}</span>`;
                }

                document.getElementById(resultElementId).innerHTML = html;
            } catch (error) {
                document.getElementById(resultElementId).innerHTML = 
                    `<span class="error">请求失败: ${error.message}</span>`;
            }
        }

        // 页面加载时自动登录
        window.onload = function() {
            login();
        };
    </script>
</body>
</html>
