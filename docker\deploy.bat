@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: 通用日志管理系统 Docker 部署脚本 (Windows)
:: 作者: Logger Management System
:: 版本: 1.0.0

echo ==========================================
echo 通用日志管理系统 Docker 部署脚本
echo ==========================================

:: 检查Docker和Docker Compose
echo [INFO] 检查系统要求...
docker --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker 未安装，请先安装 Docker Desktop
    pause
    exit /b 1
)

docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker Compose 未安装，请先安装 Docker Compose
    pause
    exit /b 1
)

echo [SUCCESS] 系统要求检查通过

:: 构建镜像
echo [INFO] 构建应用镜像...
echo [INFO] 构建后端镜像...
docker-compose build backend
if errorlevel 1 (
    echo [ERROR] 后端镜像构建失败
    pause
    exit /b 1
)

echo [INFO] 构建前端镜像...
docker-compose build frontend
if errorlevel 1 (
    echo [ERROR] 前端镜像构建失败
    pause
    exit /b 1
)

echo [SUCCESS] 镜像构建完成

:: 启动服务
echo [INFO] 启动服务...

echo [INFO] 启动 MongoDB...
docker-compose up -d mongodb
if errorlevel 1 (
    echo [ERROR] MongoDB 启动失败
    pause
    exit /b 1
)

echo [INFO] 等待 MongoDB 启动...
timeout /t 30 /nobreak >nul

echo [INFO] 启动后端服务...
docker-compose up -d backend
if errorlevel 1 (
    echo [ERROR] 后端服务启动失败
    pause
    exit /b 1
)

echo [INFO] 等待后端服务启动...
timeout /t 30 /nobreak >nul

echo [INFO] 启动前端服务...
docker-compose up -d frontend
if errorlevel 1 (
    echo [ERROR] 前端服务启动失败
    pause
    exit /b 1
)

echo [INFO] 启动 MongoDB 管理界面...
docker-compose up -d mongo-express

echo [SUCCESS] 所有服务启动完成

:: 检查服务状态
echo [INFO] 检查服务状态...
docker-compose ps

:: 显示访问信息
echo [SUCCESS] 部署完成！
echo.
echo ==========================================
echo 通用日志管理系统访问信息
echo ==========================================
echo 前端地址: http://localhost
echo 后端API: http://localhost:8080/api
echo MongoDB管理: http://localhost:8081
echo   用户名: admin
echo   密码: admin123
echo ==========================================
echo.
echo 常用命令:
echo   查看日志: docker-compose logs -f [service_name]
echo   停止服务: docker-compose down
echo   重启服务: docker-compose restart [service_name]
echo   查看状态: docker-compose ps
echo ==========================================

pause
