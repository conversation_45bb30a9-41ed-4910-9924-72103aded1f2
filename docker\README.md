# 通用日志管理系统 Docker 部署指南

## 概述

本文档介绍如何使用 Docker 和 Docker Compose 部署通用日志管理系统。该系统包含以下组件：

- **前端**: Vue3 + Element Plus + Nginx
- **后端**: Spring Boot + Java 8
- **数据库**: MongoDB 5.0
- **管理工具**: MongoDB Express (可选)

## 系统要求

### 硬件要求
- CPU: 2核心或以上
- 内存: 4GB 或以上
- 磁盘: 10GB 可用空间

### 软件要求
- Docker 20.10+ 
- Docker Compose 2.0+
- 操作系统: Linux/Windows/macOS

## 快速部署

### 场景一：全新部署（包含MongoDB）

#### 使用部署脚本（推荐）

##### Linux/macOS
```bash
cd docker
chmod +x deploy.sh
./deploy.sh
```

##### Windows
```cmd
cd docker
deploy.bat
```

#### 手动部署

1. **进入docker目录**
```bash
cd docker
```

2. **构建镜像**
```bash
docker-compose build
```

3. **启动服务**
```bash
docker-compose up -d
```

4. **检查服务状态**
```bash
docker-compose ps
```

### 场景二：使用外部MongoDB

如果您已经有现有的MongoDB服务器，可以使用外部MongoDB部署方案：

#### 1. 配置环境变量

```bash
cd docker
cp .env.external-mongo .env
```

编辑 `.env` 文件，配置您的MongoDB连接信息：
```bash
MONGODB_HOST=your-mongodb-host
MONGODB_PORT=27017
MONGODB_DATABASE=logger_management
MONGODB_USERNAME=logger_user
MONGODB_PASSWORD=logger_password
```

#### 2. 初始化MongoDB数据库

在您的MongoDB服务器中执行初始化脚本：
```bash
# 使用mongosh（MongoDB 5.0+）
mongosh "**********************************************" < init-external-mongo.js

# 或使用mongo（旧版本）
mongo "**********************************************" < init-external-mongo.js
```

#### 3. 部署应用

##### Linux/macOS
```bash
chmod +x deploy-external-mongo.sh
./deploy-external-mongo.sh
```

##### Windows
```cmd
deploy-external-mongo.bat
```

#### 4. 手动部署（外部MongoDB）

```bash
# 构建镜像
docker-compose -f docker-compose-external-mongo.yml --env-file .env build

# 启动服务
docker-compose -f docker-compose-external-mongo.yml --env-file .env up -d

# 检查状态
docker-compose -f docker-compose-external-mongo.yml ps
```

## 服务访问

部署完成后，可以通过以下地址访问各个服务：

| 服务 | 地址 | 说明 |
|------|------|------|
| 前端应用 | http://localhost | 主要的Web界面 |
| 后端API | http://localhost:8080/api | REST API接口 |
| MongoDB管理 | http://localhost:8081 | 数据库管理界面 |

### 默认账户信息

**MongoDB Express 管理界面**
- 用户名: `admin`
- 密码: `admin123`

**应用系统**
- 首次访问需要注册管理员账户

## 常用操作

### 查看服务状态
```bash
docker-compose ps
```

### 查看服务日志
```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f frontend
docker-compose logs -f backend
docker-compose logs -f mongodb
```

### 重启服务
```bash
# 重启所有服务
docker-compose restart

# 重启特定服务
docker-compose restart backend
```

### 停止服务
```bash
# 停止所有服务
docker-compose down

# 停止并删除数据卷（谨慎使用）
docker-compose down -v
```

### 更新应用
```bash
# 重新构建并启动
docker-compose build
docker-compose up -d
```

## 配置说明

### 端口配置

默认端口映射：
- 前端: 80 → 80
- 后端: 8080 → 8080  
- MongoDB: 27017 → 27017
- MongoDB Express: 8081 → 8081

如需修改端口，请编辑 `docker-compose.yml` 文件中的 `ports` 配置。

### 环境变量

主要环境变量配置在 `docker-compose.yml` 中：

```yaml
environment:
  - SPRING_PROFILES_ACTIVE=docker
  - MONGO_INITDB_ROOT_USERNAME=admin
  - MONGO_INITDB_ROOT_PASSWORD=password123
```

### 数据持久化

系统使用 Docker 卷来持久化数据：
- `mongodb_data`: MongoDB 数据文件
- `backend_logs`: 后端应用日志

## 故障排除

### 常见问题

1. **端口冲突**
   - 检查端口是否被占用: `netstat -tulpn | grep :80`
   - 修改 docker-compose.yml 中的端口映射

2. **内存不足**
   - 检查系统内存: `free -h`
   - 调整 Docker 内存限制

3. **服务启动失败**
   - 查看服务日志: `docker-compose logs [service_name]`
   - 检查配置文件语法

4. **数据库连接失败**
   - 确认 MongoDB 服务正常运行
   - 检查网络连接: `docker-compose exec backend ping mongodb`

### 健康检查

系统包含健康检查机制：

```bash
# 检查后端健康状态
curl http://localhost:8080/api/actuator/health

# 检查前端健康状态
curl http://localhost/
```

## 生产环境部署

### 安全配置

1. **修改默认密码**
   - 更改 MongoDB 管理员密码
   - 更改 MongoDB Express 访问密码

2. **配置HTTPS**
   - 使用反向代理（如 Nginx）
   - 配置 SSL 证书

3. **网络安全**
   - 限制端口访问
   - 配置防火墙规则

### 性能优化

1. **资源限制**
```yaml
services:
  backend:
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M
```

2. **日志轮转**
   - 配置日志文件大小限制
   - 设置日志保留策略

## 备份与恢复

### 数据备份
```bash
# 备份 MongoDB 数据
docker-compose exec mongodb mongodump --out /backup
docker cp logger-management-mongodb:/backup ./backup
```

### 数据恢复
```bash
# 恢复 MongoDB 数据
docker cp ./backup logger-management-mongodb:/backup
docker-compose exec mongodb mongorestore /backup
```

## 技术支持

如遇到问题，请：
1. 查看本文档的故障排除部分
2. 检查服务日志获取详细错误信息
3. 确认系统要求是否满足

---

**版本**: 1.0.0  
**更新时间**: 2024-06-20
