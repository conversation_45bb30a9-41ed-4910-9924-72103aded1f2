import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '../stores/auth'

const routes = [
  {
    path: '/',
    name: 'Home',
    redirect: '/dashboard'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/Login.vue'),
    meta: {
      title: '登录',
      hideNavbar: true
    }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('../views/Dashboard.vue'),
    meta: {
      title: '仪表板',
      requiresAuth: true
    }
  },
  {
    path: '/logs',
    name: 'LogList',
    component: () => import('../views/LogList.vue'),
    meta: {
      title: '日志列表',
      requiresAuth: true
    }
  },
  {
    path: '/logs/:id',
    name: 'LogDetail',
    component: () => import('../views/LogDetail.vue'),
    meta: {
      title: '日志详情',
      requiresAuth: true
    }
  },
  {
    path: '/applications',
    name: 'ApplicationList',
    component: () => import('../views/ApplicationList.vue'),
    meta: {
      title: '应用管理',
      requiresAuth: true,
      requiresAdmin: true
    }
  },
  {
    path: '/applications/:id',
    name: 'ApplicationDetail',
    component: () => import('../views/ApplicationDetail.vue'),
    meta: {
      title: '应用详情',
      requiresAuth: true,
      requiresAdmin: true
    }
  },
  {
    path: '/users',
    name: 'UserList',
    component: () => import('../views/UserList.vue'),
    meta: {
      title: '用户管理',
      requiresAuth: true,
      requiresAdmin: true
    }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: () => import('../views/Profile.vue'),
    meta: {
      title: '个人资料',
      requiresAuth: true
    }
  },
  {
    path: '/settings',
    name: 'Settings',
    component: () => import('../views/Settings.vue'),
    meta: {
      title: '设置',
      requiresAuth: true
    }
  },
  {
    path: '/api-docs',
    name: 'ApiDocs',
    component: () => import('../views/ApiDocs.vue'),
    meta: {
      title: 'API文档',
      requiresAuth: true
    }
  },
  {
    path: '/message-test',
    name: 'MessageTest',
    component: () => import('../views/MessageTest.vue'),
    meta: {
      title: '消息测试',
      requiresAuth: true
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - 通用日志管理系统`
  }

  const authStore = useAuthStore()

  // 如果路由需要认证
  if (to.meta?.requiresAuth) {
    // 检查是否已登录
    if (!authStore.token) {
      // 未登录，跳转到登录页
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
      return
    }

    // 验证token是否有效
    try {
      const isValid = await authStore.validateToken()
      if (!isValid) {
        // token无效，清除认证信息并跳转到登录页
        authStore.clearAuth()
        next({
          path: '/login',
          query: { redirect: to.fullPath }
        })
        return
      }

      // 如果没有用户信息，获取用户信息
      if (!authStore.user) {
        await authStore.getCurrentUser()
      }

      // 检查管理员权限
      if (to.meta?.requiresAdmin) {
        if (!authStore.isAdmin) {
          // 非管理员用户，跳转到仪表板
          next({
            path: '/dashboard',
            query: { error: 'admin_required' }
          })
          return
        }
      }
    } catch (error) {
      console.error('认证检查失败:', error)
      authStore.clearAuth()
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
      return
    }
  }

  // 如果已登录用户访问登录页，重定向到首页
  if (to.path === '/login' && authStore.isAuthenticated) {
    next('/dashboard')
    return
  }

  next()
})

export default router
