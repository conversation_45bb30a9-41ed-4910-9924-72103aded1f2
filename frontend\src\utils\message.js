import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import CustomMessageBox from '../components/CustomMessageBox.vue'

// 消息通知实例
let notificationInstance = null

// 设置通知实例
export const setNotificationInstance = (instance) => {
  notificationInstance = instance
}

// 创建自定义对话框实例
const createMessageBox = (options) => {
  return new Promise((resolve, reject) => {
    const container = document.createElement('div')
    document.body.appendChild(container)

    const app = createApp(CustomMessageBox, {
      visible: true,
      ...options,
      onConfirm: (value) => {
        cleanup()
        resolve(value)
      },
      onCancel: () => {
        cleanup()
        reject('cancel')
      },
      onClose: () => {
        cleanup()
        reject('cancel')
      }
    })

    // 注册Element Plus
    app.use(ElementPlus)

    const instance = app.mount(container)

    const cleanup = () => {
      setTimeout(() => {
        app.unmount()
        document.body.removeChild(container)
      }, 300) // 等待动画完成
    }
  })
}

// 统一的消息提示服务
export const Message = {
  // 成功消息 - 右下角弹出
  success(message, options = {}) {
    if (notificationInstance) {
      return notificationInstance.addNotification({
        type: 'success',
        message,
        duration: options.duration || 3000,
        title: options.title,
        closable: options.closable !== false,
        onClick: options.onClick
      })
    }
  },

  // 信息消息 - 右下角弹出
  info(message, options = {}) {
    if (notificationInstance) {
      return notificationInstance.addNotification({
        type: 'info',
        message,
        duration: options.duration || 3000,
        title: options.title,
        closable: options.closable !== false,
        onClick: options.onClick
      })
    }
  },

  // 警告消息 - 右下角弹出
  warning(message, options = {}) {
    if (notificationInstance) {
      return notificationInstance.addNotification({
        type: 'warning',
        message,
        duration: options.duration || 4000,
        title: options.title,
        closable: options.closable !== false,
        onClick: options.onClick
      })
    }
  },

  // 错误消息 - 右下角弹出
  error(message, options = {}) {
    if (notificationInstance) {
      return notificationInstance.addNotification({
        type: 'error',
        message,
        duration: options.duration || 5000,
        title: options.title,
        closable: options.closable !== false,
        onClick: options.onClick
      })
    }
  },

  // 持久化消息 - 不自动关闭
  persistent(type, message, options = {}) {
    if (notificationInstance) {
      return notificationInstance.addNotification({
        type,
        message,
        duration: 0, // 不自动关闭
        title: options.title,
        closable: options.closable !== false,
        onClick: options.onClick
      })
    }
  },

  // 关闭指定消息
  close(id) {
    if (notificationInstance) {
      notificationInstance.closeNotification(id)
    }
  },

  // 清空所有消息
  clearAll() {
    if (notificationInstance) {
      notificationInstance.clearAll()
    }
  }
}

// 确认对话框 - 需要用户交互的消息
export const MessageBox = {
  // 确认对话框
  confirm(message, title = '确认操作', options = {}) {
    return createMessageBox({
      type: 'confirm',
      title,
      message,
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      showCancelButton: true,
      ...options
    })
  },

  // 警告确认
  warning(message, title = '警告', options = {}) {
    return createMessageBox({
      type: 'warning',
      title,
      message,
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      showCancelButton: true,
      ...options
    })
  },

  // 删除确认
  delete(message, title = '确认删除', options = {}) {
    return createMessageBox({
      type: 'error',
      title,
      message,
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      confirmButtonClass: 'el-button--danger',
      showCancelButton: true,
      ...options
    })
  },

  // 信息提示
  alert(message, title = '提示', options = {}) {
    return createMessageBox({
      type: 'info',
      title,
      message,
      confirmButtonText: '确定',
      showCancelButton: false,
      ...options
    })
  },

  // 输入框
  prompt(message, title = '请输入', options = {}) {
    return createMessageBox({
      type: 'prompt',
      title,
      message,
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      showCancelButton: true,
      inputPlaceholder: options.inputPlaceholder || '请输入',
      inputType: options.inputType || 'text',
      ...options
    })
  }
}

// 快捷方法
export const showSuccess = (message, options) => Message.success(message, options)
export const showInfo = (message, options) => Message.info(message, options)
export const showWarning = (message, options) => Message.warning(message, options)
export const showError = (message, options) => Message.error(message, options)

export const confirmDelete = (message, title) => MessageBox.delete(message, title)
export const confirmAction = (message, title) => MessageBox.confirm(message, title)

// 默认导出
export default {
  Message,
  MessageBox,
  showSuccess,
  showInfo,
  showWarning,
  showError,
  confirmDelete,
  confirmAction,
  setNotificationInstance
}
