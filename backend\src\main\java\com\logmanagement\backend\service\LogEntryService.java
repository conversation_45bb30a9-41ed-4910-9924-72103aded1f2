package com.logmanagement.backend.service;

import com.logmanagement.backend.dto.LogSearchRequest;
import com.logmanagement.backend.dto.LogStatsResponse;
import com.logmanagement.backend.entity.LogEntry;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Optional;

/**
 * 日志条目服务接口
 * 
 * <AUTHOR> Management System
 * @version 1.0.0
 */
public interface LogEntryService {

    /**
     * 保存日志条目
     * 
     * @param logEntry 日志条目
     * @return 保存后的日志条目
     */
    LogEntry save(LogEntry logEntry);

    /**
     * 批量保存日志条目
     * 
     * @param logEntries 日志条目列表
     * @return 保存后的日志条目列表
     */
    List<LogEntry> saveAll(List<LogEntry> logEntries);

    /**
     * 根据ID查找日志条目
     * 
     * @param id 日志ID
     * @return 日志条目
     */
    Optional<LogEntry> findById(String id);

    /**
     * 分页查询所有日志条目
     * 
     * @param request 搜索请求
     * @return 日志分页结果
     */
    Page<LogEntry> findAll(LogSearchRequest request);

    /**
     * 搜索日志条目
     * 
     * @param request 搜索请求
     * @return 日志分页结果
     */
    Page<LogEntry> search(LogSearchRequest request);

    /**
     * 获取日志统计信息
     *
     * @return 日志统计响应
     */
    LogStatsResponse getStats();

    /**
     * 根据应用ID列表获取日志统计信息
     *
     * @param applicationIds 应用ID列表
     * @return 日志统计响应
     */
    LogStatsResponse getStatsByApplicationIds(List<String> applicationIds);

    /**
     * 获取所有日志来源
     * 
     * @return 日志来源列表
     */
    List<String> getAllSources();

    /**
     * 根据ID删除日志条目
     * 
     * @param id 日志ID
     * @return 是否删除成功
     */
    boolean deleteById(String id);

    /**
     * 批量删除日志条目
     * 
     * @param ids 日志ID列表
     * @return 删除的数量
     */
    long deleteByIds(List<String> ids);

    /**
     * 删除所有日志条目
     * 
     * @return 删除的数量
     */
    long deleteAll();

    /**
     * 清理过期日志（删除指定天数之前的日志）
     * 
     * @param days 天数
     * @return 删除的数量
     */
    long cleanupOldLogs(int days);

    /**
     * 获取日志总数
     * 
     * @return 总数
     */
    long count();
}
