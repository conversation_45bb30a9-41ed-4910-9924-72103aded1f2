# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE和编辑器文件
.vscode/
.idea/
*.swp
*.swo
*~

# 日志文件
*.log
logs/

# 临时文件
*.tmp
*.temp

# Java相关
*.class
*.jar
*.war
*.ear
*.nar
hs_err_pid*

# Maven
backend/target/
backend/.mvn/wrapper/maven-wrapper.jar
backend/.mvn/wrapper/maven-wrapper.properties
backend/.mvn/wrapper/MavenWrapperDownloader.java

# Gradle
backend/.gradle/
backend/build/
backend/gradle/wrapper/gradle-wrapper.jar
backend/gradle/wrapper/gradle-wrapper.properties
backend/gradlew
backend/gradlew.bat

# Spring Boot
backend/HELP.md
backend/mvnw
backend/mvnw.cmd

# Node.js相关
frontend/node_modules/
frontend/npm-debug.log*
frontend/yarn-debug.log*
frontend/yarn-error.log*
frontend/pnpm-debug.log*
frontend/lerna-debug.log*

# 构建输出
frontend/dist/
frontend/dist-ssr/
frontend/coverage/
frontend/.nyc_output/

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 缓存文件
frontend/.vite/
frontend/.cache/
frontend/.parcel-cache/

# 测试覆盖率
frontend/coverage/

# TypeScript
frontend/*.tsbuildinfo

# Docker相关
docker/data/
docker/logs/

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 配置文件中的敏感信息
application-local.yml
application-dev.yml
application-prod.yml

# 备份文件
*.bak
*.backup

# 压缩文件
*.zip
*.tar.gz
*.rar

# 其他
.vscode/settings.json
.history/
target/
