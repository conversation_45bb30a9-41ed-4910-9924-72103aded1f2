<template>
  <div class="application-list">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>应用管理</h1>
      <el-button type="primary" @click="showCreateDialog = true">
        <el-icon><Plus /></el-icon>
        创建应用
      </el-button>
    </div>

    <!-- 搜索表单 -->
    <div class="search-section">
      <el-form :model="searchForm" :inline="true" class="search-form">
        <el-form-item label="应用名称">
          <el-input 
            v-model="searchForm.name" 
            placeholder="搜索应用名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="应用状态" clearable style="width: 120px">
            <el-option label="激活" value="ACTIVE" />
            <el-option label="禁用" value="DISABLED" />
            <el-option label="维护" value="MAINTENANCE" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="环境">
          <el-select v-model="searchForm.environment" placeholder="环境" clearable style="width: 120px">
            <el-option label="开发" value="dev" />
            <el-option label="测试" value="test" />
            <el-option label="模拟" value="staging" />
            <el-option label="生产" value="prod" />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <div class="search-buttons">
            <el-button type="primary" @click="handleSearch" :loading="loading">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </div>
        </el-form-item>
      </el-form>
    </div>

    <!-- 应用表格 -->
    <div class="table-section">
      <div class="table-header">
        <h2>应用列表</h2>
        <div class="header-actions">
          <el-button 
            type="danger" 
            size="small"
            :disabled="!selectedApps.length"
            @click="handleBatchDelete"
          >
            <el-icon><Delete /></el-icon>
            批量删除
          </el-button>
          <el-button size="small" @click="handleRefresh">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>
      
      <el-table 
        :data="applications" 
        v-loading="loading"
        @selection-change="handleSelectionChange"
        stripe
        border
        style="width: 100%"
        :empty-text="loading ? '加载中...' : '暂无数据'"
      >
        <el-table-column type="selection" width="55" align="center" />

        <el-table-column prop="id" label="应用ID" width="360" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="app-id-container">
              <span class="app-id-cell">
                {{ row.id }}
              </span>
              <el-button
                link
                size="small"
                @click="copyAppInfo(row)"
                class="app-id-copy-btn"
                title="复制应用ID和令牌"
              >
                <el-icon size="14"><CopyDocument /></el-icon>
              </el-button>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="应用名称" width="150" show-overflow-tooltip />

        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
        
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="environment" label="环境" width="100" align="center">
          <template #default="{ row }">
            <el-tag v-if="row.environment" :type="getEnvTagType(row.environment)" size="small">
              {{ getEnvText(row.environment) }}
            </el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="version" label="版本" width="100" />
        
        <el-table-column prop="creatorUsername" label="创建者" width="120" />
        
        <el-table-column prop="createdAt" label="创建时间" width="200" class-name="time-column">
          <template #default="{ row }">
            <span style="color: #606266; font-size: 12px;">
              {{ formatTime(row.createdAt) }}
            </span>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="260" fixed="right" align="center">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button type="primary" size="small" @click="handleViewDetail(row)">
                <el-icon><View /></el-icon>
                详情
              </el-button>
              <el-button type="success" size="small" @click="handleEdit(row)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button type="warning" size="small" @click="handleShowToken(row)">
                <el-icon><Key /></el-icon>
                令牌
              </el-button>
              <el-button type="danger" size="small" @click="handleDelete(row)">
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="getPageSizeOptions()"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 创建应用对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="创建应用"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="createForm" :rules="createRules" ref="createFormRef" label-width="100px">
        <el-form-item label="应用名称" prop="name">
          <el-input v-model="createForm.name" placeholder="请输入应用名称" />
        </el-form-item>
        
        <el-form-item label="应用描述" prop="description">
          <el-input 
            v-model="createForm.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入应用描述"
          />
        </el-form-item>
        
        <el-form-item label="环境" prop="environment">
          <el-select v-model="createForm.environment" placeholder="选择环境" style="width: 100%">
            <el-option label="开发环境" value="dev" />
            <el-option label="测试环境" value="test" />
            <el-option label="模拟环境" value="staging" />
            <el-option label="生产环境" value="prod" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="版本" prop="version">
          <el-input v-model="createForm.version" placeholder="如：1.0.0" />
        </el-form-item>
        
        <el-form-item label="日志保留" prop="logRetentionDays">
          <el-input-number 
            v-model="createForm.logRetentionDays" 
            :min="1" 
            :max="365"
            placeholder="天数"
            style="width: 100%"
          />
          <div style="font-size: 12px; color: #909399; margin-top: 4px;">
            日志保留天数（1-365天）
          </div>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showCreateDialog = false">取消</el-button>
          <el-button type="primary" @click="handleCreate" :loading="createLoading">
            创建
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 编辑应用对话框 -->
    <el-dialog
      v-model="showEditDialog"
      title="编辑应用"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="editForm" :rules="editRules" ref="editFormRef" label-width="100px">
        <el-form-item label="应用名称" prop="name">
          <el-input v-model="editForm.name" placeholder="请输入应用名称" />
        </el-form-item>

        <el-form-item label="应用描述" prop="description">
          <el-input
            v-model="editForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入应用描述"
          />
        </el-form-item>

        <el-form-item label="应用状态" prop="status">
          <el-select v-model="editForm.status" placeholder="请选择应用状态" style="width: 100%">
            <el-option label="激活" value="ACTIVE" />
            <el-option label="禁用" value="DISABLED" />
            <el-option label="维护" value="MAINTENANCE" />
          </el-select>
        </el-form-item>

        <el-form-item label="环境" prop="environment">
          <el-select v-model="editForm.environment" placeholder="请选择环境" style="width: 100%">
            <el-option label="开发" value="dev" />
            <el-option label="测试" value="test" />
            <el-option label="模拟" value="staging" />
            <el-option label="生产" value="prod" />
          </el-select>
        </el-form-item>

        <el-form-item label="版本" prop="version">
          <el-input v-model="editForm.version" placeholder="请输入版本号，如：1.0.0" />
        </el-form-item>

        <el-form-item label="日志保留天数" prop="logRetentionDays">
          <el-input-number
            v-model="editForm.logRetentionDays"
            :min="1"
            :max="365"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="应用令牌">
          <div style="display: flex; align-items: center; gap: 10px;">
            <el-input
              :value="editForm.token ? editForm.token.substring(0, 16) + '...' : ''"
              readonly
              placeholder="令牌只能查看和重新生成"
              style="flex: 1"
            />
            <el-button type="warning" @click="handleRegenerateToken(editForm.id)">
              重新生成
            </el-button>
            <el-button
              v-if="editForm.token"
              type="primary"
              @click="copyTokenFromEdit(editForm.token)"
            >
              <el-icon><CopyDocument /></el-icon>
              复制令牌
            </el-button>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showEditDialog = false">取消</el-button>
          <el-button type="primary" :loading="editLoading" @click="handleUpdate">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 令牌显示对话框 -->
    <el-dialog
      v-model="showTokenDialog"
      title="应用令牌"
      width="600px"
    >
      <div class="token-content">
        <el-alert
          title="请妥善保管应用令牌"
          type="warning"
          description="令牌用于应用推送日志时的身份验证，请勿泄露给他人。"
          show-icon
          :closable="false"
        />
        
        <div class="token-display">
          <el-input
            v-model="currentToken"
            readonly
            type="textarea"
            :rows="3"
            placeholder="令牌"
          >
            <template #append>
              <el-button @click="copyToken">
                <el-icon><CopyDocument /></el-icon>
                复制
              </el-button>
            </template>
          </el-input>
        </div>
        
        <div class="token-usage">
          <h4>使用方法：</h4>
          <pre class="usage-code">
# 推送日志示例
curl -X POST http://localhost:8080/api/logs \
  -H "X-App-ID: YOUR_APP_ID" \
  -H "X-Api-Key: {{ currentToken }}" \
  -H "Content-Type: application/json" \
  -d '{
    "level": "INFO",
    "message": "这是一条测试日志",
    "source": "MyService",
    "timestamp": "2024-01-01T12:00:00"
  }'
          </pre>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="showTokenDialog = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onActivated, onDeactivated, inject } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  Plus, Search, Refresh, Delete, View, Key, CopyDocument, Edit
} from '@element-plus/icons-vue'
import { applicationApi } from '../api/applications'
import { useAuthStore } from '../stores/auth'
import { useConfigStore } from '../stores/config'
import { formatTime } from '../utils/dateTime'
import { Message, MessageBox } from '../utils/message'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()
const configStore = useConfigStore()

// 响应式数据
const loading = ref(false)
const createLoading = ref(false)
const editLoading = ref(false)
const applications = ref([])
const selectedApps = ref([])
const showCreateDialog = ref(false)
const showEditDialog = ref(false)
const showTokenDialog = ref(false)
const currentToken = ref('')
const createFormRef = ref()
const editFormRef = ref()

// 注入重置检查方法
const checkAndClearResetFlag = inject('checkAndClearResetFlag')

// 搜索表单
const searchForm = reactive({
  name: '',
  status: '',
  environment: ''
})

// 分页信息
const pagination = reactive({
  page: 1,
  size: configStore.getDefaultPageSize(),
  total: 0
})

// 创建表单
const createForm = reactive({
  name: '',
  description: '',
  environment: '',
  version: '',
  logRetentionDays: 30
})

// 编辑表单
const editForm = reactive({
  id: '',
  name: '',
  description: '',
  status: '',
  environment: '',
  version: '',
  logRetentionDays: 30,
  token: ''
})

// 表单验证规则
const createRules = {
  name: [
    { required: true, message: '请输入应用名称', trigger: 'blur' },
    { min: 2, max: 50, message: '应用名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  description: [
    { max: 200, message: '描述长度不能超过 200 个字符', trigger: 'blur' }
  ]
}

// 编辑表单验证规则
const editRules = {
  name: [
    { required: true, message: '请输入应用名称', trigger: 'blur' },
    { min: 2, max: 50, message: '应用名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  description: [
    { max: 200, message: '描述长度不能超过 200 个字符', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择应用状态', trigger: 'change' }
  ],
  environment: [
    { required: true, message: '请选择环境', trigger: 'change' }
  ],
  version: [
    { required: true, message: '请输入版本号', trigger: 'blur' }
  ],
  logRetentionDays: [
    { required: true, message: '请输入日志保留天数', trigger: 'blur' },
    { type: 'number', min: 1, max: 365, message: '日志保留天数必须在1-365之间', trigger: 'blur' }
  ]
}

// 获取页面大小选项
const getPageSizeOptions = () => {
  const maxSize = configStore.getMaxPageSize()
  const options = [10, 20, 50]
  if (maxSize >= 100) {
    options.push(100)
  }
  if (maxSize > 100) {
    options.push(maxSize)
  }
  return options
}

// 获取状态标签类型
const getStatusTagType = (status) => {
  const typeMap = {
    ACTIVE: 'success',
    DISABLED: 'danger',
    MAINTENANCE: 'warning'
  }
  return typeMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const textMap = {
    ACTIVE: '激活',
    DISABLED: '禁用',
    MAINTENANCE: '维护'
  }
  return textMap[status] || status
}

// 获取环境标签类型
const getEnvTagType = (env) => {
  const typeMap = {
    dev: 'warning',
    test: 'info',
    staging: 'primary',
    prod: 'success'
  }
  return typeMap[env] || 'info'
}

// 获取环境文本
const getEnvText = (env) => {
  const textMap = {
    dev: '开发',
    test: '测试',
    staging: '模拟',
    prod: '生产'
  }
  return textMap[env] || env
}



// 搜索
const handleSearch = async () => {
  pagination.page = 1
  await loadApplications()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    name: '',
    status: '',
    environment: ''
  })
  pagination.page = 1
  loadApplications()
}

// 刷新
const handleRefresh = () => {
  loadApplications()
}

// 选择变化
const handleSelectionChange = (selection) => {
  selectedApps.value = selection
}

// 查看详情
const handleViewDetail = (app) => {
  router.push(`/applications/${app.id}`)
}

// 编辑应用
const handleEdit = (app) => {
  // 填充编辑表单
  Object.assign(editForm, {
    id: app.id,
    name: app.name,
    description: app.description || '',
    status: app.status,
    environment: app.environment || '',
    version: app.version || '',
    logRetentionDays: app.logRetentionDays || 30,
    token: app.token
  })
  showEditDialog.value = true
}

// 显示令牌
const handleShowToken = (app) => {
  currentToken.value = app.token
  showTokenDialog.value = true
}

// 复制令牌
const copyToken = async () => {
  await copyToClipboard(currentToken.value, '令牌已复制到剪贴板')
}



// 从编辑表单复制令牌
const copyTokenFromEdit = async (token) => {
  await copyToClipboard(token, '令牌已复制到剪贴板')
}

// 复制应用信息（ID和令牌）
const copyAppInfo = async (app) => {
  const appInfo = `应用ID: ${app.id}\n应用令牌: ${app.token}`
  await copyToClipboard(appInfo, '应用ID和令牌已复制到剪贴板')
}

// 通用复制到剪贴板函数（带降级方案）
const copyToClipboard = async (text, successMessage = '内容已复制到剪贴板') => {
  try {
    // 首先尝试使用现代的 Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text)
      Message.success(successMessage)
      return
    }

    // 降级方案：使用传统的 document.execCommand
    const textArea = document.createElement('textarea')
    textArea.value = text
    textArea.style.position = 'fixed'
    textArea.style.left = '-999999px'
    textArea.style.top = '-999999px'
    document.body.appendChild(textArea)
    textArea.focus()
    textArea.select()

    const successful = document.execCommand('copy')
    document.body.removeChild(textArea)

    if (successful) {
      Message.success(successMessage)
    } else {
      throw new Error('execCommand failed')
    }
  } catch (error) {
    console.error('复制失败:', error)
    Message.error('复制失败，请手动选择并复制内容')
  }
}

// 删除应用
const handleDelete = async (app) => {
  try {
    await MessageBox.delete(
      `确定要删除应用"${app.name}"吗？删除后无法恢复，该应用的所有日志数据也将无法访问。`,
      '确认删除'
    )

    // 调用删除API
    const response = await applicationApi.deleteApplication(app.id)

    if (response.code === 200) {
      Message.success('应用删除成功')
      await loadApplications() // 重新加载应用列表
    } else {
      throw new Error(response.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除应用失败:', error)
      Message.error('删除失败: ' + (error.message || '请检查网络连接'))
    }
  }
}

// 批量删除
const handleBatchDelete = async () => {
  try {
    const selectedCount = selectedApps.value.length
    const appNames = selectedApps.value.map(app => app.name).join('、')

    await MessageBox.confirm(
      `确定要删除选中的 ${selectedCount} 个应用吗？\n应用：${appNames}\n\n删除后无法恢复，这些应用的所有日志数据也将无法访问。`,
      '确认批量删除'
    )



    // 逐个调用删除API
    const deletePromises = selectedApps.value.map(app =>
      applicationApi.deleteApplication(app.id)
    )

    const results = await Promise.allSettled(deletePromises)

    // 统计删除结果
    const successCount = results.filter(result =>
      result.status === 'fulfilled' && result.value.code === 200
    ).length

    const failedCount = selectedCount - successCount

    if (successCount === selectedCount) {
      Message.success(`批量删除成功，共删除 ${successCount} 个应用`)
    } else if (successCount > 0) {
      Message.warning(`部分删除成功：成功 ${successCount} 个，失败 ${failedCount} 个`)
    } else {
      Message.error('批量删除失败，所有应用删除失败')
    }

    // 清空选择并重新加载列表
    selectedApps.value = []
    await loadApplications()

  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除应用失败:', error)
      Message.error('批量删除失败: ' + (error.message || '请检查网络连接'))
    }
  }
}

// 创建应用
const handleCreate = async () => {
  try {
    await createFormRef.value.validate()
    createLoading.value = true

    let response
    try {
      // 尝试使用真实API
      response = await applicationApi.createApplication(createForm)
    } catch (error) {
      console.error('创建应用失败:', error)
      Message.error('创建应用失败，请检查后端服务是否正常运行')
      return
    }

    if (response.code === 200) {
      Message.success(response.message || '应用创建成功')
      showCreateDialog.value = false
      resetCreateForm()
      await loadApplications()
    } else {
      Message.error(response.message || '创建失败')
    }
  } catch (error) {
    console.error('创建应用失败:', error)
    Message.error('创建失败')
  } finally {
    createLoading.value = false
  }
}

// 更新应用
const handleUpdate = async () => {
  try {
    await editFormRef.value.validate()
    editLoading.value = true

    const updateData = {
      name: editForm.name,
      description: editForm.description,
      status: editForm.status,
      environment: editForm.environment,
      version: editForm.version,
      logRetentionDays: editForm.logRetentionDays
    }

    let response
    try {
      // 尝试使用真实API
      response = await applicationApi.updateApplication(editForm.id, updateData)
    } catch (error) {
      console.error('更新应用失败:', error)
      Message.error('更新应用失败，请检查后端服务是否正常运行')
      return
    }

    if (response.code === 200) {
      Message.success(response.message || '应用更新成功')
      showEditDialog.value = false
      resetEditForm()
      await loadApplications()
    } else {
      Message.error(response.message || '更新失败')
    }
  } catch (error) {
    console.error('更新应用失败:', error)
    Message.error('更新失败')
  } finally {
    editLoading.value = false
  }
}

// 重新生成令牌
const handleRegenerateToken = async (appId) => {
  try {
    await MessageBox.confirm('确定要重新生成令牌吗？旧令牌将失效。', '确认操作')

    let response
    try {
      response = await applicationApi.regenerateToken(appId)
    } catch (error) {
      console.error('重新生成令牌失败:', error)
      Message.error('重新生成令牌失败，请检查后端服务是否正常运行')
      return
    }

    if (response.code === 200) {
      Message.success(response.message || '令牌重新生成成功')
      // 更新编辑表单中的令牌
      if (response.data && response.data.token) {
        editForm.token = response.data.token
      }
      // 重新加载应用列表
      await loadApplications()
    } else {
      Message.error(response.message || '重新生成令牌失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('重新生成令牌失败:', error)
      Message.error('重新生成令牌失败')
    }
  }
}

// 重置创建表单
const resetCreateForm = () => {
  Object.assign(createForm, {
    name: '',
    description: '',
    environment: '',
    version: '',
    logRetentionDays: 30
  })
  createFormRef.value?.resetFields()
}

// 重置编辑表单
const resetEditForm = () => {
  Object.assign(editForm, {
    id: '',
    name: '',
    description: '',
    status: '',
    environment: '',
    version: '',
    logRetentionDays: 30,
    token: ''
  })
  editFormRef.value?.resetFields()
}

// 分页大小变化
const handleSizeChange = (size) => {
  pagination.size = size
  loadApplications()
}

// 当前页变化
const handleCurrentChange = (page) => {
  pagination.page = page
  loadApplications()
}

// 重置页面状态到初始状态
const resetPageState = () => {
  // 重置搜索表单
  searchForm.name = ''
  searchForm.status = ''
  searchForm.environment = ''

  // 重置分页
  pagination.page = 1
  pagination.size = configStore.getDefaultPageSize()
  pagination.total = 0

  // 清空应用列表
  applications.value = []

  // 重置选择状态
  selectedApps.value = []

  // 重置加载状态
  loading.value = false

  // 关闭所有对话框
  showCreateDialog.value = false
  showEditDialog.value = false
  showTokenDialog.value = false

  console.log('应用管理页面状态已重置')
}

// 加载应用数据
const loadApplications = async () => {
  try {
    loading.value = true

    const params = {
      page: pagination.page,
      size: pagination.size,
      sort: 'createdAt',
      order: 'desc'
    }

    // 添加搜索条件
    if (searchForm.name) {
      params.keyword = searchForm.name
    }
    if (searchForm.status) {
      params.status = searchForm.status
    }
    if (searchForm.environment) {
      params.environment = searchForm.environment
    }

    let response
    try {
      // 尝试使用真实API
      response = await applicationApi.getApplications(params)

      if (response.code === 200 && response.data) {
        let appList = response.data.content || []

        // 如果不是管理员，只显示用户有权限的应用
        if (!authStore.isAdmin && authStore.user) {
          const authorizedAppIds = authStore.user.authorizedAppIds || []
          appList = appList.filter(app =>
            authorizedAppIds.includes(app.id) || app.creatorId === authStore.user.id
          )
        }

        applications.value = appList
        pagination.total = appList.length
      } else {
        throw new Error(response.message || '获取应用列表失败')
      }
    } catch (error) {
      console.error('获取应用列表失败:', error)
      applications.value = []
      pagination.total = 0
      Message.error('获取应用列表失败，请检查后端服务是否正常运行')
    }
  } catch (error) {
    console.error('加载应用列表失败:', error)
    Message.error('加载应用列表失败')
  } finally {
    loading.value = false
  }
}

// 组件挂载时加载数据
onMounted(() => {
  console.log('ApplicationList组件挂载')
  loadApplications()
})

// 组件激活时（从其他tab切换回来时）
onActivated(async () => {
  console.log('ApplicationList组件被激活')

  // 检查是否需要重置（从导航栏进入）
  const needReset = checkAndClearResetFlag && checkAndClearResetFlag(route.path)

  if (needReset) {
    console.log('检测到从导航栏进入，重置页面状态')
    resetPageState()
    await loadApplications()
  } else {
    console.log('从其他tab切换回来，保持当前状态')
  }
})

// 组件停用时（切换到其他tab时）
onDeactivated(() => {
  console.log('ApplicationList组件被停用')
})
</script>

<style scoped>
.application-list {
  padding: var(--container-padding);
  background-color: var(--bg-color);
  min-height: calc(100vh - var(--header-height));
  max-width: var(--content-max-width);
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.search-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-form {
  margin: 0;
}

.search-buttons {
  display: flex;
  gap: 12px;
  align-items: center;
}

.table-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #ebeef5;
  background-color: #fafafa;
}

.table-header h2 {
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.action-buttons {
  display: flex;
  gap: 2px;
  justify-content: center;
  align-items: center;
  flex-wrap: nowrap;
}

.action-buttons .el-button {
  min-width: 50px;
  height: 26px;
  padding: 2px 5px;
  font-size: 11px;
  border-radius: 3px;
  font-weight: 500;
}

.pagination-wrapper {
  padding: 20px;
  display: flex;
  justify-content: center;
  background-color: #fafafa;
  border-top: 1px solid #ebeef5;
}

.token-content {
  padding: 10px 0;
}

.token-display {
  margin: 20px 0;
}

.token-usage {
  margin-top: 20px;
}

.token-usage h4 {
  margin-bottom: 10px;
  color: #303133;
}

.usage-code {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.5;
  overflow-x: auto;
  border: 1px solid #e4e7ed;
}

/* 表格样式优化 */
:deep(.el-table th) {
  font-weight: bold;
}

:deep(.el-table td) {
  padding: 8px 0;
}

/* 应用ID列样式 */
.app-id-container {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.app-id-cell {
  font-family: 'Courier New', monospace;
  font-size: 13px;
  color: #606266;
  white-space: nowrap;
  overflow: visible;
  word-break: keep-all;
  text-overflow: clip;
  flex: 1;
}

.app-id-copy-btn {
  padding: 0;
  min-height: auto;
  color: #409eff;
  transition: color 0.3s;
  flex-shrink: 0;
}

.app-id-copy-btn:hover {
  color: #66b1ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .application-list {
    padding: var(--container-padding);
    min-height: calc(100vh - 120px);
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
    margin-bottom: 16px;
  }

  .page-header h1 {
    font-size: 20px;
    text-align: center;
  }

  .page-header .el-button {
    align-self: center;
    width: 200px;
  }

  .search-section {
    padding: 16px;
    margin-bottom: 16px;
  }

  .search-form {
    display: block;
  }

  .search-form .el-form-item {
    margin-bottom: 12px;
    display: block;
  }

  .search-form .el-form-item label {
    font-size: 13px;
    margin-bottom: 6px;
    display: block;
  }

  .search-form .el-input,
  .search-form .el-select {
    width: 100% !important;
  }

  .search-buttons {
    justify-content: center;
    gap: 12px;
    margin-top: 8px;
  }

  .search-buttons .el-button {
    flex: 1;
    max-width: 120px;
    height: 36px;
    padding: 8px 16px;
    font-size: 13px;
  }

  .table-section {
    margin-bottom: 16px;
  }

  .table-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
    padding: 16px;
  }

  .table-header h2 {
    font-size: 16px;
    text-align: center;
  }

  .header-actions {
    justify-content: center;
    gap: 10px;
  }

  .header-actions .el-button {
    height: 32px;
    padding: 8px 16px;
    font-size: 12px;
    flex: 1;
    max-width: 120px;
  }

  /* 表格样式优化 */
  :deep(.el-table th) {
    font-weight: bold;
    padding: 6px 0;
  }

  :deep(.el-table td) {
    padding: 6px 0;
  }

  /* 移动端表格优化 */
  :deep(.el-table) {
    font-size: 12px;
  }

  :deep(.el-table .el-table__header th) {
    padding: 4px 4px;
    font-size: 12px;
    font-weight: bold;
  }

  :deep(.el-table .el-table__body td) {
    padding: 4px 4px;
    font-size: 12px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 6px;
  }

  .action-buttons .el-button {
    width: 100%;
    height: 28px;
    padding: 4px 8px;
    font-size: 11px;
  }

  .pagination-wrapper {
    padding: 16px;
  }

  :deep(.el-pagination) {
    justify-content: center;
  }

  :deep(.el-pagination .el-pager li) {
    min-width: 28px;
    height: 28px;
    line-height: 28px;
    font-size: 12px;
  }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {
  .application-list {
    padding: var(--container-padding);
  }

  .page-header h1 {
    font-size: 18px;
  }

  .page-header .el-button {
    width: 160px;
  }

  .search-section {
    padding: 12px;
  }

  .search-buttons .el-button {
    height: 32px;
    font-size: 12px;
  }

  .table-header {
    padding: 12px;
  }

  .header-actions .el-button {
    height: 28px;
    padding: 6px 12px;
    font-size: 11px;
  }
}
</style>
