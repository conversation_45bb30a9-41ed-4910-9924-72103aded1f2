package com.logmanagement.backend.controller;

import com.logmanagement.backend.config.PaginationConfig;
import com.logmanagement.backend.dto.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 配置控制器
 * 
 * <AUTHOR> Management System
 * @version 1.0.0
 */
@RestController
@RequestMapping("/config")
public class ConfigController {

    private static final Logger logger = LoggerFactory.getLogger(ConfigController.class);

    @Autowired
    private PaginationConfig paginationConfig;

    /**
     * 获取分页配置
     * 
     * @return 分页配置信息
     */
    @GetMapping("/pagination")
    public ResponseEntity<ApiResponse<Map<String, Integer>>> getPaginationConfig() {
        logger.info("获取分页配置");
        try {
            Map<String, Integer> config = new HashMap<>();
            config.put("defaultPageSize", paginationConfig.getDefaultPageSize());
            config.put("maxPageSize", paginationConfig.getMaxPageSize());
            
            return ResponseEntity.ok(ApiResponse.success("获取分页配置成功", config));
        } catch (Exception e) {
            logger.error("获取分页配置失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取分页配置失败"));
        }
    }

    /**
     * 获取所有系统配置
     * 
     * @return 系统配置信息
     */
    @GetMapping("/system")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getSystemConfig() {
        logger.info("获取系统配置");
        try {
            Map<String, Object> config = new HashMap<>();
            
            // 分页配置
            Map<String, Integer> paginationInfo = new HashMap<>();
            paginationInfo.put("defaultPageSize", paginationConfig.getDefaultPageSize());
            paginationInfo.put("maxPageSize", paginationConfig.getMaxPageSize());
            config.put("pagination", paginationInfo);
            
            // 可以在这里添加其他系统配置
            // config.put("upload", uploadConfig);
            // config.put("cache", cacheConfig);
            
            return ResponseEntity.ok(ApiResponse.success("获取系统配置成功", config));
        } catch (Exception e) {
            logger.error("获取系统配置失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取系统配置失败"));
        }
    }
}
