<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试查询用时功能</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            background: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            white-space: pre-wrap;
        }
        .success {
            color: green;
        }
        .error {
            color: red;
        }
        .query-time {
            color: #409eff;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>测试查询用时功能</h1>
    
    <div class="test-section">
        <h3>1. 登录获取Token</h3>
        <button onclick="login()">登录</button>
        <div id="loginResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>2. 测试日志查询（GET方式）</h3>
        <button onclick="testGetLogs()">查询日志</button>
        <div id="getLogsResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>3. 测试日志搜索（POST方式）</h3>
        <button onclick="testSearchLogs()">搜索日志</button>
        <div id="searchLogsResult" class="result"></div>
    </div>

    <script>
        let token = '';
        
        async function login() {
            const resultDiv = document.getElementById('loginResult');
            resultDiv.textContent = '正在登录...';
            
            try {
                const response = await fetch('http://localhost:8080/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });
                
                const result = await response.json();
                
                if (result.code === 200 && result.data && result.data.token) {
                    token = result.data.token;
                    resultDiv.innerHTML = `<span class="success">✓ 登录成功</span>\nToken: ${token.substring(0, 20)}...`;
                } else {
                    resultDiv.innerHTML = `<span class="error">✗ 登录失败</span>\n${JSON.stringify(result, null, 2)}`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">✗ 登录错误: ${error.message}</span>`;
            }
        }
        
        async function testGetLogs() {
            const resultDiv = document.getElementById('getLogsResult');
            
            if (!token) {
                resultDiv.innerHTML = '<span class="error">请先登录获取Token</span>';
                return;
            }
            
            resultDiv.textContent = '正在查询日志...';
            
            try {
                const response = await fetch('http://localhost:8080/api/logs?page=0&size=5', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const result = await response.json();
                
                if (result.code === 200) {
                    const total = result.data.totalElements || result.data.total || 0;
                    const queryTime = result.queryTime || 0;
                    
                    resultDiv.innerHTML = `<span class="success">✓ 查询成功</span>
总记录数: ${total}
<span class="query-time">查询用时: ${queryTime} 毫秒</span>
响应结构: ${JSON.stringify({
                        code: result.code,
                        message: result.message,
                        queryTime: result.queryTime,
                        dataType: Array.isArray(result.data) ? 'array' : typeof result.data
                    }, null, 2)}`;
                } else {
                    resultDiv.innerHTML = `<span class="error">✗ 查询失败</span>\n${JSON.stringify(result, null, 2)}`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">✗ 查询错误: ${error.message}</span>`;
            }
        }
        
        async function testSearchLogs() {
            const resultDiv = document.getElementById('searchLogsResult');
            
            if (!token) {
                resultDiv.innerHTML = '<span class="error">请先登录获取Token</span>';
                return;
            }
            
            resultDiv.textContent = '正在搜索日志...';
            
            try {
                const response = await fetch('http://localhost:8080/api/logs/search', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        page: 0,
                        size: 5,
                        keyword: 'test'
                    })
                });
                
                const result = await response.json();
                
                if (result.code === 200) {
                    const total = result.data.totalElements || result.data.total || 0;
                    const queryTime = result.queryTime || 0;
                    
                    resultDiv.innerHTML = `<span class="success">✓ 搜索成功</span>
总记录数: ${total}
<span class="query-time">查询用时: ${queryTime} 毫秒</span>
响应结构: ${JSON.stringify({
                        code: result.code,
                        message: result.message,
                        queryTime: result.queryTime,
                        dataType: Array.isArray(result.data) ? 'array' : typeof result.data
                    }, null, 2)}`;
                } else {
                    resultDiv.innerHTML = `<span class="error">✗ 搜索失败</span>\n${JSON.stringify(result, null, 2)}`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">✗ 搜索错误: ${error.message}</span>`;
            }
        }
    </script>
</body>
</html>
