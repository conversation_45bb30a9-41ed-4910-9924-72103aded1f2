version: '3.8'

services:
  # 后端服务
  backend:
    build:
      context: ../backend
      dockerfile: Dockerfile
    container_name: logger-management-backend
    restart: unless-stopped
    ports:
      - "${BACKEND_PORT:-8080}:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=docker-external
      - MONGODB_HOST=${MONGODB_HOST:-localhost}
      - MONGODB_PORT=${MONGODB_PORT:-27017}
      - MONGODB_DATABASE=${MONGODB_DATABASE:-logger_management}
      - MONGODB_USERNAME=${MONGODB_USERNAME:-logger_user}
      - MONGODB_PASSWORD=${MONGODB_PASSWORD:-logger_password}
      - MONGODB_AUTH_DATABASE=${MONGODB_AUTH_DATABASE:-logger_management}
      - JWT_SECRET=${JWT_SECRET:-LoggerManagementSystemSecretKeyForJ<PERSON>TTokenGeneration2024!@#$%^&*()_+{}|:<>?[]\\;'\",./"} 
      - JWT_EXPIRATION=${JWT_EXPIRATION:-86400}
      - CORS_ALLOWED_ORIGINS=${CORS_ALLOWED_ORIGINS:-*}
    volumes:
      - backend_logs:/app/logs
    networks:
      - logger-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    # 如果MongoDB在同一Docker网络中，添加depends_on
    # depends_on:
    #   - mongodb

  # 前端服务
  frontend:
    build:
      context: ../frontend
      dockerfile: Dockerfile
    container_name: logger-management-frontend
    restart: unless-stopped
    ports:
      - "${FRONTEND_PORT:-80}:80"
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - logger-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # MongoDB Express (可选的Web管理界面) - 仅在需要时启用
  # mongo-express:
  #   image: mongo-express:latest
  #   container_name: logger-management-mongo-express
  #   restart: unless-stopped
  #   ports:
  #     - "${MONGO_EXPRESS_PORT:-8081}:8081"
  #   environment:
  #     ME_CONFIG_MONGODB_SERVER: ${MONGODB_HOST:-localhost}
  #     ME_CONFIG_MONGODB_PORT: ${MONGODB_PORT:-27017}
  #     ME_CONFIG_MONGODB_ADMINUSERNAME: ${MONGODB_ADMIN_USERNAME:-admin}
  #     ME_CONFIG_MONGODB_ADMINPASSWORD: ${MONGODB_ADMIN_PASSWORD:-password}
  #     ME_CONFIG_BASICAUTH_USERNAME: ${MONGO_EXPRESS_USERNAME:-admin}
  #     ME_CONFIG_BASICAUTH_PASSWORD: ${MONGO_EXPRESS_PASSWORD:-admin123}
  #   networks:
  #     - logger-network

volumes:
  backend_logs:
    driver: local

networks:
  logger-network:
    driver: bridge
