package com.logmanagement.backend.dto;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 更新用户请求DTO
 * 
 * <AUTHOR> Management System
 * @version 1.0.0
 */
public class UpdateUserRequest {

    /**
     * 邮箱
     */
    @NotBlank(message = "邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    private String email;

    /**
     * 真实姓名
     */
    @NotBlank(message = "真实姓名不能为空")
    @Size(max = 100, message = "真实姓名长度不能超过100个字符")
    private String realName;

    /**
     * 密码（可选，如果不提供则不更新密码）
     */
    @Size(min = 6, max = 100, message = "密码长度必须在6-100个字符之间")
    private String password;

    /**
     * 用户角色（只有管理员可以修改）
     */
    private String role;

    /**
     * 用户状态（只有管理员可以修改）
     */
    private String status;

    /**
     * 授权的应用ID列表（只有管理员可以修改）
     */
    private List<String> authorizedAppIds;

    // 构造函数
    public UpdateUserRequest() {
    }

    public UpdateUserRequest(String email, String realName) {
        this.email = email;
        this.realName = realName;
    }

    // Getter 和 Setter 方法
    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public List<String> getAuthorizedAppIds() {
        return authorizedAppIds;
    }

    public void setAuthorizedAppIds(List<String> authorizedAppIds) {
        this.authorizedAppIds = authorizedAppIds;
    }

    @Override
    public String toString() {
        return "UpdateUserRequest{" +
                "email='" + email + '\'' +
                ", realName='" + realName + '\'' +
                ", role='" + role + '\'' +
                ", status='" + status + '\'' +
                ", authorizedAppIds=" + authorizedAppIds +
                '}';
    }
}
