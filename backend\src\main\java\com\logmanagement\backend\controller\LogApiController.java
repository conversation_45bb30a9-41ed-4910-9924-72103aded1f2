package com.logmanagement.backend.controller;

import com.logmanagement.backend.entity.Application;
import com.logmanagement.backend.entity.ApplicationStatus;
import com.logmanagement.backend.entity.LogEntry;
import com.logmanagement.backend.repository.ApplicationRepository;
import com.logmanagement.backend.service.LogEntryService;
import com.logmanagement.backend.dto.ApiResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 日志API控制器
 * 
 * 专门用于外部应用调用的日志创建API
 * 使用AppID和ApiKey进行身份认证
 * 
 * <AUTHOR> Management System
 * @version 1.0.0
 */
@Api(tags = "日志API", description = "供外部应用调用的日志创建接口，使用AppID和ApiKey认证")
@RestController
@RequestMapping("/external/logs")
public class LogApiController {

    private static final Logger logger = LoggerFactory.getLogger(LogApiController.class);

    @Autowired
    private LogEntryService logEntryService;

    @Autowired
    private ApplicationRepository applicationRepository;

    /**
     * 创建单条日志
     * 
     * 外部应用调用此接口创建日志，需要提供AppID和ApiKey进行认证
     * 
     * @param appId 应用ID
     * @param apiKey API密钥（应用Token）
     * @param logEntry 日志条目
     * @return 创建结果
     */
    @ApiOperation(
        value = "创建日志条目", 
        notes = "外部应用调用此接口创建单条日志记录。" +
                "需要在请求头中提供AppID和ApiKey进行身份认证。" +
                "ApiKey实际就是应用的Token。"
    )
    @PostMapping("/create")
    public ResponseEntity<ApiResponse<LogEntry>> createLog(
            @ApiParam(value = "应用ID", required = true, example = "app123456")
            @RequestHeader("X-App-ID") String appId,
            @ApiParam(value = "API密钥（应用Token）", required = true, example = "abc123def456")
            @RequestHeader("X-Api-Key") String apiKey,
            @ApiParam(value = "日志条目信息", required = true)
            @Valid @RequestBody LogEntry logEntry) {
        
        logger.info("外部应用创建日志: AppID={}, LogLevel={}", appId, logEntry.getLevel());

        try {
            // 验证AppID和ApiKey
            Application application = validateAppCredentials(appId, apiKey);
            if (application == null) {
                logger.warn("应用认证失败: AppID={}", appId);
                return ResponseEntity.ok(ApiResponse.error("应用认证失败，请检查AppID和ApiKey"));
            }

            // 设置日志的应用ID（确保与认证的应用一致）
            logEntry.setApplicationId(appId);
            
            // 设置创建时间
            LocalDateTime now = LocalDateTime.now();
            logEntry.setCreatedAt(now);
            logEntry.setUpdatedAt(now);

            // 更新应用最后活跃时间
            application.setLastActiveTime(now);
            applicationRepository.save(application);

            // 保存日志
            LogEntry savedLog = logEntryService.save(logEntry);
            
            logger.info("日志创建成功: AppID={}, LogID={}", appId, savedLog.getId());
            return ResponseEntity.ok(ApiResponse.success("日志创建成功", savedLog));

        } catch (Exception e) {
            logger.error("创建日志失败: AppID=" + appId, e);
            return ResponseEntity.ok(ApiResponse.error("创建日志失败: " + e.getMessage()));
        }
    }

    /**
     * 批量创建日志
     * 
     * 外部应用调用此接口批量创建日志，提高批量导入效率
     * 
     * @param appId 应用ID
     * @param apiKey API密钥（应用Token）
     * @param logEntries 日志条目列表
     * @return 创建结果
     */
    @ApiOperation(
        value = "批量创建日志条目", 
        notes = "外部应用调用此接口批量创建日志记录。" +
                "需要在请求头中提供AppID和ApiKey进行身份认证。" +
                "所有日志条目都会被设置为相同的应用ID。"
    )
    @PostMapping("/batch")
    public ResponseEntity<ApiResponse<Map<String, Object>>> createLogs(
            @ApiParam(value = "应用ID", required = true, example = "app123456")
            @RequestHeader("X-App-ID") String appId,
            @ApiParam(value = "API密钥（应用Token）", required = true, example = "abc123def456")
            @RequestHeader("X-Api-Key") String apiKey,
            @ApiParam(value = "日志条目列表", required = true)
            @Valid @RequestBody List<LogEntry> logEntries) {
        
        logger.info("外部应用批量创建日志: AppID={}, 数量={}", appId, logEntries.size());

        try {
            // 验证请求参数
            if (logEntries == null || logEntries.isEmpty()) {
                return ResponseEntity.ok(ApiResponse.error("日志条目列表不能为空"));
            }

            if (logEntries.size() > 1000) {
                return ResponseEntity.ok(ApiResponse.error("单次批量创建日志数量不能超过1000条"));
            }

            // 验证AppID和ApiKey
            Application application = validateAppCredentials(appId, apiKey);
            if (application == null) {
                logger.warn("应用认证失败: AppID={}", appId);
                return ResponseEntity.ok(ApiResponse.error("应用认证失败，请检查AppID和ApiKey"));
            }

            // 设置所有日志的应用ID和时间
            LocalDateTime now = LocalDateTime.now();
            logEntries.forEach(entry -> {
                entry.setApplicationId(appId);
                entry.setCreatedAt(now);
                entry.setUpdatedAt(now);
            });

            // 更新应用最后活跃时间
            application.setLastActiveTime(now);
            applicationRepository.save(application);

            // 批量保存日志
            List<LogEntry> savedLogs = logEntryService.saveAll(logEntries);
            
            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("totalCount", savedLogs.size());
            result.put("successCount", savedLogs.size());
            result.put("failedCount", 0);
            result.put("logs", savedLogs);
            
            logger.info("批量日志创建成功: AppID={}, 成功数量={}", appId, savedLogs.size());
            return ResponseEntity.ok(ApiResponse.success("批量创建日志成功", result));

        } catch (Exception e) {
            logger.error("批量创建日志失败: AppID=" + appId, e);
            return ResponseEntity.ok(ApiResponse.error("批量创建日志失败: " + e.getMessage()));
        }
    }

    /**
     * 验证应用凭据
     * 
     * @param appId 应用ID
     * @param apiKey API密钥
     * @return 验证成功返回应用对象，失败返回null
     */
    private Application validateAppCredentials(String appId, String apiKey) {
        try {
            // 检查参数
            if (appId == null || appId.trim().isEmpty()) {
                logger.warn("AppID为空");
                return null;
            }
            
            if (apiKey == null || apiKey.trim().isEmpty()) {
                logger.warn("ApiKey为空");
                return null;
            }

            // 查找应用
            Application application = applicationRepository.findById(appId).orElse(null);
            if (application == null) {
                logger.warn("应用不存在: AppID={}", appId);
                return null;
            }

            // 检查应用状态
            if (application.getStatus() != ApplicationStatus.ACTIVE) {
                logger.warn("应用状态不是活跃状态: AppID={}, Status={}", appId, application.getStatus());
                return null;
            }

            // 验证ApiKey（Token）
            if (!apiKey.equals(application.getToken())) {
                logger.warn("ApiKey验证失败: AppID={}", appId);
                return null;
            }

            logger.debug("应用认证成功: AppID={}, AppName={}", appId, application.getName());
            return application;

        } catch (Exception e) {
            logger.error("验证应用凭据时发生异常: AppID=" + appId, e);
            return null;
        }
    }
}
